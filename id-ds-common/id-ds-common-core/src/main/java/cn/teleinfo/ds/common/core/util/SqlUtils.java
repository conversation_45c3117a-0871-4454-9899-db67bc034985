package cn.teleinfo.ds.common.core.util;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.create.table.ColDataType;
import net.sf.jsqlparser.statement.create.table.ColumnDefinition;
import net.sf.jsqlparser.statement.create.table.CreateTable;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.insert.InsertModifierPriority;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectItem;
import net.sf.jsqlparser.util.TablesNamesFinder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class SqlUtils {

	/**
	 * 基础属性 转换sql
	 * 支持移除 SQL 中与 tid 相关的条件
	 * 支持的 tid 参数格式: :tid, #{tid}, ${tid}, `tid`
	 * 支持带反引号的表名和字段名
	 *
	 * @param sql SQL 语句
	 * @return 处理后的 SQL 语句
	 */
	public static String baseToSql(String sql) {
		if (sql == null || sql.isEmpty()) {
			return sql;
		}
		// 预处理SQL，将多个空格和换行替换为单个空格
		sql = sql.replaceAll("\\s+", " ");

		// 支持多种tid参数格式：:tid, #{tid}, ${tid}, `tid`
		String tidParam = "(:\\s*tid|:\\s*original_id|substring\\(\\s*:original_id\\s*,\\s*2\\s*\\))";
		// 先尝试处理所有条件都是tid的情况
		Pattern wherePattern = Pattern.compile(
				"(\\bwhere\\b.*?)($|\\bgroup\\s+by\\b|\\bhaving\\b|\\border\\s+by\\b|\\blimit\\b)",
				Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
		Matcher whereMatcher = wherePattern.matcher(sql);

		if (whereMatcher.find()) {
			String whereClause = whereMatcher.group(1);

			// 提取where子句中的条件部分
			Pattern conditionsPattern = Pattern.compile("\\bwhere\\b\\s+(.*?)$", Pattern.CASE_INSENSITIVE);
			Matcher conditionsMatcher = conditionsPattern.matcher(whereClause);

			if (conditionsMatcher.find()) {
				String conditions = conditionsMatcher.group(1).trim();

				// 使用(?i)使正则表达式对大小写不敏感
				String[] conditionArray = conditions.split("(?i)\\s+and\\s+");

				boolean allTidConditions = true;
				for (String condition : conditionArray) {
					// 检查是否是tid条件，支持带反引号的表名和字段名
					if (!Pattern.compile("(`[\\w\\.]+`|\\w+)(?:\\s*\\.\\s*(`[\\w]+`|\\w+))?\\s*=\\s*" + tidParam,
							Pattern.CASE_INSENSITIVE).matcher(condition).matches()) {
						allTidConditions = false;
						break;
					}
				}

				// 如果所有条件都是tid条件，则移除整个WHERE子句
				if (allTidConditions) {
					// 使用(?i)使正则表达式对大小写不敏感
					return sql.replaceAll("(?i)" + Pattern.quote(whereClause), " ").trim();
				}
			}
		}

		// 匹配 "and column=:tid" 或 "column=:tid and" 或单独的 "column=:tid"，考虑空格变化
		// 修改正则表达式以支持带反引号的表名和字段名
		Pattern andTidPattern = Pattern.compile("(?i)\\s+and\\s+(`[\\w\\.]+`|\\w+)(?:\\s*\\.\\s*(`[\\w]+`|\\w+))?\\s*=\\s*" + tidParam + "\\b");
		Pattern tidAndPattern = Pattern.compile("(?i)\\b(`[\\w\\.]+`|\\w+)(?:\\s*\\.\\s*(`[\\w]+`|\\w+))?\\s*=\\s*" + tidParam + "\\s+and\\s+");
		Pattern onlyTidPattern = Pattern
				.compile("(?i)\\bwhere\\s+(`[\\w\\.]+`|\\w+)(?:\\s*\\.\\s*(`[\\w]+`|\\w+))?\\s*=\\s*" + tidParam + "\\b");

		// 先处理 "and column=:tid" 的情况
		Matcher andTidMatcher = andTidPattern.matcher(sql);
		sql = andTidMatcher.replaceAll("");

		// 处理 "column=:tid and" 的情况
		Matcher tidAndMatcher = tidAndPattern.matcher(sql);
		sql = tidAndMatcher.replaceAll(" ");

		// 处理单独的 "where column=:tid" 的情况
		Matcher onlyTidMatcher = onlyTidPattern.matcher(sql);
		sql = onlyTidMatcher.replaceAll("where");

		// 处理可能出现的 "where " 后面没有条件的情况
		sql = sql.replaceAll("(?i)\\bwhere\\s+$", "");

		// 处理可能出现的 "where and" 的情况（当tid条件被移除后可能出现）
		sql = sql.replaceAll("(?i)\\bwhere\\s+and\\s+", "where ");

		return sql.trim();
	}

	/**
	 * 扩展属性 转换sql
	 * 处理包含 tid 参数的 SQL，会保留对应字段并添加到 SELECT 和 GROUP BY 子句中
	 * 支持的参数格式: :tid, #{tid}, ${tid}, `tid`
	 * 支持带反引号的表名和字段名
	 * 注意：tid 参数可以对应任何字段名，不仅限于 "tid" 或 "TID"
	 *
	 * @param sql SQL 语句
	 * @return 处理后的 SQL 语句
	 */
	public static String extendToSql(String sql) {
		if (sql == null || sql.isEmpty()) {
			return sql;
		}

		// 预处理SQL，将多个空格和换行替换为单个空格
		sql = sql.replaceAll("\\s+", " ");

		// 对于带有反引号的情况，先尝试直接匹配表名
		Pattern tablePattern = Pattern.compile("\\bfrom\\s+`([\\w]+)`", Pattern.CASE_INSENSITIVE);
		Matcher tableMatcher = tablePattern.matcher(sql);
		String tableName = null;
		if (tableMatcher.find()) {
			tableName = tableMatcher.group(1); // 获取表名（不带反引号）
		}

		// 支持多种tid参数格式：:tid
		// 注意：此处使用 (?i) 前缀进行不区分大小写匹配，但保留原始大小写
		String tidParam = "(:\\s*tid|:\\s*original_id|substring\\(\\s*:original_id\\s*,\\s*2\\s*\\))";

		// 查找WHERE子句中的所有条件字段，支持带反引号的表名和字段名
		// 格式为: WHERE/AND `表名`.`字段名` = :tid
		Pattern tidConditionPattern = Pattern.compile(
				"(?i)(\\s+and\\s+|\\bwhere\\s+)(`?([\\w\\.]+)`?)(?:\\s*\\.\\s*(`?([\\w]+)`?))?\\s*=\\s*" + tidParam,
				Pattern.CASE_INSENSITIVE);
		Matcher tidConditionMatcher = tidConditionPattern.matcher(sql);

		// 存储找到的所有字段
		Set<String> tidColumns = new HashSet<>();
		// 存储找到的完整字段名（表名.字段名）
		Set<String> fullTidFields = new HashSet<>();

		while (tidConditionMatcher.find()) {
			// 获取表名和字段名
			String tableOrColumn = tidConditionMatcher.group(3); // 去掉反引号后的表名或字段名
			String columnName = tidConditionMatcher.group(5);    // 去掉反引号后的字段名（如果存在）

			// 如果是 table.column 格式，则添加表名和完整字段名
			if (columnName != null && !columnName.isEmpty()) {
				tidColumns.add(tableOrColumn);
				// 保存完整字段名（表名.字段名），使用原始字段名
				fullTidFields.add(tableOrColumn + "." + columnName);
			} else {
				// 如果只有单独的字段名，则直接添加
				tidColumns.add(tableOrColumn);
				fullTidFields.add(tableOrColumn);
			}
		}

		// 如果通过正则表达式没有找到表名，但我们直接匹配到了表名，尝试使用额外的正则表达式查找字段名
		if (tableName != null) {
			// 尝试在 WHERE 子句中查找形如 `tableName`.`字段名` = :tid 的模式
			Pattern extraFieldPattern = Pattern.compile(
					"(?i)\\bwhere\\b.*?`" + Pattern.quote(tableName) + "`.`([\\w]+)`\\s*=\\s*" + tidParam,
					Pattern.CASE_INSENSITIVE);
			Matcher extraFieldMatcher = extraFieldPattern.matcher(sql);
			if (extraFieldMatcher.find()) {
				String fieldName = extraFieldMatcher.group(1); // 获取字段名
				tidColumns.add(tableName);
				fullTidFields.add(tableName + "." + fieldName);
			} else {
				// 如果找不到特定字段，默认添加表名
				tidColumns.add(tableName);
				fullTidFields.add(tableName + ".tid"); // 使用默认的 tid 字段名
			}
		}

		// 如果找到了字段，处理
		if (!tidColumns.isEmpty()) {
			// 移除WHERE子句中的所有TID条件
			sql = baseToSql(sql);

			// 在SELECT子句中添加字段
			Pattern selectPattern = Pattern.compile(
					"(?i)\\bselect\\b\\s+(.*?)\\s+\\bfrom\\b",
					Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
			Matcher selectMatcher = selectPattern.matcher(sql);

			if (selectMatcher.find()) {
				String selectClause = selectMatcher.group(1);
				StringBuilder newSelectAdditions = new StringBuilder();

				// 优先添加完整字段名（表名.字段名）
				for (String fullTidField : fullTidFields) {
					// 检查SELECT子句中是否已经包含了字段，大小写不敏感匹配，但保留原始大小写
					boolean fieldAlreadyInSelect = false;

					// 检查不带引号的情况
					Pattern noBacktickPattern = Pattern.compile(
							"(?i)\\b" + Pattern.quote(fullTidField) + "\\b",
							Pattern.CASE_INSENSITIVE);
					// 检查带引号的情况
					Pattern backtickPattern = Pattern.compile(
							"(?i)`" + Pattern.quote(fullTidField) + "`",
							Pattern.CASE_INSENSITIVE);
					// 检查 表达式.字段名 as 字段名 的情况
					String[] parts = fullTidField.split("\\.");
					if (parts.length == 2) {
						String tablePart = parts[0];
						String fieldPart = parts[1];
						Pattern asPattern = Pattern.compile(
								"(?i)`" + Pattern.quote(tablePart) + "`.`" + Pattern.quote(fieldPart) + "` as",
								Pattern.CASE_INSENSITIVE);
						fieldAlreadyInSelect = noBacktickPattern.matcher(selectClause).find() ||
								backtickPattern.matcher(selectClause).find() ||
								asPattern.matcher(selectClause).find();
					} else {
						fieldAlreadyInSelect = noBacktickPattern.matcher(selectClause).find() ||
								backtickPattern.matcher(selectClause).find();
					}

					if (!fieldAlreadyInSelect) {
						// 添加字段到新的SELECT部分的开头，保持原始大小写
						if (newSelectAdditions.length() == 0) {
							newSelectAdditions.append("`").append(fullTidField).append("`, ");
						} else {
							newSelectAdditions.append(", `").append(fullTidField).append("`");
						}
					}
				}

				// 如果有新的字段需要添加到SELECT子句
				if (newSelectAdditions.length() > 0) {
					// 将新字段添加到 SELECT 子句的开头
					sql = sql.replace("select ", "select " + newSelectAdditions.toString() + " ");
				}
			}

			// 检查是否有GROUP BY子句
			Pattern groupByPattern = Pattern.compile(
					"(?i)\\bgroup\\s+by\\b\\s+(.*?)(?=\\s+having\\b|\\s+order\\b|\\s+limit\\b|$)",
					Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
			Matcher groupByMatcher = groupByPattern.matcher(sql);

			if (groupByMatcher.find()) {
				String groupByClause = groupByMatcher.group(1).trim();
				StringBuilder newGroupByAdditions = new StringBuilder();

				// 对每个字段进行处理
				for (String fullTidField : fullTidFields) {
					// 检查GROUP BY子句中是否已经包含了字段
					boolean fieldAlreadyInGroupBy = Pattern.compile(
							"(?i)\\b" + Pattern.quote(fullTidField) + "\\b|`" + Pattern.quote(fullTidField) + "`",
							Pattern.CASE_INSENSITIVE).matcher(groupByClause).find();

					if (!fieldAlreadyInGroupBy) {
						// 添加字段到新的GROUP BY部分，保持原始大小写
						newGroupByAdditions.append(", `").append(fullTidField).append("`");
					}
				}

				// 如果有新的字段需要添加到GROUP BY子句
				if (newGroupByAdditions.length() > 0) {
					String newGroupByClause = groupByClause + newGroupByAdditions.toString();
					// 使用正则表达式替换整个GROUP BY子句
					sql = sql.replaceFirst("(?i)\\bgroup\\s+by\\b\\s+" + Pattern.quote(groupByClause),
							"group by " + newGroupByClause);
				}
			}
		}
		sql = sql.replaceAll("`", "");
		return sql.trim();
	}

	/**
	 * 生成兼容模式SQL语句
	 *
	 * @param sql 数据通道SQL语句，如SELECT concat('99.1000.0034/YMC05N182_',ilm_carriage_order.carrier_id) as carriage_order_tid FROM ilm_carriage_order WHERE carriage_order_id = substring(:original_id, 2);
	 * @param handle 对象标识编码，如99.1000.0034/YMC05N183
	 * @return 处理后的SQL语句，如SELECT concat('99.1000.0034/YMC05N182_',ilm_carriage_order.carrier_id) as carrier_tid, concat('99.1000.0034/YMC05N183_', carriage_order_id) carriage_order_id  FROM ilm_carriage_order WHERE carriage_order_id = substring(:original_id, 2);
	 */
	public static String extendToSql(String sql, String handle) {
		if (sql == null || sql.isEmpty() || handle == null || handle.isEmpty()) {
			return sql;
		}

		// 预处理SQL，将多个空格和换行替换为单个空格
		sql = sql.replaceAll("\\s+", " ").trim();

		// 先保存原始SQL用于查找字段信息
		String originalSql = sql;

		// 1. 在Select查询字段里增加新字段并修改现有字段的别名
		sql = addHandleField(originalSql, sql, handle);

		// 2. 去除where条件中包含:original_id的查询条件（根据需求可选）
		// sql = removeOriginalIdConditions(sql);

		return sql;
	}

	/**
	 * 去除where条件中包含:original_id的查询条件
	 */
	private static String removeOriginalIdConditions(String sql) {
		// 支持多种original_id参数格式：:original_id, substring(:original_id, 2)
		String originalIdParam = "(:\\s*original_id|substring\\(\\s*:original_id\\s*,\\s*\\d+\\s*\\))";

		// 匹配 "and column=:original_id" 的情况
		Pattern andOriginalIdPattern = Pattern.compile(
			"(?i)\\s+and\\s+[\\w\\.]+\\s*=\\s*" + originalIdParam + "\\b"
		);

		// 匹配 "column=:original_id and" 的情况
		Pattern originalIdAndPattern = Pattern.compile(
			"(?i)\\b[\\w\\.]+\\s*=\\s*" + originalIdParam + "\\s+and\\s+"
		);

		// 匹配单独的 "where column=:original_id" 的情况
		Pattern onlyOriginalIdPattern = Pattern.compile(
			"(?i)\\bwhere\\s+[\\w\\.]+\\s*=\\s*" + originalIdParam + "\\b"
		);

		// 先处理 "and column=:original_id" 的情况
		sql = andOriginalIdPattern.matcher(sql).replaceAll("");

		// 处理 "column=:original_id and" 的情况
		sql = originalIdAndPattern.matcher(sql).replaceAll("");

		// 处理单独的 "where column=:original_id" 的情况
		sql = onlyOriginalIdPattern.matcher(sql).replaceAll("WHERE");

		// 清理可能出现的多余空格和关键字
		sql = sql.replaceAll("(?i)\\bwhere\\s+$", "");
		sql = sql.replaceAll("(?i)\\bwhere\\s+and\\s+", "WHERE ");
		sql = sql.replaceAll("\\s+", " ");

		return sql.trim();
	}

	/**
	 * 在Select查询字段里增加新字段，并修改现有字段的别名
	 */
	private static String addHandleField(String originalSql, String currentSql, String handle) {
		// 查找WHERE子句中的original_id相关字段
		String originalIdParam = "(:\\s*original_id|substring\\(\\s*:original_id\\s*,\\s*\\d+\\s*\\))";
		Pattern fieldPattern = Pattern.compile(
			"(?i)(\\w+(?:\\.\\w+)?)\\s*=\\s*" + originalIdParam,
			Pattern.CASE_INSENSITIVE
		);

		Matcher fieldMatcher = fieldPattern.matcher(originalSql);
		String fieldName = null;

		if (fieldMatcher.find()) {
			String fullField = fieldMatcher.group(1);
			// 提取字段名（去掉表名前缀）
			if (fullField.contains(".")) {
				fieldName = fullField.substring(fullField.lastIndexOf('.') + 1);
			} else {
				fieldName = fullField;
			}
		}

		if (fieldName != null) {
			// 生成新字段的别名
			String aliasName = generateAliasName(fieldName);

			// 生成新字段表达式
			String newField = String.format("concat('%s_', %s) as %s", handle, fieldName, aliasName);

			// 在SELECT子句中添加新字段，并修改现有字段的别名
			Pattern selectPattern = Pattern.compile(
				"(?i)(\\bselect\\b\\s+)(.*?)(\\s+\\bfrom\\b)",
				Pattern.CASE_INSENSITIVE | Pattern.DOTALL
			);
			Matcher selectMatcher = selectPattern.matcher(currentSql);

			if (selectMatcher.find()) {
				String selectKeyword = selectMatcher.group(1);
				String selectClause = selectMatcher.group(2);
				String fromKeyword = selectMatcher.group(3);

				// 修改现有字段的别名（如果存在包含fieldName的别名）
				String modifiedSelectClause = modifyExistingAliases(selectClause, fieldName);

				// 在现有字段后添加新字段
				String newSelectClause = modifiedSelectClause + ", " + newField;
				currentSql = selectKeyword + newSelectClause + fromKeyword +
					currentSql.substring(selectMatcher.end());
			}
		}

		return currentSql;
	}

	/**
	 * 修改现有字段的别名
	 */
	private static String modifyExistingAliases(String selectClause, String fieldName) {
		// 查找现有字段表达式中的字段名，并基于这些字段名生成新的别名
		// 例如：concat('99.1000.0034/YMC05N182_',ilm_carriage_order.carrier_id) as carriage_order_tid
		// 应该基于 carrier_id 生成别名 carrier_tid

		// 查找所有的 as 别名
		Pattern aliasPattern = Pattern.compile(
			"(?i)(.*?)\\s+as\\s+(\\w+)",
			Pattern.CASE_INSENSITIVE
		);

		Matcher aliasMatcher = aliasPattern.matcher(selectClause);
		StringBuffer sb = new StringBuffer();

		while (aliasMatcher.find()) {
			String expression = aliasMatcher.group(1);
			String originalAlias = aliasMatcher.group(2);

			// 在表达式中查找表名.字段名的模式，但要排除字符串中的内容
			Pattern fieldInExprPattern = Pattern.compile("(\\w+)\\.(\\w+)(?![^']*')");
			Matcher fieldMatcher = fieldInExprPattern.matcher(expression);

			String newAlias = originalAlias; // 默认保持原别名
			String lastFieldName = null;

			// 找到最后一个匹配的字段名（排除字符串中的内容）
			while (fieldMatcher.find()) {
				lastFieldName = fieldMatcher.group(2);
			}

			if (lastFieldName != null) {
				newAlias = generateAliasName(lastFieldName);
			}

			aliasMatcher.appendReplacement(sb, expression + " as " + newAlias);
		}
		aliasMatcher.appendTail(sb);

		return sb.toString();
	}

	/**
	 * 生成别名规则：如果查询字段包含id则把id替换为tid，如果不包含id，则直接在查询条件后面拼接_tid
	 */
	private static String generateAliasName(String fieldName) {
		if (fieldName.toLowerCase().contains("id")) {
			// 替换最后一个id为tid（不区分大小写）
			return fieldName.replaceAll("(?i)id(?!.*id)", "tid");
		} else {
			// 直接在字段名后面拼接_tid
			return fieldName + "_tid";
		}
	}

	public static Set<String> findTableName(String sql) throws JSQLParserException {
		return TablesNamesFinder.findTables(sql);
	}

	public static boolean validateSQL(String sql) {
		try {
			Statement statement = CCJSqlParserUtil.parse(sql);
			return true;
		} catch (JSQLParserException e) {
			return false;
		}
	}

	public static String findStdTable(String tableName, String hiveSql) {
		if (StrUtil.isBlank(tableName) || StrUtil.isBlank(hiveSql)) {
			return null;
		}
		// 预处理：所有连续空白变成单一空格，去除首尾空格
		String sql = hiveSql.replaceAll("\\s+", " ").trim();

		// 更宽松的正则，匹配INSERT OVERWRITE语句，不要求完整的SQL
		Pattern pattern = Pattern.compile(
				".*?INSERT\\s+OVERWRITE\\s+TABLE\\s+([a-zA-Z0-9_]+(?:\\.[a-zA-Z0-9_]+)?)\\s+.*?FROM\\s+([a-zA-Z0-9_]+(?:\\.[a-zA-Z0-9_]+)?).*",
				Pattern.CASE_INSENSITIVE | Pattern.DOTALL
		);
		Matcher matcher = pattern.matcher(sql);
		if (matcher.find()) {
			String targetTable = matcher.group(1); // INSERT OVERWRITE TABLE 后面的表名
			String fromTable = matcher.group(2);   // FROM 后面的表名

			// 只比较表名部分（去掉schema）
			String fromTableName = fromTable.contains(".") ? fromTable.substring(fromTable.lastIndexOf('.') + 1) : fromTable;
			String inputTableName = tableName.contains(".") ? tableName.substring(tableName.lastIndexOf('.') + 1) : tableName;

			// 不区分大小写比较表名
			if (fromTableName.equalsIgnoreCase(inputTableName)) {
				// 返回 targetTable 的表名部分（去掉库名）
				return targetTable.contains(".") ? targetTable.substring(targetTable.lastIndexOf('.') + 1) : targetTable;
			}
		}
		return null;
	}

	public static String findStdTableColumns(String hiveSql) {
		if (StrUtil.isBlank(hiveSql)) {
			return "";
		}
		try {
			// 预处理SQL：
			// 1. 移除单行注释
			// 2. 移除多行注释
			// 3. 提取INSERT语句
			String processedSql = preprocessSql(hiveSql);
			if (StrUtil.isBlank(processedSql)) {
				return "";
			}

			Statement stmt = CCJSqlParserUtil.parse(processedSql);
			if (!(stmt instanceof Insert insert)) {
				return "";
			}
			// 兼容不同JSQLParser版本
			Select select = insert.getSelect();
			if (select == null || !(select.getSelectBody() instanceof PlainSelect plainSelect)) {
				return "";
			}
			List<SelectItem<?>> selectItems = plainSelect.getSelectItems();
			if (selectItems == null || selectItems.isEmpty()) {
				return "";
			}
			List<String> fields = new ArrayList<>();
			for (SelectItem<?> item : selectItems) {
				// select * 直接返回 *
				if ("*".equals(item.toString().trim())) {
					return "*";
				}
				String expr = item.getExpression().toString();
				// 去掉 as 后的别名
				String[] asSplit = expr.split("(?i)\\s+as\\s+");
				String fieldExpr = asSplit[0].trim();
				// 去掉表名前缀
				int dotIdx = fieldExpr.lastIndexOf('.');
				String fieldName = dotIdx >= 0 ? fieldExpr.substring(dotIdx + 1) : fieldExpr;
				// 去掉反引号、双引号等
				fieldName = fieldName.replaceAll("^[`'\"]|[`'\"]$", "");
				fields.add(fieldName);
			}
			return String.join("&", fields);
		} catch (Exception e) {
			log.debug("SQL解析异常，sql={}", hiveSql, e);
			return "";
		}
	}

	/**
	 * 预处理SQL，移除注释并提取INSERT语句
	 *
	 * @param sql 原始SQL
	 * @return 处理后的SQL
	 */
	private static String preprocessSql(String sql) {
		if (StrUtil.isBlank(sql)) {
			return sql;
		}

		// 1. 移除以--开头的单行注释
		String[] lines = sql.split("\n");
		StringBuilder cleanSql = new StringBuilder();
		for (String line : lines) {
			String trimmedLine = line.trim();
			if (!trimmedLine.startsWith("--")) {
				cleanSql.append(line).append("\n");
			}
		}
		sql = cleanSql.toString();

		// 2. 移除多行注释 /* ... */
		sql = sql.replaceAll("/\\*[\\s\\S]*?\\*/", "");

		// 3. 提取INSERT语句
		Pattern insertPattern = Pattern.compile(
				"(?i)INSERT\\s+(?:INTO|OVERWRITE)\\s+TABLE\\s+[^;]+?SELECT[^;]+?FROM[^;]+",
				Pattern.CASE_INSENSITIVE | Pattern.DOTALL
		);
		Matcher matcher = insertPattern.matcher(sql);
		if (matcher.find()) {
			return matcher.group().trim();
		}

		return "";
	}

	@Data
	public static class Table {
		private String tableName;
		List<String> columnList;
	}

	public static Table extractCreateSqlTable(String sql) {
		Statement createStmt = null;
		try {
			createStmt = CCJSqlParserUtil.parse(sql);
		} catch (JSQLParserException e) {
			log.error("sql 解析异常 sql={}", sql, e);
			return null;
		}

		var table = new Table();
		CreateTable createTable = (CreateTable) createStmt;
		String name = createTable.getTable().getName();
		List<String> list = createTable.getColumnDefinitions().stream()
				.map(ColumnDefinition::getColumnName).toList();
		table.setTableName(name);
		table.setColumnList(list);
		return table;
	}

	/**
	 * 提取查询 SQL 中的字段
	 *
	 * @param sql sql
	 * @return 查询字段
	 */
	public static List<String> extractSelectSqlField(String sql) {
		Select select = null;
		try {
			select = (Select) CCJSqlParserUtil.parse(sql);
		} catch (JSQLParserException e) {
			log.error("sql 解析异常 sql={}", sql, e);
			return null;
		}
		PlainSelect plainSelect = select.getPlainSelect();

		List<SelectItem<?>> selectItems = plainSelect.getSelectItems();
		if (selectItems == null || selectItems.isEmpty()) {
			return null;
		}

		List<String> fields = new ArrayList<>();

		for (SelectItem<?> selectItem : selectItems) {
			if (StrUtil.isEmpty(selectItem.getAliasName())) {
				String expression = selectItem.getExpression().toString();
				int i = expression.indexOf(".");
				if (i > 0) {
					expression = expression.substring(i + 1);
				}

				fields.add(expression);
			} else {
				fields.add(selectItem.getAliasName());
			}
		}

		return fields;
	}


	public static String genInsertSQLByCreateSQLAndSelectSQL(String createSQL, String selectSQL) {
		Statement createStmt = null;
		Statement selectStmt = null;
		try {
			createStmt = CCJSqlParserUtil.parse(createSQL);
			selectStmt = CCJSqlParserUtil.parse(selectSQL);
		} catch (JSQLParserException e) {
			log.error("SQL 解析失败 createSQL={} selectSQL={}", createSQL, selectSQL);
			return null;
		}

		if (!(selectStmt instanceof Select) || !(createStmt instanceof CreateTable)) {
			throw new IllegalArgumentException("Invalid SQL statement types");
		}

		// 1.解析CREATE TABLE语句获取目标列
		List<String> targetColumns = ((CreateTable) createStmt).getColumnDefinitions().stream()
				.map(ColumnDefinition::getColumnName).toList();

		Map<String, Expression> aliasMap = new HashMap<>();
		Map<String, Expression> columnMap = new HashMap<>();

		// 2. 构建SELECT项映射（别名->表达式）
		for (SelectItem<?> sei : ((Select) selectStmt).getPlainSelect().getSelectItems()) {
			Expression expr = sei.getExpression();

			if (sei.getAlias() != null) {
				String c = StrUtil.replace(sei.getAlias().getName().toUpperCase(),"`","");
				aliasMap.put(c, expr);
			} else if (expr instanceof Column) {
				columnMap.put(((Column) expr).getColumnName().toUpperCase(), expr);
			}
		}

		// 3. 按目标列顺序重组SELECT项
		List<SelectItem<?>> newSelectItems = new ArrayList<>();
		for (String col : targetColumns) {
			String c = StrUtil.replace(col.toUpperCase(), "`", "");
			Expression expr = aliasMap.get(c);
			if (expr == null) expr = columnMap.get(c);
			if (expr == null) throw new RuntimeException("Column not found: " + col);
			newSelectItems.add(SelectItem.from(expr));
		}

		// 4. 构建新SELECT（保留FROM/JOIN/WHERE）
		PlainSelect newSelect = new PlainSelect()
				.withSelectItems(newSelectItems)
				.withFromItem(((Select) selectStmt).getPlainSelect().getFromItem())
				.withJoins(((Select) selectStmt).getPlainSelect().getJoins())
				.withWhere(((Select) selectStmt).getPlainSelect().getWhere());

		// 5. 生成INSERT OVERWRITE语句
		Insert insert = new Insert()
				.withTable(((CreateTable) createStmt).getTable())
				.withSelect(newSelect.getPlainSelect());

		return insert.toString();
	}

	// hive to MYSQL 的建表语句
	public static String hiveToMysql(String createHiveSQL) {
		Statement createStmt = null;
		try {
			createStmt = CCJSqlParserUtil.parse(createHiveSQL);
		} catch (JSQLParserException e) {
			log.error("SQL 解析失败 createHiveSQL={}", createHiveSQL);
			return null;
		}


		if (createStmt instanceof CreateTable createTable) {
			// 1. 转换列数据类型
			for (ColumnDefinition col : createTable.getColumnDefinitions()) {
				String gaussType = mapDataType(col.getColDataType().getDataType());
				col.setColDataType(new ColDataType(gaussType));
			}

			// 2. 移除 Hive 特有属性
			createTable.getTableOptionsStrings().removeIf(opt ->
					opt.startsWith("ROW FORMAT") || opt.startsWith("STORED AS")
			);

			String gaussDDL = createTable.toString();
			// gaussDDL = gaussDDL.replace("COMMENT '", ""); // 移除列注释内联语法


			return gaussDDL;
		}

		return null;
	}

	private static String mapDataType(String hiveType) {
		return switch (hiveType.toLowerCase()) {
			case "string" -> "TEXT";
			case "bigint" -> "BIGINT";
			case "array<string>" -> "TEXT[]"; // GaussDB 数组类型
			case "map<string,string>" -> "JSONB"; // 用 JSONB 替代 Map
			case "timestamp" -> "TIMESTAMP";
			case "decimal" -> "NUMERIC(18,2)";
			default -> hiveType.toUpperCase(); // 其他类型尝试直接转换
		};
	}

	/**
	 * Main 方法用于测试功能
	 */
	public static void main(String[] args) {
		// 测试新的extendToSql方法
		String originalSql = "SELECT concat('99.1000.0034/YMC05N182_',ilm_carriage_order.carrier_id) as carriage_order_tid FROM ilm_carriage_order WHERE carriage_order_id = substring(:original_id, 2)";
		String handle = "99.1000.0034/YMC05N183";

		System.out.println("测试兼容模式SQL处理:");
		System.out.println("原始 SQL: " + originalSql);
		System.out.println("Handle: " + handle);
		String result = extendToSql(originalSql, handle);
		System.out.println("处理后的 SQL: " + result);
		System.out.println();

		// 测试更多场景
		testMoreCases();

		// 测试使用 AGBS 字段作为 tid 参数
		String sql1 = "select `train_lc_t_in_mas`.`BILL_NO` as `BILL_NO`, `train_lc_t_in_mas`.`PROCESS_CODE` as `PROCESS_CODE` " +
				"from `train_lc_t_in_mas` where `train_lc_t_in_mas`.`fsdaasdg` = substring(:original_id, 2)";

		System.out.println("测试自定义字段 AGBS 作为 tid 参数:");
		System.out.println("原始 SQL: " + sql1);
		String result1 = extendToSql(sql1);
		System.out.println("处理后的 SQL: " + result1);
	}

	private static void testMoreCases() {
		System.out.println("=== 测试更多场景 ===");

		// 测试1: 大写SQL关键字
		String sql1 = "SELECT name FROM users WHERE user_id = :original_id";
		String handle1 = "99.1000.0034/TEST";
		System.out.println("测试1 - 大写关键字:");
		System.out.println("原始: " + sql1);
		System.out.println("结果: " + extendToSql(sql1, handle1));
		System.out.println();

		// 测试2: 包含AND的复杂WHERE条件
		String sql2 = "select name from users where status = 'active' and user_id = substring(:original_id, 2)";
		String handle2 = "99.1000.0034/TEST2";
		System.out.println("测试2 - 复杂WHERE条件:");
		System.out.println("原始: " + sql2);
		System.out.println("结果: " + extendToSql(sql2, handle2));
		System.out.println();

		// 测试3: 不包含id的字段名
		String sql3 = "select name from users where username = :original_id";
		String handle3 = "99.1000.0034/TEST3";
		System.out.println("测试3 - 不包含id的字段名:");
		System.out.println("原始: " + sql3);
		System.out.println("结果: " + extendToSql(sql3, handle3));
		System.out.println();
	}

//		// 测试 TID 字段的情况
//		String tidSql = "select `train_lc_t_in_mas`.`BILL_NO` as `BILL_NO` " +
//				"from `train_lc_t_in_mas` where `train_lc_t_in_mas`.`TID` = :TID";
//
//		System.out.println("\n测试标准 TID 字段:");
//		System.out.println("原始 SQL: " + tidSql);
//		String tidResult = extendToSql(tidSql);
//		System.out.println("处理后的 SQL: " + tidResult);
//
//		// 测试 SELECT 子句中已包含字段的情况
//		String existingSql = "select `train_lc_t_in_mas`.`AGBS` as `AGBS`, `train_lc_t_in_mas`.`BILL_NO` as `BILL_NO` " +
//				"from `train_lc_t_in_mas` where `train_lc_t_in_mas`.`AGBS` = :tid";
//
//		System.out.println("\n测试 SELECT 子句中已包含字段的情况:");
//		System.out.println("原始 SQL: " + existingSql);
//		String existingResult = extendToSql(existingSql);
//		System.out.println("处理后的 SQL: " + existingResult);
//
//		// 测试不同参数格式 #{tid}
//		String mybatisSql = "select `train_lc_t_in_mas`.`BILL_NO` as `BILL_NO` " +
//				"from `train_lc_t_in_mas` where `train_lc_t_in_mas`.`FIELD1` = #{tid}";
//
//		System.out.println("\n测试 #{tid} 参数格式:");
//		System.out.println("原始 SQL: " + mybatisSql);
//		String mybatisResult = extendToSql(mybatisSql);
//		System.out.println("处理后的 SQL: " + mybatisResult);
//
//		// 测试不同参数格式 ${tid}
//		String expressionSql = "select `train_lc_t_in_mas`.`BILL_NO` as `BILL_NO` " +
//				"from `train_lc_t_in_mas` group by `BILL_NO`  where  `train_lc_t_in_mas`.`FIELD2` = ${tid}";
//
//		System.out.println("\n测试 ${tid} 参数格式:");
//		System.out.println("原始 SQL: " + expressionSql);
//		String expressionResult = extendToSql(expressionSql);
//		System.out.println("处理后的 SQL: " + expressionResult);
	}

}
