<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
		<groupId>cn.teleinfo</groupId>
        <artifactId>id-ds-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>id-ds-common-seata</artifactId>
    <packaging>jar</packaging>

    <description>pig 分布式事务处理模块</description>

    <dependencies>
        <!--核心依赖-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-core</artifactId>
        </dependency>
        <!-- seata-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
        </dependency>
    </dependencies>
</project>
