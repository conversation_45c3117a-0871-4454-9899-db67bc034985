package cn.teleinfo.ds.common.log;

import cn.teleinfo.ds.common.log.util.BusinessLoggerOption;
import cn.teleinfo.ds.upms.api.feign.RemoteLogService;
import cn.teleinfo.ds.common.log.aspect.SysLogAspect;
import cn.teleinfo.ds.common.log.config.PigLogProperties;
import cn.teleinfo.ds.common.log.event.SysLogListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 * @date 2019/2/1 日志自动配置
 */
@EnableAsync
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(PigLogProperties.class)
@ConditionalOnProperty(value = "security.log.enabled", matchIfMissing = true)
public class LogAutoConfiguration {

	@Bean
	public SysLogListener sysLogListener(PigLogProperties logProperties, RemoteLogService remoteLogService) {
		return new SysLogListener(remoteLogService, logProperties);
	}

	@Bean
	public SysLogAspect sysLogAspect() {
		return new SysLogAspect();
	}

	@Bean
	public BusinessLoggerOption businessLoggerOption() {
		return new BusinessLoggerOption();
	}
}
