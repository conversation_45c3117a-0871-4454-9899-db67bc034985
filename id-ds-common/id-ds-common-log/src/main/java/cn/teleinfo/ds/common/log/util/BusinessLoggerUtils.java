package cn.teleinfo.ds.common.log.util;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import ch.qos.logback.core.FileAppender;
import cn.hutool.core.date.DateUtil;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.util.Iterator;
import java.util.List;

/**
 * 业务日志
 */
public class BusinessLoggerUtils {

	// 全局日志序列化组件
	private static final PatternLayoutEncoder GLOBAL_ENCODER;

	static {
		LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
		GLOBAL_ENCODER = new PatternLayoutEncoder();
		GLOBAL_ENCODER.setContext(context);
		GLOBAL_ENCODER.setPattern("%d{yyyy-MM-dd HH:mm:ss} [%thread] %level %msg%n");
		GLOBAL_ENCODER.start();
	}

	private static final String CONSOLE_APPENDER_NAME = "console";


	/**
	 * 生成完整的日志路径
	 *
	 * @param logPath 日志前缀
	 * @param logName logName 名称，不包含后缀
	 * @return 完整的日志路径
	 */
	public static Path fullLogPath(String logPath, String logName, String... subPath) {
		Path basePath = Path.of(logPath, DateUtil.today(), logName);

		if (subPath != null) {
			for (String segment : subPath) {
				basePath = basePath.resolve(segment);
			}
		}

		return basePath.resolve(System.currentTimeMillis() + ".log");
	}

	public static Logger createLogger(Path fullLogPath, Level level) {
		LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();

		FileAppender<ILoggingEvent> fileAppender = new FileAppender<>();
		fileAppender.setContext(context);
		fileAppender.setName("DYNAMIC_FILE_" + System.currentTimeMillis());
		fileAppender.setFile(fullLogPath.toString());
		fileAppender.setEncoder(GLOBAL_ENCODER);
		fileAppender.start();


		Logger logger = context.getLogger("TASK_LOGGER_" + System.currentTimeMillis());
		logger.setAdditive(false);
		logger.addAppender(fileAppender);

		List<Logger> loggerList = context.getLoggerList();
		for (Logger l : loggerList) {
			Appender<ILoggingEvent> console = l.getAppender(CONSOLE_APPENDER_NAME);
			if (console != null) {
				logger.addAppender(console);
				break;
			}
		}

		logger.setLevel(level);

		return logger;
	}

	/**
	 * 销毁
	 * <p>
	 * logger 与 appender 解绑。
	 *
	 * @param logger logger
	 */
	public static void destroy(Logger logger) {

		Iterator<Appender<ILoggingEvent>> appenders = logger.iteratorForAppenders();
		while (appenders.hasNext()) {
			Appender<ILoggingEvent> appender = appenders.next();
			if (!CONSOLE_APPENDER_NAME.equals(appender.getName())) {
				appender.stop();
			}

			logger.detachAppender(appender);
		}
	}

}
