<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
		<groupId>cn.teleinfo</groupId>
        <artifactId>id-ds-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>id-ds-common-excel</artifactId>
    <packaging>jar</packaging>

    <description>excel 导入导出处理模块</description>

    <dependencies>
        <!--核心依赖,提供字典查询能力-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-core</artifactId>
        </dependency>
        <!-- excel 导入导出工具类：https://github.com/pig-mesh/excel-spring-boot-starter-->
        <dependency>
            <groupId>com.pig4cloud.excel</groupId>
            <artifactId>excel-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
