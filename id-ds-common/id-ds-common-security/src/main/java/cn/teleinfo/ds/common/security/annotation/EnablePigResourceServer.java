package cn.teleinfo.ds.common.security.annotation;

import cn.teleinfo.ds.common.security.component.PigResourceServerAutoConfiguration;
import cn.teleinfo.ds.common.security.component.PigResourceServerConfiguration;
import cn.teleinfo.ds.common.security.feign.PigFeignClientConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2022-06-04
 * <p>
 * 资源服务注解
 */
@Documented
@Inherited
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Import({ PigResourceServerAutoConfiguration.class, PigResourceServerConfiguration.class,
		PigFeignClientConfiguration.class })
public @interface EnablePigResourceServer {

}
