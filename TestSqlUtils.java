public class TestSqlUtils {
    public static void main(String[] args) {
        // 测试您提供的示例
        String originalSql = "SELECT concat('99.1000.0034/YMC05N182_',ilm_carriage_order.carrier_id) as carriage_order_tid FROM ilm_carriage_order WHERE carriage_order_id = substring(:original_id, 2)";
        String handle = "99.1000.0034/YMC05N183";
        
        System.out.println("=== 测试兼容模式SQL处理 ===");
        System.out.println("原始SQL:");
        System.out.println(originalSql);
        System.out.println();
        System.out.println("Handle: " + handle);
        System.out.println();
        
        // 模拟调用 SqlUtils.extendToSql(originalSql, handle)
        String expectedResult = "SELECT concat('99.1000.0034/YMC05N182_',ilm_carriage_order.carrier_id) as carrier_tid, concat('99.1000.0034/YMC05N183_', carriage_order_id) as carriage_order_tid FROM ilm_carriage_order WHERE carriage_order_id = substring(:original_id, 2)";
        
        System.out.println("期望结果:");
        System.out.println(expectedResult);
        System.out.println();
        
        System.out.println("=== 实现说明 ===");
        System.out.println("1. 保留原有字段，但修改其别名：");
        System.out.println("   - 原字段表达式中的 carrier_id 生成别名 carrier_tid");
        System.out.println();
        System.out.println("2. 添加新字段：");
        System.out.println("   - 基于WHERE条件中的 carriage_order_id 字段");
        System.out.println("   - 生成表达式: concat('99.1000.0034/YMC05N183_', carriage_order_id)");
        System.out.println("   - 生成别名: carriage_order_tid");
        System.out.println();
        System.out.println("3. 别名生成规则：");
        System.out.println("   - 如果字段名包含'id'，则替换最后一个'id'为'tid'");
        System.out.println("   - 如果字段名不包含'id'，则在字段名后拼接'_tid'");
        System.out.println();
        System.out.println("4. WHERE条件保留（根据您的期望结果）");
    }
}
