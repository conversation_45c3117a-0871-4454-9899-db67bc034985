<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.teleinfo</groupId>
        <artifactId>id-data-share</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>id-ds-gateway</artifactId>
    <packaging>jar</packaging>

    <description>id-data-share 服务网关，基于 spring cloud gateway</description>

    <dependencies>
        <!--gateway 网关依赖,内置webflux 依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- LB 扩展 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <!--caffeine 替换LB 默认缓存实现-->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <!-- 工具包依赖 -->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-core</artifactId>
        </dependency>
        <!--接口文档-->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
        </dependency>
        <!--引入Knife4j的官方ui包-->
        <dependency>
            <groupId>io.springboot</groupId>
            <artifactId>knife4j-openapi3-ui</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>com.google.cloud.tools</groupId>
				<artifactId>jib-maven-plugin</artifactId>
				<configuration>
					<from>
						<image>harbor.idx.space/ops/openjdk:17.0-jdk</image>
						<!-- 指定所需系统架构 -->
						<platforms>
							<platform>
								<architecture>amd64</architecture>
								<os>linux</os>
							</platform>
							<platform>
								<architecture>arm64</architecture>
								<os>linux</os>
							</platform>
						</platforms>
					</from>
					<to>
						<image>harbor.idx.space/zadig/id-ds-gateway:latest</image>
					</to>
					<!--容器相关的属性-->
					<container>
						<!--jvm内存参数-->
						<environment>
							<JAVA_OPTS>-Xms1g -Xmx2g -Xss1024k</JAVA_OPTS>
						</environment>
						<entrypoint>
							<arg>/bin/bash</arg>
							<arg>-c</arg>
							<!--suppress UnresolvedMavenProperty -->
							<arg>java ${JAVA_OPTS} -Duser.timezone=PRC -cp /app/resources/:/app/classes/:/app/libs/* cn.teleinfo.ds.gateway.DsGatewayApplication
							</arg>
						</entrypoint>
						<ports>
							<port>9999</port>
						</ports>
						<creationTime>USE_CURRENT_TIMESTAMP</creationTime>
					</container>
					<allowInsecureRegistries>true</allowInsecureRegistries>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
