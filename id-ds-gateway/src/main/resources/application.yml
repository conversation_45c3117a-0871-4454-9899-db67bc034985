server:
  port: 9999

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: nacos
      password: KGOy5JmDdh*TU&Noamjg
      discovery:
        server-addr: ${NACOS_HOST:*************}:${NACOS_PORT:30848}
        namespace: ${NACOS_NAMESPACE:id-ds-dev}
        watch:
          enabled: true
        watch-delay: 1000
        group: liheng
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml