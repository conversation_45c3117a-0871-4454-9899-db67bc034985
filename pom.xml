<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.teleinfo</groupId>
    <artifactId>id-data-share</artifactId>
    <name>${project.artifactId}</name>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <properties>
        <!-- 项目版本号 -->
        <revision>3.8.3</revision>
        <spring-boot.version>3.4.5</spring-boot.version>
        <spring-cloud.version>2024.0.1</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.3.2</spring-cloud-alibaba.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <spring-boot-admin.version>3.4.5</spring-boot-admin.version>
        <screw.version>0.0.3</screw.version>
        <captcha.version>2.2.5</captcha.version>
        <aws.version>1.12.675</aws.version>
        <velocity.version>2.4</velocity.version>
        <velocity.tool.version>3.1</velocity.tool.version>
        <configuration.version>1.10</configuration.version>
        <jasypt.version>3.0.5</jasypt.version>
        <jaxb.version>4.0.5</jaxb.version>
        <knife4j.version>3.0.5</knife4j.version>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <docker.plugin.version>0.45.1</docker.plugin.version>
        <docker.host>http://*************:2375</docker.host>
        <docker.registry>registry.cn-shanghai.aliyuncs.com</docker.registry>
        <docker.namespace>pig4cloud</docker.namespace>
        <docker.username>username</docker.username>
        <docker.password>password</docker.password>
        <git.commit.plugin>9.0.1</git.commit.plugin>
        <spring.checkstyle.plugin>0.0.43</spring.checkstyle.plugin>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <maven-compiler-plugin.version>3.14.0</maven-compiler-plugin.version>
    </properties>

    <!-- 以下依赖 全局所有的模块都会引入  -->
    <dependencies>
        <!--配置文件处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--配置文件加解密-->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>
        <!--监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--监控客户端-->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin.version}</version>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- JAVA 17 -->
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>${jaxb.version}</version>
        </dependency>
        <!--测试依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <modules>
        <module>id-ds-gateway</module>
        <module>id-ds-auth</module>
        <module>id-ds-service-api</module>
        <module>id-ds-service</module>
        <module>id-ds-common</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!--id-data-share 公共版本定义-->
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-bom</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring boot 依赖 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring cloud 依赖 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring cloud alibaba 依赖 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <finalName>${project.build.finalName}</finalName>
                        <layers>
                            <enabled>true</enabled>
                        </layers>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.plugin.version}</version>
                    <configuration>
                        <!-- Docker Remote Api-->
                        <dockerHost>${docker.host}</dockerHost>
                        <!-- Docker 镜像私服-->
                        <registry>${docker.registry}</registry>
                        <!-- 认证信息-->
                        <authConfig>
                            <push>
                                <username>${docker.username}</username>
                                <password>${docker.password}</password>
                            </push>
                        </authConfig>
                        <images>
                            <image>
                                <!-- 镜像名称： ************/library/pig-gateway:2.6.3-->
                                <name>${docker.registry}/${docker.namespace}/${project.name}:${project.version}</name>
                                <build>
                                    <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                                </build>
                            </image>
                        </images>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <target>${maven.compiler.target}</target>
                    <source>${maven.compiler.source}</source>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
            <!--打包jar 与git commit 关联插件-->
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
                <version>${git.commit.plugin}</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <phase>initialize</phase>
                    </execution>
                </executions>
                <configuration>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <!--因为项目定制了jackson的日期时间序列化/反序列化格式，因此这里要进行配置,不然通过management.info.git.mode=full进行完整git信息监控时会存在问题-->
                    <dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
                    <includeOnlyProperties>
                        <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
                        <includeOnlyProperty>^git.commit.(id|message|time).*$</includeOnlyProperty>
                    </includeOnlyProperties>
                </configuration>
            </plugin>
            <!--
            	代码格式插件，默认使用spring 规则，可运行命令进行项目格式化：./mvnw spring-javaformat:apply 或 mvn spring-javaformat:apply，可在IDEA中安装插件以下插件进行自动格式化：
            	https://repo1.maven.org/maven2/io/spring/javaformat/spring-javaformat-intellij-idea-plugin
            -->
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
                <version>${spring.checkstyle.plugin}</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <inherited>true</inherited>
                    </execution>
                </executions>
            </plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.0.0</version>
				<configuration>
					<argLine>-Djava.net.preferIPv4Stack=true</argLine>
				</configuration>
			</plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>cloud</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.username>nacos</nacos.username>
                <nacos.password>Teleinfo-88</nacos.password>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>
</project>
