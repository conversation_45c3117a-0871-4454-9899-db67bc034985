
## 系统说明

- 基于 Spring Cloud 、Spring Boot、 OAuth2 的 RBAC **企业快速开发平台**， 支持微服务架构
- 提供对 Spring Authorization Server 生产级实践，支持多种安全授权模式

### 核心依赖

| 依赖                          | 版本    |
|-----------------------------|-------|
| Spring Boot                 | 3.4   |
| Spring Cloud                | 2024  |
| Spring Cloud Alibaba        | 2023  |
| Spring Authorization Server | 1.4   |
| Spring Data JPA             | 3.5.0 |
| Nacos                       | 3.0.1 |
| Mysql                       | 2.7   |
| Redis                       | 4.0   |

### 模块说明

```lua
id-data-share -- 标识解析批量数据共享工具
├── id-ds-auth -- 授权服务提供[3000]
├── id-ds-common -- 系统公共模块
│    ├── id-ds-common-bom -- 全局依赖管理控制
│    ├── id-ds-common-core -- 公共工具类核心包
│    ├── id-ds-common-log -- 日志服务
│    ├── id-ds-common-oss -- 文件上传工具类
│    ├── id-ds-common-jpa -- jpa 扩展封装
│    ├── id-ds-common-seata -- 分布式事务
│    ├── id-ds-common-security -- 安全工具类
│    ├── id-ds-common-feign -- feign 扩展封装
│    └── id-ds-common-xss -- xss 安全封装
├── id-ds-gateway -- Spring Cloud Gateway网关[9999]
├── id-ds-service-api -- 业务模块API封装
│    ├── id-ds-upms-api -- 用户权限管理API模块
│    ├── id-ds-quartz-api -- 定时任务管理API模块
│    └── id-ds-business-api -- 核心业务API模块
└── id-ds-service -- 业务模块
     ├── id-ds-upms -- 用户权限管理模块[4000]
     ├── id-ds-quartz -- 定时任务管理模块[5000]
     └── id-ds-business -- 核心业务模块[6000]
```