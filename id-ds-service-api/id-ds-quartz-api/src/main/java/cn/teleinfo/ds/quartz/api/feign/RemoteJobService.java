package cn.teleinfo.ds.quartz.api.feign;

import cn.teleinfo.ds.quartz.api.dto.JobSaveReqDTO;
import cn.teleinfo.ds.common.core.constant.ServiceNameConstants;
import cn.teleinfo.ds.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "remoteJobService", value = ServiceNameConstants.QUARTZ_SERVICE)
public interface RemoteJobService {

	@PostMapping("/job/create")
	R<Long> createJob(JobSaveReqDTO job);

	@DeleteMapping("/job/delete")
	R<Boolean> deleteJob(@RequestParam("id") Long id);

	@PutMapping("/job/update-status")
	R<Boolean> updateJobStatus(@RequestParam("id") Long id, @RequestParam("status") Integer status);
}
