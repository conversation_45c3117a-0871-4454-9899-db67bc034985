package cn.teleinfo.ds.upms.api.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(description = "用户应用关系表")
@EqualsAndHashCode(callSuper = true)
public class SysUserApp extends Model<SysUserApp> {

	@Schema(description = "用户id")
	private Long userId;

	@Schema(description = "appid")
	private Long appId;
}
