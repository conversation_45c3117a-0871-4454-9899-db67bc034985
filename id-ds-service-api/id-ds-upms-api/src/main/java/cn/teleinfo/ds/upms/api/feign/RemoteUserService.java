package cn.teleinfo.ds.upms.api.feign;

import cn.teleinfo.ds.upms.api.dto.UserDTO;
import cn.teleinfo.ds.upms.api.dto.UserInfo;
import cn.teleinfo.ds.common.core.constant.ServiceNameConstants;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @date 2018/6/22
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteUserService {

	/**
	 * (未登录状态调用，需要加 @NoToken) 通过用户名查询用户、角色信息
	 * @param user 用户查询对象
	 * @return R
	 */
	@NoToken
	@GetMapping("/user/info/query")
	R<UserInfo> info(@SpringQueryMap UserDTO user);

}
