package cn.teleinfo.ds.upms.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Data
@Schema(description = "用户和社会化用户关联表")
@TableName("t_user_social_user")
public class UserSocialUser implements Serializable {

	/**
	 * 主键ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	@Schema(description = "主键id")
	private Long id;
	private Long userId;
	private String socialUserId;
	private Integer isDeleted;


}
