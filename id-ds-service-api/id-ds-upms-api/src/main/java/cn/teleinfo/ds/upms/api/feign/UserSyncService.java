package cn.teleinfo.ds.upms.api.feign;

import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import static cn.teleinfo.ds.common.core.constant.ServiceNameConstants.UPMS_SERVICE;

@FeignClient(name = UPMS_SERVICE)
public interface UserSyncService {

	@NoToken
	@GetMapping(value = "/user/sync")
	R sync();

}
