package cn.teleinfo.ds.upms.api.feign;

import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

import static cn.teleinfo.ds.common.core.constant.ServiceNameConstants.UPMS_SERVICE;

@FeignClient(contextId = "roleService",name = UPMS_SERVICE)
public interface RoleService {

	@NoToken
	@GetMapping(value = "/role/getRoleListByUserId/{userId}")
	R<List<RoleCommonVO>> getRoleListByUserId(@PathVariable("userId")Long userId);
}
