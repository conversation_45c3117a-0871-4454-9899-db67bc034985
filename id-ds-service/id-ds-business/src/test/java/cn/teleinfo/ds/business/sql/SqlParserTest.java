package cn.teleinfo.ds.business.sql;

import cn.teleinfo.ds.common.core.util.SqlUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import org.junit.jupiter.api.Test;

import java.util.List;

@Slf4j
public class SqlParserTest {

	// 提取 SQL 信息
	@Test
	void info() throws JSQLParserException {
		var sql = """
				select
						ai.id,
						concat('99.1000.1/YMZ12N98_', ai.id)  as APP_TID,
						concat('99.1000.1/YMZ12N73_', ds.id) as DATA_SERVICE_TID
				from yc_data_service ds inner join yc_app_info ai on ds.app_id = ai.id
				where ds.`is_deleted`= 0
						and ai.is_deleted = 0
				""";

		List<String> fields = SqlUtils.extractSelectSqlField(sql);
		if(fields != null){
			for (String string : fields) {
				log.info("field={}",string);
			}
		}

	}

}
