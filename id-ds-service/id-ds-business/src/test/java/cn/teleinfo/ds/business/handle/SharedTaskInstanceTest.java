package cn.teleinfo.ds.business.handle;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
public class SharedTaskInstanceTest {

	private static final ExecutorService EXECUTOR = Executors.newFixedThreadPool(
			Math.max(4, Runtime.getRuntime().availableProcessors() * 2)
	);

	@Data
	public static class SharedTaskInstance {
		private List<SharedSubTaskInstance> subs; // 子任务

		public void exec() {
			long s = System.currentTimeMillis();

			// 子任务执行结束会返回执行结果
			List<CompletableFuture<Result>> futures = subs.stream().map(sub -> CompletableFuture.supplyAsync(sub::exec, EXECUTOR)).toList();


			CompletableFuture<Void> future = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

			CompletableFuture<List<Result>> resultsFuture = future.thenApply(v ->
					futures.stream().map(CompletableFuture::join).toList());

			try {
				List<Result> results = resultsFuture.get();

				results.forEach(r -> {
					log.info("{} 执行时间 {}秒", r.getHandle(), r.getTime());
				});

				long e = System.currentTimeMillis();
				log.info("所有任务执行结束 {}秒", (e - s) / 1000);

			} catch (InterruptedException | ExecutionException e) {
				throw new RuntimeException(e);
			}
		}

	}

	@Data
	public static class SharedSubTaskInstance {
		private String handle;

		public Result exec() {
			int i = RandomUtil.randomInt(10);

			long s = System.currentTimeMillis() / 1000;
			long e;
			log.info("{} 任务执行开始 {}", handle, s);

			do {
				e = System.currentTimeMillis() / 1000;
			}
			while (e - s <= i);

			log.info("{} 任务执行结束 {}", handle, e - s);

			return new Result(handle, i);
		}
	}

	@Data
	@AllArgsConstructor
	public static class Result {
		private String handle;
		private Integer time;
	}

	@Test
	void execTest() {
		SharedTaskInstance instance = new SharedTaskInstance();
		instance.setSubs(new ArrayList<>());

		int i = RandomUtil.randomInt(1, 10);
		log.info("随机 {} 任务", i);

		for (int j = 0; j < i; j++) {
			var sub = new SharedSubTaskInstance();
			String h = RandomUtil.randomString(8);

			sub.setHandle(j + "-" + h);
			instance.getSubs().add(sub);
		}

		instance.exec();
	}

}
