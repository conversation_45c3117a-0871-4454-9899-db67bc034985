package cn.teleinfo.ds.business.huaweicloud;

import cn.hutool.json.JSONUtil;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.http.HttpConfig;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAuthProjectsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAuthProjectsResponse;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListServicesRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListServicesResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.List;

@Slf4j
public class HcsIamTest {

    /**
     * AK
     */
    // static String ak = "Y20RVDRARXXKDJNSYVRB";
	static String ak = "FDHNGVD9RN4URFIUJRLK";
    /**
     * SK
     */
    //static String sk = "db3d1XxtHtwxXeAjpfapSu88Z26kdzjcNjtEZrvn";
    static String sk = "WORFRN99uA9ayBU9WHzW286qlCSohIgCDSHDYS7v";

    /**
     * 项目ID
     */
    static String projectId = "a24ee94724ea4bfa85f4c7938dc2e0a1";

    /**
     * 认证信息
     */
    static ICredential auth;

    /**
     * HTTP配置
     */
    static HttpConfig httpConfig;

    /**
     * 华为云HCS终端节点
     */
    static List<String> endpoints = List.of("https://iam-apigateway-proxy.cn-sdzy-1.sdgy.yc");

    /**
     * CDM客户端
     */
    static IamClient client;

    /**
     * 初始化参数
     */
    @BeforeAll
    static void beforeAll() {
        // 认证凭证
        auth = new BasicCredentials()
				//.withProjectId(projectId)
				.withAk(ak).withSk(sk);
        // 禁用SSL校验
        httpConfig = HttpConfig.getDefaultHttpConfig();
        httpConfig.setIgnoreSSLVerification(true);
        // 构建IAM客户端
        client = IamClient.newBuilder()
                .withHttpConfig(httpConfig)
                .withCredential(auth)
                .withEndpoints(endpoints)
                .build();
        log.info("CDM客户端创建成功");
    }

    /**
     * 查询IAM用户可以访问的项目列表
     */
    @Test
    void keystoneListAuthProjects() {
        KeystoneListAuthProjectsRequest request = new KeystoneListAuthProjectsRequest();
        KeystoneListAuthProjectsResponse response = client.keystoneListAuthProjects(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

}
