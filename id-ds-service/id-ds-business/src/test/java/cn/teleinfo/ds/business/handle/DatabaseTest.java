package cn.teleinfo.ds.business.handle;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.interfaces.dto.request.GraphRequest;
import cn.teleinfo.ds.common.core.util.SqlUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.Test;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;

import java.nio.charset.Charset;

public class DatabaseTest {
	@Test
	void createTableTest() {
		//SharedTask sharedTask = initShareTask();

		var sql = """
				CREATE TABLE IF NOT EXISTS TSTD_YMZ12N98_BASIC (
				    "MZ12_BSJX_APP_NAME" VARCHAR(255) ,
				    "MZ12_BSJX_APP_HANDLE_CODE" VARCHAR(255) ,
				    "APP_TYPE" VARCHAR(255) ,
				    "SYS_VERSION" VARCHAR(255)
				) COMMENT '标识解析应用信息基础属性表';
				""";

		//TargetSourceItems targetSourceItems = sharedTask.getTargetSource().getTargetSourceItems();
		var databaseUrl = "10.14.145.200";
		var port = 8000;
		var databaseName = "bsjx";
		var schema = "bsjx";
		var username = "bsjx_user";
		var passwd = "Sdzy@bsjx2025";


		String jdbcUrl = StrUtil.format("jdbc:postgresql://{}:{}/{}?sslmode=disable&currentSchema={}", databaseUrl, port, databaseName,schema);

		try (HikariDataSource dataSource = new HikariDataSource()) {
			dataSource.setDriverClassName("org.postgresql.Driver");
			dataSource.setJdbcUrl(jdbcUrl);
			dataSource.setUsername(username);
			dataSource.setPassword(passwd);

			JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

			jdbcTemplate.execute(sql);
		} catch (DataAccessException e) {
			throw new RuntimeException(e);
		}

	}

	@Test
	void t(){
		var sql = """
				CREATE TABLE IF NOT EXISTS TSTD_YMZ12N98_BASIC (
				    MZ12_BSJX_APP_NAME STRING COMMENT 'MZ12_BSJX_APP_NAME',
				    MZ12_BSJX_APP_HANDLE_CODE STRING COMMENT 'MZ12_BSJX_APP_HANDLE_CODE',
				    APP_TYPE STRING COMMENT 'APP_TYPE',
				    SYS_VERSION STRING COMMENT 'SYS_VERSION'
				) COMMENT '标识解析应用信息基础属性表';""";

		System.out.println(SqlUtils.hiveToMysql(sql));
	}

	@Test
	void json(){
		String s = FileUtil.readString("/Users/<USER>/go/src/xin/views/1.json", Charset.defaultCharset());
		GraphRequest request = JSONUtil.toBean(s, GraphRequest.class);
		System.out.println(request);

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

			try {
				String string = objectMapper.writeValueAsString(request);
				System.out.println(string);
			} catch (JsonProcessingException e) {
				throw new RuntimeException(e);
			}

	}

}
