//package cn.teleinfo.ds.business.channel;
//
//import ch.qos.logback.classic.Level;
//import ch.qos.logback.classic.Logger;
//import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
//import cn.teleinfo.ds.business.domain.model.entity.*;
//import cn.teleinfo.ds.business.domain.repository.*;
//import cn.teleinfo.ds.business.domain.service.*;
//import cn.teleinfo.ds.business.domain.util.SqlGenerator;
//import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ListScriptResultsResponseDTO;
//import cn.teleinfo.ds.common.core.util.SqlUtils;
//import cn.teleinfo.ds.common.log.util.BusinessLoggerOption;
//import cn.teleinfo.ds.common.log.util.BusinessLoggerUtils;
//import com.huaweicloud.sdk.cdm.v1.model.Configs;
//import com.huaweicloud.sdk.cdm.v1.model.Input;
//import com.huaweicloud.sdk.cdm.v1.model.Job;
//import com.huaweicloud.sdk.dataartsstudio.v1.model.ColumnsList;
//import com.huaweicloud.sdk.dataartsstudio.v1.model.TablesList;
//import net.sf.jsqlparser.JSQLParserException;
//import org.apache.commons.lang3.StringUtils;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.nio.file.Path;
//import java.util.*;
//
//
//@SpringBootTest
//public class ChannelTest {
//
//
//    @Autowired
//    private ShareDataSourcesRepository shareDataSourcesRepository;
//
//    @Autowired
//    private PlatformConnectionDomainService platformConnectionDomainService;
//
//    @Autowired
//    private ShareChannelRepository shareChannelRepository;
//    @Autowired
//    private HcsDomainService hcsDomainService;
//
//    @Autowired
//    private BusinessLoggerOption businessLoggerOption;
//
//
//    @Test
//    public void channelTest() {
//        List<ShareChannel> channels = new ArrayList<>();
//        ShareChannel shareChannel1 = new ShareChannel();
//        shareChannel1.setDefaultSql("select * from  yc_handle  where 1=1");
//        shareChannel1.setAppHandleCode("99.1000.1/hoj3uw");
//        shareChannel1.setId(1L);
//        shareChannel1.setShareChannelId(99999L);
//        channels.add(shareChannel1);
//        for (ShareChannel shareChannel : channels) {
//            Path fullLogPath = BusinessLoggerUtils.fullLogPath(businessLoggerOption.getLogPath(), shareChannel.getShareChannelId().toString());
//            Logger logger = BusinessLoggerUtils.createLogger(fullLogPath, Level.DEBUG);
//            String appHandleCode = shareChannel.getAppHandleCode();
//            String editSql = shareChannel.getCustomSql();
//            String autoSql = shareChannel.getDefaultSql();
//            String sql = StringUtils.isEmpty(editSql) ? autoSql : editSql;
//            ShareChannelDetectLog shareChannelDetectLog = new ShareChannelDetectLog();
//            shareChannelDetectLog.setId(1L);
//            shareChannelDetectLog.setLogPath(fullLogPath.toString());
//            shareChannelDetectLog.setShareChannelId(shareChannel.getShareChannelId());
//            shareChannelDetectLog.setMainVersion(shareChannel.getMainVersion());
//            shareChannelDetectLog.setMinorVersion(shareChannel.getMinorVersion());
//            shareChannelDetectLog.setConnectTest(1);
//            shareChannelDetectLog.setExecTest(0);
//            shareChannelRepository.saveShareChannelLog(shareChannelDetectLog);
//            Set<String> tables;
//            try {
//                tables = SqlUtils.findTableName(sql);
//            } catch (JSQLParserException e) {
//                throw new RuntimeException(e);
//            }
//            // 业务>帖源层
//            Map<String, String> stg = new HashMap<>();
//            // 帖源>规范
//            Map<String, String> std = new HashMap<>();
//            // 业务>帖源层，业务表字段
//            Map<String, String> stgFromColumnList = new HashMap<>();
//            // 业务>帖源层，帖源表字段
//            Map<String, String> stgToColumnList = new HashMap<>();
//            // 帖源>规范 帖源表字段
//            Map<String, String> stdFromColumnList = new HashMap<>();
//            // 帖源>规范 规范表字段
//            Map<String, String> stdToColumnList = new HashMap<>();
//            // 贴源层 表字段类型
//            Map<String, List<ColumnsList>> stgColumnsType = new HashMap<>();
//            List<String> scriptNames = new ArrayList<>();
//            Set<String> tycTable = new HashSet<>(tables);
//            class BreakException extends RuntimeException {}
//            // 保留测试用数据
//            String projectId = "a24ee94724ea4bfa85f4c7938dc2e0a1";
//            String clusterId = "eb76c76d-beeb-4eef-af87-ce645e6fc540";
//            String connectionId = "49db0aac863745878fbeb85c2e36d170";
//            String connectionName = "bsjx-hive-test";
//            String workspace = "dd8652d44ba140799357ff8e2b790286";
//            String stgDatabaseName = "bsjx_stg_test";
//            String stdDatabaseName = "bsjx_std_test";
//            ShareDataSourcesDomainEntity dataSourcesDomainEntity = shareDataSourcesRepository.findByAppHandleCode(appHandleCode);
//            Integer platformType = dataSourcesDomainEntity.getPlatformType();
//			PlatformConnection connection = platformConnectionDomainService.findByPlatformType(platformType);
//            try {
//                // 1. 获取作业
//                List<Job> jobs = hcsDomainService.findJobs(connection, projectId, clusterId);
//                // 2. 解析贴源层表
//                try {
//                    for (Job job : jobs) {
//                        for (var config : job.getFromConfigValues().getConfigs()) {
//                            for (var input : config.getInputs()) {
//                                if ("fromJobConfig.tableName".equals(input.getName())) {
//                                    for (String table : new HashSet<>(tycTable)) {
//                                        if (input.getValue().toString().equals(table)) {
//                                            for (Configs configs : job.getFromConfigValues().getConfigs()) {
//                                                for (Input configsInput : configs.getInputs()) {
//                                                    if ("fromJobConfig.columnList".equals(configsInput.getName())) {
//                                                        String columnListValue = configsInput.getValue().toString();
//                                                        stgFromColumnList.put(table, columnListValue);
//                                                        break;
//                                                    }
//                                                }
//                                            }
//                                            for (var config1 : job.getToConfigValues().getConfigs()) {
//                                                for (var input1 : config1.getInputs()) {
//                                                    if ("toJobConfig.table".equals(input1.getName())) {
//                                                        stg.put(table, input1.getValue().toString());
//                                                        tycTable.remove(table);
//                                                        String columnListValue = null;
//                                                        for (var in1 : config1.getInputs()) {
//                                                            if ("toJobConfig.columnList".equals(in1.getName())) {
//                                                                columnListValue = in1.getValue().toString();
//                                                                stgToColumnList.put(table, columnListValue);
//                                                                break;
//                                                            }
//                                                        }
//                                                        if (tycTable.isEmpty()) {
//                                                            throw new BreakException();
//                                                        }
//                                                        break;
//                                                    }
//                                                }
//                                            }
//                                            break;
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//                } catch (BreakException ignore) {}
//                if (stg.size() != tables.size()) {
//                    logger.info("贴源层表不存在");
//                    shareChannelDetectLog.setExecTest(2);
//                }
//                Set<String> gfcTable = new HashSet<>(stg.values());
//                logger.info("获取规范层表信息");
//                try {
//                    for (Job job : jobs) {
//                        for (var config : job.getFromConfigValues().getConfigs()) {
//                            for (var input : config.getInputs()) {
//                                if ("fromJobConfig.table".equals(input.getName())) {
//                                    for (String table : new HashSet<>(gfcTable)) {
//                                        if (input.getValue().toString().equals(table)) {
//                                            for (Configs configs : job.getFromConfigValues().getConfigs()) {
//                                                for (Input configsInput : configs.getInputs()) {
//                                                    if ("fromJobConfig.columnList".equals(configsInput.getName())) {
//                                                        String columnListValue = configsInput.getValue().toString();
//                                                        stdFromColumnList.put(table, columnListValue);
//                                                        break;
//                                                    }
//                                                }
//                                            }
//                                            for (var config1 : job.getToConfigValues().getConfigs()) {
//                                                for (var input1 : config1.getInputs()) {
//                                                    if ("toJobConfig.table".equals(input1.getName())) {
//                                                        std.put(table, input1.getValue().toString());
//                                                        gfcTable.remove(table);
//                                                        String columnListValue;
//                                                        for (var in1 : config1.getInputs()) {
//                                                            if ("toJobConfig.columnList".equals(in1.getName())) {
//                                                                columnListValue = in1.getValue().toString();
//                                                                stdToColumnList.put(input1.getValue().toString(), columnListValue);
//                                                                break;
//                                                            }
//                                                        }
//                                                        if (gfcTable.isEmpty()) {
//                                                            throw new BreakException();
//                                                        }
//                                                        break;
//                                                    }
//                                                }
//                                            }
//                                            break;
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//                } catch (BreakException ignore) {}
//                // 分页匹配未能直接通过作业找到的规范层表
//                List<String> scriptStgTables = new ArrayList<>();
//                Set<String> scriptStdTables = new HashSet<>();
//                if (std.size() != stg.size()) {
//                    for (String value : stg.values()) {
//                        String stdTable = std.get(value);
//                        if (stdTable == null) {
//                            scriptStgTables.add(value);
//                        }
//                    }
//                    int limit = 20;
//                    int offset = 0;
//                    Set<String> unmatchedTables = new HashSet<>(scriptStgTables);
//                    boolean allMatched = false;
//                    while (!unmatchedTables.isEmpty()) {
//                        var scriptList = hcsDomainService.scriptList(connection, projectId, workspace, limit, offset);
//                        var scriptInfo1 = scriptList.getScriptInfo();
//                        if (scriptInfo1 == null || scriptInfo1.isEmpty()) {
//                            break;
//                        }
//                        for (var scriptInfo : scriptInfo1) {
//                            String hiveSql = scriptInfo.getContent();
//                            Iterator<String> it = unmatchedTables.iterator();
//                            while (it.hasNext()) {
//                                String tableName = it.next();
//                                String table = SqlUtils.findStdTable(tableName, hiveSql);
//                                if (table != null) {
//                                    std.put(tableName, table);
//                                    String columns = SqlUtils.findStdTableColumns(hiveSql);
//                                    stdFromColumnList.put(tableName, columns);
//                                    scriptStdTables.add(table);
//                                    it.remove();
//                                }
//                            }
//                            if (unmatchedTables.isEmpty()) {
//                                allMatched = true;
//                                break;
//                            }
//                        }
//                        if (allMatched) {
//                            break;
//                        }
//                        offset += limit;
//                        if (scriptList.getTotal() != null && offset >= scriptList.getTotal()) {
//                            break;
//                        }
//                    }
//                    // 获取规范层表结构
//                    List<TablesList> stdTableList = hcsDomainService.listTables(connection, projectId, connectionId, stdDatabaseName, "", workspace);
//                    for (TablesList tablesList : stdTableList) {
//                        String tableName = tablesList.getTableName();
//                        Iterator<String> it = scriptStdTables.iterator();
//                        while (it.hasNext()) {
//                            String value = it.next();
//                            if (value.equals(tableName)) {
//                                String tableId = tablesList.getTableId();
//                                List<ColumnsList> columnsLists = hcsDomainService.listTableColumns(connection, projectId, connectionId, workspace, tableId);
//                                // 这里假设SqlGenerator.getStdColumns静态方法可用
//                                String columns = SqlGenerator.getStdColumns(columnsLists);
//                                stdToColumnList.put(tableName, columns);
//                                it.remove();
//                                break;
//                            }
//                        }
//                        if (scriptStdTables.isEmpty()) {
//                            break;
//                        }
//                    }
//                }
//                // 3. 获取贴源层表结构
//                List<TablesList> stgtableList = hcsDomainService.listTables(connection, projectId, connectionId, stgDatabaseName, "", workspace);
//                Set<String> stgToMatch = new HashSet<>(stg.values());
//                for (TablesList tablesList : stgtableList) {
//                    String tableName = tablesList.getTableName();
//                    Iterator<String> it = stgToMatch.iterator();
//                    while (it.hasNext()) {
//                        String value = it.next();
//                        if (value.equals(tableName)) {
//                            String tableId = tablesList.getTableId();
//                            List<ColumnsList> columnsLists = hcsDomainService.listTableColumns(connection, projectId, connectionId, workspace, tableId);
//                            stgColumnsType.put(value, columnsLists);
//                            it.remove();
//                            break;
//                        }
//                    }
//                    if (stgToMatch.isEmpty()) {
//                        break;
//                    }
//                }
//                // 5. 合并所有建表SQL
//                StringBuilder ddlBuilder = new StringBuilder();
//                for (String table : tables) {
//                    String stgTable = stg.get(table);
//                    String createTableSql = SqlGenerator.generateCreateTableSql(table, stgColumnsType.get(stgTable));
//                    ddlBuilder.append(createTableSql).append(";");
//                }
//                String allDdlSql = ddlBuilder.toString();
//                String ddlScriptName = "DDL-" + shareChannel.getShareChannelId();
//                scriptNames.add(ddlScriptName);
//                hcsDomainService.createScript(connection, projectId, ddlScriptName, allDdlSql, workspace, stdDatabaseName, connectionName);
//                String ddlInstanceId = hcsDomainService.executeScript(connection, projectId, ddlScriptName, workspace);
//                // 6. 轮询DDL脚本执行状态
//                while (true) {
//                    Thread.sleep(1000);
//                    ListScriptResultsResponseDTO result = hcsDomainService.listScriptResults(connection, projectId, ddlScriptName, workspace, ddlInstanceId);
//                    String status = result.getStatus();
//                    if (status.equals("FINISHED")) {
//                        logger.info("所有规范层表创建成功");
//                        break;
//                    } else if (status.equals("FAILED")) {
//                        logger.info("DDL脚本执行失败:{}", result.getMessage());
//                        shareChannelDetectLog.setExecTest(2);
//                        shareChannelRepository.updateDetectStatus(shareChannelDetectLog);
//                        BusinessLoggerUtils.destroy(logger);
//                        throw new RuntimeException();
//                    }
//                }
//                // 7. 合并所有插入SQL
//                StringBuilder insertBuilder = new StringBuilder();
//                for (String table : tables) {
//                    String stgTable = stg.get(table);
//                    String stdTable = std.get(stgTable);
//                    String insertSql = SqlGenerator.generateInsertSql(stdDatabaseName, stdTable, table, stgFromColumnList.get(table), stgToColumnList.get(table), stdFromColumnList.get(stgTable), stdToColumnList.get(stdTable));
//                    insertBuilder.append(insertSql).append(";");
//                }
//				insertBuilder.append(sql).append(";");
//                String allInsertSql = insertBuilder.toString();
//                String insertScriptName = "INSERT-" + shareChannel.getShareChannelId();
//                scriptNames.add(insertScriptName);
//                hcsDomainService.createScript(connection, projectId, insertScriptName, allInsertSql, workspace, stdDatabaseName, connectionName);
//                String insertInstanceId = hcsDomainService.executeScript(connection, projectId, insertScriptName, workspace);
//                // 8. 轮询INSERT脚本执行状态
//                while (true) {
//                    Thread.sleep(1000);
//                    ListScriptResultsResponseDTO result = hcsDomainService.listScriptResults(connection, projectId, insertScriptName, workspace, insertInstanceId);
//                    String status = result.getStatus();
//                    if (status.equals("FINISHED")) {
//						shareChannelDetectLog.setExecTest(1);
//                        logger.info("所有规范层数据插入成功");
//                        break;
//                    } else if (status.equals("FAILED")) {
//                        logger.info("INSERT脚本执行失败:{}", result.getMessage());
//                        shareChannelDetectLog.setExecTest(2);
//                        shareChannelRepository.updateDetectStatus(shareChannelDetectLog);
//                        BusinessLoggerUtils.destroy(logger);
//                        throw new RuntimeException();
//                    }
//                }
//                shareChannelRepository.updateDetectStatus(shareChannelDetectLog);
//                BusinessLoggerUtils.destroy(logger);
//            } catch (Exception ignore) {
//                if (!scriptNames.isEmpty()) {
//                    for (String name : scriptNames) {
//                        logger.info("探测失败，删除脚本：{}", name);
//                        hcsDomainService.deleteScript(connection, projectId, name, workspace);
//                    }
//                }
//            }
//        }
//    }
//
//
//}
