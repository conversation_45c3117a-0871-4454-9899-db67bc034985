package cn.teleinfo.ds.business.huaweicloud;

import cn.hutool.json.JSONUtil;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.http.HttpConfig;
import com.huaweicloud.sdk.dataartsstudio.v1.DataArtsStudioClient;
import com.huaweicloud.sdk.dataartsstudio.v1.model.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.List;

@Slf4j
public class HcsDataArtsStudioTest {

    /**
     * AK
     */
    static String ak = "Y20RVDRARXXKDJNSYVRB";

    /**
     * SK
     */
    static String sk = "db3d1XxtHtwxXeAjpfapSu88Z26kdzjcNjtEZrvn";

    /**
     * 项目ID
     */
    static String projectId = "a24ee94724ea4bfa85f4c7938dc2e0a1";

    /**
     * 认证信息
     */
    static ICredential auth;

    /**
     * HTTP配置
     */
    static HttpConfig httpConfig;

    /**
     * 华为云HCS终端节点
     */
    static List<String> endpoints = List.of("https://dayu.cn-sdzy-1.sdgy.yc");

    /**
     * DataArtsStudio客户端
     */
    static DataArtsStudioClient client;

    /**
     * 初始化参数
     */
    @BeforeAll
    static void beforeAll() {
        // 认证凭证
        auth = new BasicCredentials().withProjectId(projectId).withAk(ak).withSk(sk);
        // 禁用SSL校验
        httpConfig = HttpConfig.getDefaultHttpConfig();
        httpConfig.setIgnoreSSLVerification(true);
        // 构建DataArtsStudioClient客户端
        client = DataArtsStudioClient.newBuilder()
                .withCredential(auth)
                .withHttpConfig(httpConfig)
                .withEndpoints(endpoints)
                .build();
        log.info("DataArtsStudio客户端创建成功");
    }
    /**
     * 获取实例列表
     */
    @Test
    void listDataArtsStudioInstances() {
        ListDataArtsStudioInstancesRequest request = new ListDataArtsStudioInstancesRequest();

        request.withLimit(20);
        request.withOffset(0);
        ListDataArtsStudioInstancesResponse response = client.listDataArtsStudioInstances(request);
        log.info(JSONUtil.toJsonStr(response));
    }

    /**
     * 获取工作空间列表
     */
    @Test
    void ListManagerWorkSpacesRequest() {
        ListManagerWorkSpacesRequest request = new ListManagerWorkSpacesRequest();
        request.withInstanceId("25f39d77ed444cec872af87e54efd3c3");
        request.withLimit(20);
        request.withOffset(0);
        ListManagerWorkSpacesResponse response = client.listManagerWorkSpaces(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 查询数据连接列表
     */
    @Test
    void listDataconnections() {
        ListDataconnectionsRequest request = new ListDataconnectionsRequest();
        request.withWorkspace("dd8652d44ba140799357ff8e2b790286");
        request.withLimit("20");
        request.withOffset("0");
        ListDataconnectionsResponse response = client.listDataconnections(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 获取数据库列表
     */
    @Test
    void listDatabases() {
        ListDatabasesRequest request = new ListDatabasesRequest();
        request.withWorkspace("dd8652d44ba140799357ff8e2b790286");
        request.withConnectionId("49db0aac863745878fbeb85c2e36d170");
        request.withLimit("20");
        request.withOffset("0");
        ListDatabasesResponse response = client.listDatabases(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

	@Test
	void listTables() {
		ListDataTablesRequest request = new ListDataTablesRequest();
		request.withWorkspace("dd8652d44ba140799357ff8e2b790286");
		request.withConnectionId("49db0aac863745878fbeb85c2e36d170");
		request.withDatabaseName("bsjx_stg_test");
//
		ListDataTablesResponse response = client.listDataTables(request);
		log.info(JSONUtil.toJsonPrettyStr(response));
	}
	@Test
	void listColumns() {
		ListColumnsRequest request = new ListColumnsRequest();
		request.withTableId("NativeTable-49db0aac863745878fbeb85c2e36d170-bsjx_stg_test-tstg_yc_handle");
		request.withWorkspace("dd8652d44ba140799357ff8e2b790286");
		request.withConnectionId("49db0aac863745878fbeb85c2e36d170");
		ListColumnsResponse response = client.listColumns(request);
		log.info(JSONUtil.toJsonPrettyStr(response));
	}


}
