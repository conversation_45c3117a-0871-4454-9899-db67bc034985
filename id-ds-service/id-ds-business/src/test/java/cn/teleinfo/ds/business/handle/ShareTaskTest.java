package cn.teleinfo.ds.business.handle;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.service.SharedTaskApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTask;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ExecutionType;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDetail;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.repository.HcsRepository;
import cn.teleinfo.ds.business.domain.service.AppInfoDomainService;
import cn.teleinfo.ds.business.domain.service.HandlesDomainService;
import cn.teleinfo.ds.business.domain.service.ShareChannelsDomainService;
import cn.teleinfo.ds.business.domain.service.ShareDataSourcesDomainService;
import cn.teleinfo.ds.business.domain.service.SharedTaskDomainService;
import cn.teleinfo.ds.business.domain.service.PlatformConnectionDomainService;
import cn.teleinfo.ds.business.domain.service.TargetSourceDomainService;
import cn.teleinfo.ds.common.log.util.BusinessLoggerOption;
import cn.teleinfo.ds.common.log.util.BusinessLoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@SpringBootTest
public class ShareTaskTest {
	@Autowired
	private SharedTaskDomainService sharedTaskDomainService;
	@Autowired
	private TargetSourceDomainService targetSourceDomainService;
	@Autowired
	private HandlesDomainService handlesDomainService;
	@Autowired
	private ShareDataSourcesDomainService shareDataSourcesDomainService;
	@Autowired
	private PlatformConnectionDomainService platformConnectionDomainService;
	@Autowired
	private AppInfoDomainService appInfoDomainService;
	@Autowired
	private ShareChannelsDomainService shareChannelsDomainService;
	@Autowired
	private BusinessLoggerOption businessLoggerOption;
	@Autowired
	private SharedTaskApplicationService sharedTaskApplicationService;
	@Autowired
	private HcsRepository hcsRepository;

	SharedTask initShareTask() {
		Long id = 1L;
		// 查询任务
		SharedTaskDomainEntity task = sharedTaskDomainService.getSharedTask(id);

		// 查询对象标识
		List<HandleDomainEntity> handles = new ArrayList<>();
		List<SharedTaskDetail> details = task.getDetails();
		for (SharedTaskDetail detail : details) {
			HandleDomainEntity handle = handlesDomainService.findByHandle(detail.getHandle());

			for (HandleItemDomainEntity item : handle.getHandleItems()) {
				ShareChannel enableChannel = shareChannelsDomainService.findEnableChannel(item.getDataChannelId());
				item.setShareChannel(enableChannel);
			}

			handles.add(handle);
		}


		// 查询应用
		AppInfoDomainEntity appInfo = appInfoDomainService.findByHandleCode(task.getAppHandleCode());

		// 查询共享源
		ShareDataSourcesDomainEntity shareDataSource = shareDataSourcesDomainService.findByAppHandleCode(task.getAppHandleCode());
		appInfo.setShareDataSources(shareDataSource);

		// 目标源
		TargetSourceDomainEntity targetSource = targetSourceDomainService.findById(task.getTargetSourceId());

		// 系统连接
		var conn = platformConnectionDomainService.findByPlatformType(targetSource.getPlatformType());

		return new SharedTask(task, handles, appInfo, targetSource, conn.getPlatformConnection(), businessLoggerOption, ExecutionType.TEST);
	}

	@Test
	void genOutputTablesSQLTest() {
		var sharedTask = initShareTask();
		sharedTask.start(); // 共享任务正式开始

		String SQL = sharedTaskDomainService.genOutputTablesSQL(sharedTask);

		System.out.println(SQL);

		System.out.println(JSONUtil.toJsonStr(sharedTask.getOutputTables()));
	}

	@Test
	void genExtractSQLTest() {
		var sharedTask = initShareTask();

		sharedTaskDomainService.genOutputTablesSQL(sharedTask);

		var SQL = sharedTaskDomainService.genExtractSQL(sharedTask);
		System.out.println();
		System.out.println(SQL);
		System.out.println();
	}

	@Test
	void outputToTargetDataSourceTest() {
		var sharedTask = initShareTask();
		sharedTask.start();

		var handle = "99.1000.1/YMZ12N98";
		var sql = """
				CREATE TABLE IF NOT EXISTS TSTD_YMZ12N98_BASIC (
				    MZ12_BSJX_APP_NAME STRING COMMENT 'MZ12_BSJX_APP_NAME',
				    MZ12_BSJX_APP_HANDLE_CODE STRING COMMENT 'MZ12_BSJX_APP_HANDLE_CODE',
				    APP_TYPE STRING COMMENT 'APP_TYPE',
				    SYS_VERSION STRING COMMENT 'SYS_VERSION'
				) COMMENT '标识解析应用信息基础属性表';
				""";

		sharedTask.getOutputTables().put(handle, new ArrayList<>());
		sharedTask.getOutputTables().get(handle).add(sql);

		sharedTaskDomainService.outputToTargetDataSource(sharedTask);
	}

	@Test
	void execTest() {
		sharedTaskApplicationService.execute(1L, ExecutionType.TEST.code());
	}

	@Test
	void loggerTest() {
		Path fullLogPath = BusinessLoggerUtils.fullLogPath(businessLoggerOption.getLogPath(), "task_001");

		Logger logger = BusinessLoggerUtils.createLogger(fullLogPath, Level.DEBUG);

		logger.info("task 001 info");
		logger.warn("task 001 warn");

		BusinessLoggerUtils.destroy(logger);
		logger = BusinessLoggerUtils.createLogger(fullLogPath, Level.DEBUG);

		logger.debug("task 001 debug");
		logger.error("task 001 error");


		BusinessLoggerUtils.destroy(logger);

		logger.error("task 001 destroy");
	}


}
