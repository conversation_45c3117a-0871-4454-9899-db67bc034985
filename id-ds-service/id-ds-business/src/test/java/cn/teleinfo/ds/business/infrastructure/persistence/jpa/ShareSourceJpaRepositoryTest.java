package cn.teleinfo.ds.business.infrastructure.persistence.jpa;

import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskAuthEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareSourceJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareTaskAuthJpaRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Pageable;

@SpringBootTest
public class ShareSourceJpaRepositoryTest {

	@Autowired
	private ShareSourceJpaRepository shareSourceJpaRepository;
	@Autowired
	private ShareTaskAuthJpaRepository shareTaskAuthJpaRepository;

	@Test
	void t(){
		var lists = shareSourceJpaRepository.listShareDataSources("TEST_HANDLE_1", null, null, null, null, null, null, Pageable.ofSize(10));
		System.out.println(JSONUtil.toJsonStr(lists));
	}


	@Test
	void findByApplicationIdAndAuthStatusTest(){
		ShareTaskAuthEntity entity = shareTaskAuthJpaRepository.findFirstByShareTaskApplicationsIdAndAuthStatus(1932717415137939456L, 3);
		System.out.println(JSONUtil.toJsonPrettyStr(entity));
	}

}
