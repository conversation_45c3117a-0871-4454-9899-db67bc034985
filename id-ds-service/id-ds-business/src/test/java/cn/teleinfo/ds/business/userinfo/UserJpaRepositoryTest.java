package cn.teleinfo.ds.business.userinfo;

import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.entity.UserInfo;
import cn.teleinfo.ds.business.domain.repository.UserRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.UserInfoView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.UserJpaRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest
public class UserJpaRepositoryTest {
	@Autowired
	private UserJpaRepository userJpaRepository;
	@Autowired
	private UserRepository userRepository;

	@Test
	void findInfoByIdTest(){
		UserInfo userinfo = userRepository.findById("admin");

		System.out.println(userinfo.getUsername());
		System.out.println(JSONUtil.toJsonStr(userinfo.getAppIds()));

	}
}
