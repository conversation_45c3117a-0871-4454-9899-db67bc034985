package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.domain.repository.HcsRepository;
import cn.teleinfo.ds.business.domain.service.HcsDomainService;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmLinkResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.HcsBaseResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ListScriptResultsResponseDTO;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ScriptInfoDTO;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigCommodityOrder;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigDataSourceView;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ColumnsList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.DatabasesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.TablesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.Workspacebody;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Service
public class HcsDomainServiceImpl implements HcsDomainService {
	private final HcsRepository hcsRepository;

	/**
	 * 数据集成-规范层连接名称
	 */
	@Override
	public List<Links> findCdmConnections(PlatformConnection platformConnection, String projectId, String clusterId) {
		platformConnection.check();

		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getCdmEndpoint();
		return hcsRepository.findCdmConnections(ak, sk, projectId, List.of(endpoints), clusterId);
	}

	/**
	 * 数据开发-规范层连接名称
	 */
	@Override
	public List<ApigDataSourceView> findDayuConnections(PlatformConnection platformConnection, String projectId,
														String workspace, Integer offset, Integer limit) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDataArtsStudioEndpoint();
		return hcsRepository.findDayuConnections(ak, sk, projectId, List.of(endpoints), workspace, offset, limit);
	}

	/**
	 * 数据开发-规范层数据库名称
	 */
	@Override
	public List<DatabasesList> findDayuConnectionsDatabases(PlatformConnection platformConnection, String projectId,
															String workspace, String connectionId, Integer offset, Integer limit) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDataArtsStudioEndpoint();
		return hcsRepository.findDayuConnectionsDatabases(ak, sk, projectId, List.of(endpoints), workspace, connectionId, offset, limit);
	}

	/**
	 * 获取资源空间列表
	 */
	@Override
	public List<AuthProjectResult> findProjects(PlatformConnection platformConnection) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getIamEndpoint();
		return hcsRepository.findProjects(ak, sk, List.of(endpoints));
	}

	/**
	 * 获取数据治理中心实例列表
	 */
	@Override
	public List<ApigCommodityOrder> findDasInstances(PlatformConnection platformConnection, String projectId, Integer current, Integer size) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDataArtsStudioEndpoint();
		return hcsRepository.findDasInstances(ak, sk, projectId, List.of(endpoints), current, size);
	}

	/**
	 * 获取工作空间列表
	 */
	@Override
	public List<Workspacebody> findDasWorkspaces(PlatformConnection platformConnection, String projectId, String instanceId, Integer current, Integer size) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDataArtsStudioEndpoint();
		return hcsRepository.findDasWorkspaces(ak, sk, projectId, instanceId, List.of(endpoints), current, size);
	}

	/**
	 * 获取CDM集群名称列表
	 */
	@Override
	public List<Clusters> findCdmClusters(PlatformConnection platformConnection, String projectId) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getCdmEndpoint();
		return hcsRepository.findCdmClusters(ak, sk, projectId, List.of(endpoints));
	}

	@Override
	public List<Job> findJobs(PlatformConnection platformConnection, String projectId, String clusterId) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getCdmEndpoint();
		return hcsRepository.findJobs(ak, sk, projectId, List.of(endpoints), clusterId);
	}

	@Override
	public void createScript(PlatformConnection platformConnection, String projectId, String scriptName, String scriptContent, String workspace, String databaseName, String connectionName) {

		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDgcEndpoint();
		hcsRepository.createScript(ak, sk, projectId, List.of(endpoints), scriptName, scriptContent, workspace, databaseName, connectionName);
	}

	@Override
	public String executeScript(PlatformConnection platformConnection, String projectId, String scriptName, String workspace) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDgcEndpoint();
		return hcsRepository.executeScript(ak, sk, projectId, List.of(endpoints), scriptName, workspace);

	}

	@Override
	public ListScriptResultsResponseDTO listScriptResults(PlatformConnection platformConnection, String projectId, String scriptName, String workspace, String instanceId) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDgcEndpoint();
		return hcsRepository.listScriptResults(ak, sk, projectId, List.of(endpoints), scriptName, workspace, instanceId);
	}

	@Override
	public List<TablesList> listTables(PlatformConnection platformConnection, String projectId, String connectionId, String databaseName, String tableName, String workSpace) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDataArtsStudioEndpoint();
		return hcsRepository.listTables(ak, sk, projectId, List.of(endpoints), connectionId, databaseName, tableName, workSpace);

	}

	@Override
	public List<ColumnsList> listTableColumns(PlatformConnection platformConnection, String projectId, String connectionId, String workSpace, String tableId) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDataArtsStudioEndpoint();
		return hcsRepository.listTableColumns(ak, sk, projectId, List.of(endpoints), connectionId, workSpace, tableId);
	}

	@Override
	public void deleteScript(PlatformConnection platformConnection, String projectId, String scriptName, String workspace) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDgcEndpoint();
		hcsRepository.deleteScript(ak, sk, projectId, List.of(endpoints), scriptName, workspace);
	}

	@Override
	public ScriptInfoDTO scriptList(PlatformConnection platformConnection, String projectId, String workspace, Integer limit, Integer offset) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDgcEndpoint();
		return hcsRepository.findScriptList(ak, sk, projectId, List.of(endpoints), workspace, limit, offset);
	}

	@Override
	public ScriptInfo findScript(PlatformConnection platformConnection, String projectId, String workspace, String scriptName) {
		platformConnection.check();
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = platformConnection.getHcsConnContent().getDgcEndpoint();
		return hcsRepository.findScript(ak, sk, projectId, List.of(endpoints), workspace, scriptName);
	}

	/**
	 * 查询连接
	 */
	@Override
	public CdmLinkResponse listLink(PlatformConnection platformConnection, String linkName) {
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		List<String> endpoint = List.of(platformConnection.getHcsConnContent().getCdmEndpoint());
		String projectId = platformConnection.getHcsConnContent().getProjectId();
		String clusterId = platformConnection.getHcsConnContent().getClusterId();

		return hcsRepository.listLink(ak, sk, projectId, endpoint, clusterId, linkName);
	}

	/**
	 * 创建连接
	 */
	@Override
	public void createLink(PlatformConnection platformConnection, String host, Integer port, String database, String username, String password, String linkName) {
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		List<String> endpoint = List.of(platformConnection.getHcsConnContent().getCdmEndpoint());
		String projectId = platformConnection.getHcsConnContent().getProjectId();
		String clusterId = platformConnection.getHcsConnContent().getClusterId();

		HcsBaseResponse resp = hcsRepository.createLink(ak, sk, projectId, endpoint, clusterId, host, port, database, username, password, linkName);
		if (StrUtil.isNotEmpty(resp.getErrorCode())) {
			throw new CheckedException(resp.getErrorMsg());
		}
	}

	@Override
	public void updateLink(PlatformConnection platformConnection, String host, Integer port, String database, String username, String password, String linkName) {
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		List<String> endpoint = List.of(platformConnection.getHcsConnContent().getCdmEndpoint());
		String projectId = platformConnection.getHcsConnContent().getProjectId();
		String clusterId = platformConnection.getHcsConnContent().getClusterId();

		HcsBaseResponse resp = hcsRepository.updateLink(ak, sk, projectId, endpoint, clusterId, host, port, database, username, password, linkName);
		if (StrUtil.isNotEmpty(resp.getErrorCode())) {
			throw new CheckedException(resp.getErrorMsg());
		}
	}

	/**
	 * 删除连接
	 */
	@Override
	public void delLink(PlatformConnection platformConnection, String linkName) {
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		List<String> endpoint = List.of(platformConnection.getHcsConnContent().getCdmEndpoint());
		String projectId = platformConnection.getHcsConnContent().getProjectId();
		String clusterId = platformConnection.getHcsConnContent().getClusterId();

		HcsBaseResponse resp = hcsRepository.delLink(ak, sk, projectId, endpoint, clusterId, linkName);
		if (StrUtil.isNotEmpty(resp.getErrorCode())) {
			throw new CheckedException(resp.getErrorMsg());
		}
	}
}
