package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.to.DataChannelTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.DataChannelEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.DataChannelsView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DataChannelJpaRepository extends BaseRepository<DataChannelEntity, Long> {

	@Query(nativeQuery = true, value = """
			SELECT
			    d.id AS id,
			    d.source_id as sourceId,
			    d.data_channel_name AS dataChannelName,
			    d.data_type AS dataType,
			    d.update_time AS updateTime,
			    d.resolve_sql AS resolveSql,
			    d.query_sql AS querySql,
			    d.object_handle AS objectHandle,
			    d.data_channel_id as dataChannelId
			    from t_data_channel d
			    where IF(:handle IS NOT NULL, d.object_handle = :handle, 1=1 )
			    AND IF(:appHandleCode IS NOT NULL, d.app_handle_code = :appHandleCode, 1=1 )
			    AND is_deleted = 0
			
			""", countQuery = """
			SELECT
				count(*)
				from t_data_channel d
				where IF(:handle IS NOT NULL, d.object_handle = :handle, 1=1 )
				AND IF(:appHandleCode IS NOT NULL, d.app_handle_code = :appHandleCode, 1=1 )
				and is_deleted = 0
			"""
	)
	Page<DataChannelsView> findListDataChannels(@Param("handle") String handle, @Param("appHandleCode") String appHandleCode, Pageable pageable);


	@Query(nativeQuery = true, value = """
			select * from t_data_channel 
			""")
	List<DataChannelEntity> findDataChannelEntity();


	@Query(nativeQuery = true, value = """
				SELECT
			                           dc.id AS id,
			                           dc.source_id AS sourceId,
			                           dc.data_channel_name AS dataChannelName,
			                           dc.object_handle AS objectHandle,
			                           CASE
			                               WHEN EXISTS (
			                                   SELECT 1
			                                   FROM t_handle_item hi
			                                   WHERE hi.data_channel_id = dc.data_channel_id
			                                   AND hi.is_deleted = 0
			                                   AND hi.field_source_type = 0
			                               ) THEN 1
			                               WHEN EXISTS (
			                                   SELECT 1
			                                   FROM t_handle_item hi
			                                   WHERE hi.data_channel_id = dc.data_channel_id
			                                   AND hi.is_deleted = 0
			                                   AND hi.field_source_type = 1
			                               ) THEN 2
			                               ELSE dc.object_handle_type
			                           END AS itemType,
			                           dc.data_channel_id AS dataChannelId,
			                           dc.data_type AS dataType,
			                           dc.resolve_sql AS resolveSql,
			                           dc.query_sql AS querySql,
			                           dc.province_prefix AS provincePrefix,
			                           dc.ent_prefix AS entPrefix,
			                           dc.app_handle_code AS appHandleCode,
			                           dc.is_share AS isShare,
			                           dc.create_time AS createTime,
			                           dc.update_time AS updateTime,
			                           dc.is_deleted AS isDeleted
			                       FROM
			                           t_data_channel dc
			                       WHERE
			                           dc.is_deleted = 0;
			""")
	List<DataChannelTO> findDataChannels();

	@Query(nativeQuery = true, value = """
				SELECT
			                           dc.id AS id,
			                           dc.source_id AS sourceId,
			                           dc.data_channel_name AS dataChannelName,
			                           dc.object_handle AS objectHandle,
			                           CASE
			                               WHEN EXISTS (
			                                   SELECT 1
			                                   FROM t_handle_item hi
			                                   WHERE hi.data_channel_id = dc.data_channel_id
			                                   AND hi.is_deleted = 0
			                                   AND hi.field_source_type = 0
			                               ) THEN 1
			                               WHEN EXISTS (
			                                   SELECT 1
			                                   FROM t_handle_item hi
			                                   WHERE hi.data_channel_id = dc.data_channel_id
			                                   AND hi.is_deleted = 0
			                                   AND hi.field_source_type = 1
			                               ) THEN 2
			                               ELSE dc.object_handle_type
			                           END AS itemType,
			                           dc.data_channel_id AS dataChannelId,
			                           dc.data_type AS dataType,
			                           dc.resolve_sql AS resolveSql,
			                           dc.query_sql AS querySql,
			                           dc.province_prefix AS provincePrefix,
			                           dc.ent_prefix AS entPrefix,
			                           dc.app_handle_code AS appHandleCode,
			                           dc.is_share AS isShare,
			                           dc.create_time AS createTime,
			                           dc.update_time AS updateTime,
			                           dc.is_deleted AS isDeleted
			                       FROM
			                           t_data_channel dc
			                       WHERE
			                           dc.is_deleted = 0 and dc.data_channel_id= :shareChannelId;
			""")
	DataChannelTO findDataChannel(@Param("shareChannelId") Long shareChannelId);
}