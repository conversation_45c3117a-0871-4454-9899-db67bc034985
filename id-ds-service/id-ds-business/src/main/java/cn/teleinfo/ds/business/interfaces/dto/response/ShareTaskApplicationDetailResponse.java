package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ShareTaskApplicationDetailResponse {

	/**
	 * 共享任务申请详情
	 */
	private ShareTaskApplicationResponse basicInfo;

	/**
	 * 授权记录
	 */
	private List<ShareTaskAuthApplicationResponse> authorizeRecords;

	private List<ShareTaskApplicationDetailDataResponse> shareData;

	/**
	 * 新增详情信息
	 */
	private Object applicationInfo;


}
