package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceDetailResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;

public interface SharedTaskInstanceAppService {

	PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query);

	SharedTaskInstanceDetailResponse getShareTaskInstanceDetail(Long instanceId);


	HandleApplicationResponse getHandleItemsByHandle(Long applicationId);


}
