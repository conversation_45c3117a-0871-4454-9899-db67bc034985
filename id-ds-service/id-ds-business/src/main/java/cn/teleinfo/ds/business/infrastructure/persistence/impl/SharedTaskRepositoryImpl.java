package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDetail;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.repository.SharedTaskRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskBasicInfoValueDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskDetailEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.SharedTaskDetailJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.SharedTaskJpaRepository;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Component
@AllArgsConstructor
public class SharedTaskRepositoryImpl implements SharedTaskRepository {

	private final SharedTaskJpaRepository sharedTaskJpaRepository;

	private final SharedTaskDetailJpaRepository sharedTaskDetailJpaRepository;

	@Override
	public Long save(SharedTaskDomainEntity sharedTaskDomainEntity) {
		SharedTaskEntity sharedTaskEntity = new SharedTaskEntity();
		BeanUtils.copyProperties(sharedTaskDomainEntity, sharedTaskEntity);
		this.sharedTaskJpaRepository.save(sharedTaskEntity);
		return sharedTaskEntity.getId();
	}

	@Override
	public PageResponse<SharedTaskDomainEntity> listSharedTask(SharedTaskDomainEntity entity, LocalDateTime start,
															   LocalDateTime end, Integer page, Integer size, String taskCode, String appHandleCode, List<String> userHandleCodes, LocalDateTime editStart, LocalDateTime editEnd) {

		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);

		var results = sharedTaskJpaRepository.findListSharedTask(entity.getTaskName(), entity.getTaskType(), entity.getRunStatus(),
				start, end, taskCode, appHandleCode, userHandleCodes, editStart, editEnd, pageable);
		/*Page<SharedTaskEntity> p = sharedTaskJpaRepository.findAll((root, query, cb) -> {
			List<Predicate> predicates = new ArrayList<>();

			if (StringUtils.hasText(entity.getTaskName())) {
				predicates.add(cb.like(root.get("taskName"), "%" + entity.getTaskName() + "%"));
			}

			if (entity.getTaskType() != null) {
				predicates.add(cb.equal(root.get("taskType"), entity.getTaskType()));
			}

			if (entity.getRunStatus() != null) {
				predicates.add(cb.equal(root.get("runStatus"), entity.getRunStatus()));
			}

			// 最后一次运行时间
			if (start != null && end != null) {
				predicates.add(cb.between(root.get("lastRunTime"), start, end));
			}
			else if (start != null) {
				predicates.add(cb.greaterThanOrEqualTo(root.get("lastRunTime"), end));
			}

			return cb.and(predicates.toArray(new Predicate[0]));
		}, pageable);

		var records = p.stream().map(e -> BeanUtil.copyProperties(e, SharedTaskDomainEntity.class)).toList();*/

		return new PageResponse<>(BeanUtil.copyToList(results.toList(), SharedTaskDomainEntity.class), results.getTotalElements(), (long) size, (long) page, (long) results.getTotalPages());
	}

	@Override
	public SharedTaskDomainEntity findById(Long id) {
		Optional<SharedTaskEntity> optional = sharedTaskJpaRepository.findById(id);

		List<SharedTaskDetailEntity> details = sharedTaskDetailJpaRepository.findBySharedTaskId(id);

		return optional.map(e -> {
			SharedTaskDomainEntity entity = BeanUtil.copyProperties(e, SharedTaskDomainEntity.class);
			entity.setDetails(BeanUtil.copyToList(details, SharedTaskDetail.class));
			return entity;
		}).orElse(null);
	}

	@Override
	public void deleteById(Long id) {
		sharedTaskJpaRepository.deleteById(id);
	}

	@Override
	public List<SharedTaskDomainEntity> findAllByTargetSourceId(Long id) {
		var entityList = sharedTaskJpaRepository.findAllByTargetSourceId(id);
		return BeanUtil.copyToList(entityList, SharedTaskDomainEntity.class);
	}

	@Override
	public SharedTaskBasicInfoValueDTO getSharedTaskBasicInfoValueDTO(Long id) {
		return sharedTaskJpaRepository.getSharedTaskBasicInfoValueDTO(id);
	}

}
