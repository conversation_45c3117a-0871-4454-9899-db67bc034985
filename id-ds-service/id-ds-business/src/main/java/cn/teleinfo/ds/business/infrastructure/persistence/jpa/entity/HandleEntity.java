package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Getter
@Setter
@Entity
@Table(name = "t_handle")
@SQLDelete(sql = "update t_handle set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class HandleEntity extends BaseEntity {
	/**
	 * 企业前缀
	 */
	@Column(name = "ent_prefix")
	private String entPrefix;

	/**
	 * 通配符
	 */
	@Column(name = "wildcard")
	private String wildcard;

	/**
	 * 标识名称
	 */
	@Column(name = "name")
	private String name;

	/**
	 * 标识
	 */
	@Column(name = "handle")
	private String handle;

	/**
	 * 实体类型 1业务实体 2资源实体
	 */
	@Column(name = "entity_type")
	private Integer entityType;

	/**
	 * 应用标识身份
	 */
	@Column(name = "app_handle_code")
	private String appHandleCode;

	/**
	 * 省级前缀
	 */
	@Column(name = "province_prefix")
	private String provincePrefix;
}
