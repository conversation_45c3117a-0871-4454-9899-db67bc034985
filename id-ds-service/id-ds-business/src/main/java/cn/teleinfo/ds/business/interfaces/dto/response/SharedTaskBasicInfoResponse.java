package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SharedTaskBasicInfoResponse {

	private Long id;

	private Integer taskType;

	private Integer taskStatus;

	private Integer testStatus;

	private Integer runStatus;

	private Integer lastRunDuration;

	private Integer lastSharedDataCount;

	private String taskNo;

	private String taskName;

	private String databaseName;

	private String appName;

	private String entName;

	private String lastExecutionNo;

	private String operator;

	private String cronExpression;

	private String cronDetail;

	private String targetName;

	private String appHandleName;

	private LocalDateTime lastRunTime;

	private LocalDateTime updateTime;
}
