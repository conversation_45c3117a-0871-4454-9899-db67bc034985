package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ShareTaskAuthResponse {

	/**
	 * id
	 */
	private Long id;

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 任务类型(1：手动任务 2：定时任务)
	 */
	private Integer taskType;

	/**
	 * CRON表达式
	 */
	private String cronExpression;

	/**
	 * 定时周期
	 */
	private String cronDetail;

	/**
	 * 目标源名称
	 */
	private String targetSourceName;

	/**
	 * 目标源-数据库名称
	 */
	private String targetDatabaseName;

	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 企业名称
	 */
	private String entName;

	/**
	 * 申请人
	 */
	private String createByName;


	/**
	 * 授权状态( 1:申请中 2:已驳回 3:已授权)
	 */
	private Integer authStatus;

	/**
	 * 操作人
	 */
	private String auditUserName;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 共享数据
	 */
	private List<ShareTaskAuthDetailsResponse> details;
}
