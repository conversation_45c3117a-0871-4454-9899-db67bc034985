package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareDataDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.EntPrefixDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationDetailValue;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskAuthApplicationValue;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskAuthDTO;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskApplicationsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareTaskApplicationsRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.*;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ShareTaskApplicationsAssemble {

	public ShareTaskApplicationsCommand toCommand(ShareTaskApplicationsRequest request) {
		ShareTaskApplicationsCommand command = new ShareTaskApplicationsCommand();
		BeanUtils.copyProperties(request, command);
		return command;
	}

	public ShareTaskApplicationsQuery toQueryList(ListShareTaskApplicationsRequest request) {
		ShareTaskApplicationsQuery query = new ShareTaskApplicationsQuery();
		BeanUtils.copyProperties(request, query);
		return query;
	}

	/**
	 * 转换详情信息
	 *
	 * @param details 详情信息
	 * @return 详情信息响应
	 */
	public ShareTaskApplicationDetailResponse toDetailResponse(ShareTaskApplicationDetails details) {
		ShareTaskApplicationDetailResponse query = new ShareTaskApplicationDetailResponse();
		ShareTaskApplicationResponse applicationResponse = new ShareTaskApplicationResponse();
		BeanUtils.copyProperties(details.getBasicInfo(), applicationResponse);
		query.setBasicInfo(applicationResponse);
		// 获取授权信息
		if (CollectionUtil.isNotEmpty(details.getAuthorizeRecords())) {
			ArrayList<ShareTaskAuthApplicationResponse> values = new ArrayList<>();
			for (ShareTaskAuthApplicationValue auth : details.getAuthorizeRecords()) {
				ShareTaskAuthApplicationResponse value = new ShareTaskAuthApplicationResponse();
				BeanUtils.copyProperties(auth, value);
				values.add(value);
			}
			query.setAuthorizeRecords(values);
		}
		// 获取共享数据
		if (CollectionUtil.isNotEmpty(details.getShareData())) {
			ArrayList<ShareTaskApplicationDetailDataResponse> detailValues = new ArrayList<>();
			details.getShareData().forEach(detail -> {
				ShareTaskApplicationDetailDataResponse value = new ShareTaskApplicationDetailDataResponse();
				BeanUtils.copyProperties(detail, value);
				detailValues.add(value);
			});
			query.setShareData(detailValues);
		}
		if (ObjectUtil.isNotNull(details.getApplicationInfo())) {
			query.setApplicationInfo(details.getApplicationInfo());
		}
		return query;
	}

	public ShareTaskAuthResponse toShareTaskAuthResponse(ShareTaskAuthDomainEntity shareTaskAuth) {
		ShareTaskAuthResponse response = new ShareTaskAuthResponse();

		TargetSourceDomainEntity targetSource = shareTaskAuth.getTargetSource();
		if (targetSource != null) {
			response.setTargetSourceName(targetSource.getTargetSourceName());
			response.setTargetDatabaseName(targetSource.getTargetSourceItems().getDatabaseName());
		}

		AppInfoDomainEntity appInfo = shareTaskAuth.getAppInfo();
		if (appInfo != null) {
			response.setAppName(appInfo.getAppName());
		}

		EntPrefixDomainEntity ent = shareTaskAuth.getEnt();
		if (ent != null) {
			response.setEntName(ent.getOrgName());
		}

		response.setId(shareTaskAuth.getId());
		response.setTaskName(shareTaskAuth.getTaskName());
		response.setTaskNo(shareTaskAuth.getTaskNo());
		response.setTaskType(shareTaskAuth.getTaskType());
		response.setCronExpression(shareTaskAuth.getCronExpression());
		response.setAuthStatus(shareTaskAuth.getAuthStatus());
		response.setUpdateTime(shareTaskAuth.getUpdateTime());

		response.setCreateByName(shareTaskAuth.getCreateByName());
		response.setAuditUserName(shareTaskAuth.getAuditUserName());
		response.setCronDetail(shareTaskAuth.getCronDetail());


		List<ShareTaskAuthDetailsResponse> details = new ArrayList<>();
		for (ShareTaskAuthDetailsDomainEntity each : shareTaskAuth.getShareTaskAuthDetails()) {
			ShareTaskAuthDetailsResponse d = new ShareTaskAuthDetailsResponse();
			d.setId(each.getId());
			d.setHandle(each.getHandle());
			d.setHandleName(each.getHandleName());
			d.setProvincePrefix(each.getProvincePrefix());
			d.setProvinceName(each.getProvinceName());
			d.setEntPrefix(each.getEntPrefix());
			d.setEntName(each.getEntName());
			d.setAppHandleCode(each.getAppHandleCode());
			d.setAppName(each.getAppName());
			details.add(d);

		}
		response.setDetails(details);

		return response;
	}

	public List<ShareTaskApplicationTargetSourceResponse> toShareTaskApplicationTargetSourceResponse(List<TargetSource> targetSources) {
		List<ShareTaskApplicationTargetSourceResponse> result = new ArrayList<>();

		for (TargetSource targetSource : targetSources) {
			ShareTaskApplicationTargetSourceResponse shareTaskApplicationTargetSourceResponse = new ShareTaskApplicationTargetSourceResponse();
			shareTaskApplicationTargetSourceResponse.setId(targetSource.getTargetSourceDomainEntity().getId());
			shareTaskApplicationTargetSourceResponse.setTargetSourceName(targetSource.getTargetSourceDomainEntity().getTargetSourceName());
			shareTaskApplicationTargetSourceResponse.setPlatformType(targetSource.getTargetSourceDomainEntity().getPlatformType());
			shareTaskApplicationTargetSourceResponse.setAppHandleCode(targetSource.getAppInfo().getHandleCode());
			shareTaskApplicationTargetSourceResponse.setAppName(targetSource.getAppInfo().getAppName());
			shareTaskApplicationTargetSourceResponse.setEntPrefix(targetSource.getAppInfo().getEntPrefix());
			shareTaskApplicationTargetSourceResponse.setEntName(targetSource.getAppInfo().getEntName());
			result.add(shareTaskApplicationTargetSourceResponse);
		}
		return result;
	}

	public SharedDataDetailsResponse toSharedDataDetailsResponse(ShareDataDetails sharedDataDetails) {
		return BeanUtil.copyProperties(sharedDataDetails, SharedDataDetailsResponse.class);
	}

	public ShareTaskApplicationGraphResponse toShareTaskApplicationGraphResponse(ShareTaskApplicationsDomainEntity graph) {
		ShareTaskApplicationGraphResponse response = new ShareTaskApplicationGraphResponse();

		TargetSourceDomainEntity targetSource = graph.getTargetSource();
		AppInfoDomainEntity appInfo = graph.getAppInfo();
		EntPrefixDomainEntity ent = graph.getEntPrefixDomainEntity();

		response.setId(graph.getId());
		response.setTaskName(graph.getTaskName());
		response.setTaskType(graph.getTaskType());
		response.setTargetSourceId(graph.getTargetSourceId());
		response.setTargetSourceName(targetSource.getTargetSourceName());
		response.setAppHandleCode(appInfo.getHandleCode());
		response.setAppName(appInfo.getAppName());
		response.setEntPrefix(ent.getEntPrefix());
		response.setEntName(ent.getOrgName());
		response.setCronExpression(graph.getCronExpression());
		response.setGraph(JSONUtil.toBean(graph.getGraph(), GraphResponse.class));

		List<CartResponse> cart = new ArrayList<>();
		if (graph.getDetailsDomainEntities() != null && !graph.getDetailsDomainEntities().isEmpty()) {
			for (ShareTaskApplicationsDetailsDomainEntity details : graph.getDetailsDomainEntities()) {
				CartResponse cartResponse = new CartResponse();


				EntPrefixDomainEntity entPrefixDomainEntity = details.getEntPrefixDomainEntity();
				cartResponse.setEntPrefix(entPrefixDomainEntity.getEntPrefix());
				cartResponse.setEntName(entPrefixDomainEntity.getOrgName());

				AppInfoDomainEntity app = details.getAppInfo();

				cartResponse.setAppHandleCode(app.getHandleCode());
				cartResponse.setAppName(app.getAppName());

				HandleDomainEntity handle = details.getHandleDomainEntity();

				cartResponse.setHandle(details.getHandle());
				cartResponse.setName(handle.getName());
				cartResponse.setEntityType(handle.getEntityType());

				List<CartHandleFieldResponse> fields = new ArrayList<>();

				for (HandleItemDomainEntity handleItem : handle.getHandleItems()) {
					CartHandleFieldResponse cartHandleFieldResponse = new CartHandleFieldResponse();
					cartHandleFieldResponse.setField(handleItem.getField());
					cartHandleFieldResponse.setDescription(handleItem.getDescription());
					cartHandleFieldResponse.setFieldSourceType(handleItem.getFieldSourceType());
					fields.add(cartHandleFieldResponse);
				}

				cartResponse.setFields(fields);
				cart.add(cartResponse);
			}
		}

		response.setCart(cart);

		return response;
	}
}
