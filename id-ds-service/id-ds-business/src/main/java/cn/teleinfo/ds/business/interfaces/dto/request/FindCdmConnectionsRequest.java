package cn.teleinfo.ds.business.interfaces.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class FindCdmConnectionsRequest {
	/**
	 * 平台类型
	 * t_connection platform_type
	 */
	@NotNull(message = "平台类型 不能为空")
	private Integer platformType;

	/**
	 * 资源空间
	 */
	@NotNull(message = "资源空间 不能为空")
	private String projectId;

	/**
	 * 集群ID
	 */
	@NotNull(message = "集群ID 不能为空")
	private String clusterId;
}
