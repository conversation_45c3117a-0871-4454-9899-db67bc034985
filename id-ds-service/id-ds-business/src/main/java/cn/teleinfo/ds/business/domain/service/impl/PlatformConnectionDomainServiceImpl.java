package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionType;
import cn.teleinfo.ds.business.domain.repository.PlatformConnectionRepository;
import cn.teleinfo.ds.business.domain.service.PlatformConnectionDomainService;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class PlatformConnectionDomainServiceImpl implements PlatformConnectionDomainService {
	private final PlatformConnectionRepository sysConnectionRepository;

	@Override
	public PlatformConnection findByPlatformType(Integer platformType) {
		var code = PlatformConnectionType.findByCode(platformType);
		PlatformConnectionDomainEntity conn = sysConnectionRepository.findByPlatformType(code);
		if (conn == null) {
			throw new CheckedException("未配置系统连接！");
		}

		return new PlatformConnection(conn);
	}

	@Override
	public void updateConnectionSetting(Integer platformType, String platformConnection) {
		sysConnectionRepository.updateConnectionSetting(platformType, platformConnection);
	}
}
