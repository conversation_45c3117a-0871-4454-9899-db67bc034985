package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.sql.Timestamp;

@Getter
@Setter
@Entity
@Table(name = "t_share_channel_applications")
@SQLDelete(sql = "update t_share_channel_applications set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class ShareChannelApplicationsEntity {

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "id")
	private Long id;

	/**
	 * 共享通道ID
	 */
	@Column(name = "share_channel_id")
	private String shareChannelId;

	/**
	 * 任务状态(1:申请中 2:已驳回 3:已授权)
	 */
	@Column(name = "channel_status")
	private Integer channelStatus;

	/**
	 * 审核人ID
	 */
	@Column(name = "audit_user_id")
	private Long auditUserId;

	/**
	 * 审核备注
	 */
	@Column(name = "audit_remark")
	private String auditRemark;

	/**
	 * 0:否, NULL:是
	 */
	@Column(name = "is_deleted")
	private Integer isDeleted;

	/**
	 * 主版本号
	 */
	@Column(name = "main_version")
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	@Column(name = "minor_version")
	private Integer minorVersion;

	/**
	 * 申请人ID
	 */
	@Column(name = "apply_user_id")
	private String applyUserId;

	/**
	 * 创建时间
	 */
	@Column(name = "created_time")
	private Timestamp createdTime;

	/**
	 * 更新时间
	 */
	@Column(name = "updated_time")
	private Timestamp updatedTime;

}
