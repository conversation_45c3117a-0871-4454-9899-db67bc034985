package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;

import java.util.Date;

/**
 * 企业前缀信息表
 */
@Getter
@Setter
@Entity
@Table(name = "t_ent_prefix")
public class EntPrefixEntity {

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "id")
	private Long id;

	/**
	 * 创建时间
	 */
	@Column(name = "created_time")
	private Date createdTime;

	/**
	 * 更新时间
	 */
	@Column(name = "updated_time")
	private Date updatedTime;

	/**
	 * 企业前缀
	 */
	@Column(name = "ent_prefix")
	private String entPrefix;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	private String orgName;

	/**
	 * 组织机构编码
	 */
	@Column(name = "org_code")
	private String orgCode;

	/**
	 * 上级单位名称
	 */
	@Column(name = "parent_org_name")
	private String parentOrgName;

	/**
	 * 所属省
	 */
	@Column(name = "org_addr_province")
	private String orgAddrProvince;

	/**
	 * 所属市
	 */
	@Column(name = "org_addr_city")
	private String orgAddrCity;

	/**
	 * 所属区
	 */
	@Column(name = "org_addr_district")
	private String orgAddrDistrict;

	/**
	 * 地址
	 */
	@Column(name = "org_address")
	private String orgAddress;

	/**
	 * 省级前缀
	 */
	@Column(name = "province_prefix")
	private String provincePrefix;

} 