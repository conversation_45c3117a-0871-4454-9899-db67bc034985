package cn.teleinfo.ds.business.interfaces.dto.request;

import cn.teleinfo.ds.common.core.util.PageRequest;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class ListSharedTaskRequest extends PageRequest {
	/**
	 * 任务名称
	 */
	private String taskName;
	/**
	 * 任务类型：1-手动任务，2-定时任务
	 */
	private Integer taskType;
	/**
	 * 运行状态：1-运行中，2-成功，3-失败，0-未运行
	 */
	private Integer runStatus;
	/**
	 * 最后一次运行时间-开始时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime start;
	/**
	 * 最后一次运行时间-结束时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime end;

	/**
	 * 任务编号
	 */
	private String taskCode;

	/**
	 * 应用编码
	 */
	private String appHandleCode;
	/**
	 * 最后一次操作时间-开始时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime editStart;
	/**
	 * 最后一次操作时间-结束时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime editEnd;
}
