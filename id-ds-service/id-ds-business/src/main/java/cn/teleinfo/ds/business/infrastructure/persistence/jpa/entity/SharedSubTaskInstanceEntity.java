package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntityListeners;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 共享子任务表
 */
@Getter
@Setter
@Entity
@Table(name = "t_shared_sub_task_instance")
@EntityListeners({BaseEntityListeners.class, AuditingEntityListener.class})
public class SharedSubTaskInstanceEntity {

	@Id
	private Long id;

	/**
	 * 创建时间
	 */
	@CreatedDate
	@Column(name = "create_time")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@LastModifiedDate
	@Column(name = "update_time")
	private LocalDateTime updateTime;

	/**
	 * 关联共享任务实例ID
	 */
	@Column(name = "shared_task_instance_id", nullable = false)
	private Long sharedTaskInstanceId;

	/**
	 * 任务执行编号，如RW202506001
	 */
	@Column(name = "task_instance_no", nullable = false)
	private String taskInstanceNo;

	/**
	 * 对象标识
	 */
	@Column(name = "handle", nullable = false)
	private String handle;

	/**
	 * 子任务状态：0-未开始，1-进行中，2-成功，3-失败
	 */
	@Column(name = "sub_task_status", nullable = false)
	private Integer subTaskStatus;

	/**
	 * 当前执行步骤（失败时记录在哪一步失败）
	 * 1 分析湖仓映射关系
	 * 2. 查询批量共享通道 SQL
	 * 3. 数据写入对象标识
	 * 4. 创建离线管道任务
	 * 5. 执行任务写入目标源';
	 */
	@Column(name = "current_step")
	private Integer currentStep;

	/**
	 * 运行时间
	 */
	@Column(name = "run_time")
	private LocalDateTime runTime;

	/**
	 * 运行时长(秒)
	 */
	@Column(name = "run_duration")
	private Integer runDuration;

	/**
	 * 共享数据总量
	 */
	@Column(name = "shared_data_count")
	private Integer sharedDataCount;

	/**
	 * 任务日志路径
	 */
	@Column(name = "log_path", columnDefinition = "text")
	private String logPath;

	/**
	 * 错误信息（失败时记录具体错误）
	 */
	@Column(name = "error_message", columnDefinition = "text")
	private String errorMessage;
}