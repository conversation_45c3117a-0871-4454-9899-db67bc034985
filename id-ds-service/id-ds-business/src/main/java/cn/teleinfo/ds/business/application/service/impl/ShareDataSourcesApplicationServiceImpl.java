package cn.teleinfo.ds.business.application.service.impl;

import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.command.CreateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.query.ListShareDataSourcesQuery;
import cn.teleinfo.ds.business.application.service.ShareDataSourcesApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.service.ShareDataSourcesDomainService;
import cn.teleinfo.ds.business.domain.service.PlatformConnectionDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareDataSourcesDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ShareDataSourcesApplicationServiceImpl implements ShareDataSourcesApplicationService {

	private final ShareDataSourcesDomainService shareDataSourcesDomainService;

	private final PlatformConnectionDomainService platformConnectionDomainService;

	/**
	 * 创建共享数据源
	 */
	@Override
	public void createShareDataSources(CreateShareDataSourcesCommand command) {
		shareDataSourcesDomainService.createShareDataSources(command.getPlatformType(), command.getAppHandleCode(), JSONUtil.toJsonStr(command.getItems()));
	}

	/**
	 * 查询共享源列表
	 */
	@Override
	public PageResponse<ShareSourceDTO> listShareDataSources(ListShareDataSourcesQuery query) {
		ShareDataSourcesDomainEntity entity = new ShareDataSourcesDomainEntity();
		entity.setAppHandleCode(query.getAppHandleCode());
		entity.setAppName(query.getAppName());
		entity.setPlatformType(query.getPlatformType());
		entity.setConnState(query.getConnState());

		return shareDataSourcesDomainService.listShareDataSources(entity, query.getStartTime(), query.getEndTime(),
				query.getCurrent(), query.getSize());
	}

	@Override
	public ShareDataSourcesDomainEntity queryShareDataSourcesDetail(String id) {
		return shareDataSourcesDomainService.queryShareDataSourcesDetail(id);
	}

	@Override
	public void updateShareDataSources(UpdateShareDataSourcesCommand command) {
		shareDataSourcesDomainService.updateShareDataSources(command.getPlatformType(), command.getAppHandleCode(), JSONUtil.toJsonStr(command.getItems()), command.getId());
	}

	@Override
	public void deleteShareDataSources(Long id) {
		shareDataSourcesDomainService.deleteShareDataSources(id);
	}

}
