package cn.teleinfo.ds.business.application.query;

import cn.teleinfo.ds.common.core.util.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ListShareChannelsQuery extends PageRequest {

	/**
	 * 通道名
	 */
	private String channelName;

	/**
	 * 所属标识
	 */
	private String handle;

	/**
	 * 探测状态
	 */
	private String detectionStatus;

	/**
	 * 开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 应用编码
	 */
	private String appHandle;

	/**
	 * 用户ID
	 */
	private Long userId;

}
