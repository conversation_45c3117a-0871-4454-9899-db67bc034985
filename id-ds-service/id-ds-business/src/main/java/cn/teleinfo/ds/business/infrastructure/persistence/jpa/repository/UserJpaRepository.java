package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.UserEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.UserAppView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.UserInfoView;
import feign.Param;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserJpaRepository extends BaseRepository<UserEntity, Long> {

	@Query(
			nativeQuery = true,
			value = """
					SELECT
					  u.`user_id` AS `userId`,
					  u.`username` AS `userName`,
					  u.`password` AS `password`,
					  u.`uc_open_id` AS `ucOpenId`,
					  u.`salt` AS `salt`,
					  u.`phone` AS `phone`,
					  u.`avatar` AS `avatar`,
					  u.`nickname` AS `nickName`,
					  u.`name` AS `name`,
					  u.`email` AS `email`,
					  u.`dept_id` AS `deptId`,
					  u.`create_by` AS `createBy`,
					  u.`update_by` AS `updateBy`,
					  u.`create_time` AS `createTime`,
					  u.`update_time` AS `updateTime`,
					  u.`lock_flag` AS `lockFlag`,
					  u.`del_flag` AS `delFlag`,
					  u.`wx_openid` AS `wxOpenid`,
					  u.`mini_openid` AS `miniOpenid`,
					  u.`qq_openid` AS `qqOpenid`,
					  u.`gitee_login` AS `giteeLogin`,
					  u.`osc_id` AS `oscId`,
					  a.app_id AS appId
					FROM `sys_user` u
					LEFT JOIN sys_user_app a ON u.user_id = a.user_id
					WHERE u.`username` = :username and u.del_flag = 0
					""")
	List<UserInfoView> findInfoById(@Param("username") String username);


	@Query(
			nativeQuery = true,
			value = """
					SELECT
					u.`user_id` AS `userId`,
					  u.app_id AS appId
					FROM `sys_user_app` u
					WHERE u.`user_id` = :userId 
					""")
	List<UserAppView> findUserAppByUserId(@Param("userId")Long userId);
}