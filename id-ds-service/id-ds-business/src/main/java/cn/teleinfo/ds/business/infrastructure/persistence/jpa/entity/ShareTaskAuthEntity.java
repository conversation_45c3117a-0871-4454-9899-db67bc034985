package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

/**
 * 共享任务授权表
 */
@Getter
@Setter
@Entity
@Table(name = "t_share_task_auth")
@SQLDelete(sql = "update t_share_task_auth set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class ShareTaskAuthEntity extends AuditableEntity {

	/**
	 * 共享任务申请id
	 * <p>
	 * t_share_task_applications
	 */
	@Column(name = "share_task_applications_id")
	private Long shareTaskApplicationsId;

	/**
	 * 任务名称
	 */
	@Column(name = "task_name")
	private String taskName;

	/**
	 * 任务编号
	 */
	@Column(name = "task_no")
	private String taskNo;

	/**
	 * 任务类型(1：手动任务 2：定时任务)
	 */
	@Column(name = "task_type")
	private Integer taskType;

	/**
	 * 授权状态( 1:申请中 2:已驳回 3:已授权)
	 */
	@Column(name = "auth_status")
	private Integer authStatus;

	/**
	 * 提交人
	 */
	@Column(name = "committed_by")
	private Long committedBy;

	/**
	 * 共享源类型
	 */
	@Column(name = "source_type")
	private String sourceType;

	/**
	 * 省级前缀
	 */
	@Column(name = "province_prefix")
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	@Column(name = "ent_prefix")
	private String entPrefix;

	/**
	 * 应用身份编码
	 */
	@Column(name = "app_handle_code")
	private String appHandleCode;

	/**
	 * 审核人ID
	 */
	@Column(name = "audit_user_id")
	private Long auditUserId;

	/**
	 * 审核备注
	 */
	@Column(name = "audit_remark")
	private String auditRemark;

	/**
	 * 目标源ID
	 */
	@Column(name = "target_id")
	private Long targetId;

	/**
	 * CRON表达式
	 */
	@Column(name = "cron_expression")
	private String cronExpression;
}