package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareSourceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareDataSourcesView;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.time.LocalDateTime;
import java.util.List;


public interface ShareDataSourcesRepository {

	ShareDataSourcesDomainEntity findById(Long id);

	/**
	 * 创建共享数据源
	 *
	 * @param entity 共享数据源信息
	 */
	void createShareDataSources(ShareSourceEntity entity);

	/**
	 * 查询共享源列表
	 */
	PageResponse<ShareSourceDTO> listShareDataSources(ShareDataSourcesDomainEntity entity, List<String> userHandleCodes, LocalDateTime start, LocalDateTime end, Integer current, Integer size);

	ShareDataSourcesDomainEntity findByAppHandleCode(String appHandleCode);

	ShareDataSourcesView queryShareDataSourcesDetail(String id);

	void updateShareDataSources(ShareDataSourcesDomainEntity entity);

	void deleteShareDataSources(Long id);

	List<ShareDataSourcesDomainEntity> findAllByAppHandleCode(String appCode);

	List<ShareDataSourcesDomainEntity> findAllByAppHandleCodeAndPlatformType(String appHandleCode, Integer platformType);
}
