package cn.teleinfo.ds.business.domain.model.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class TargetSourceDomainEntity {
	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 目标源名称
	 */
	private String targetSourceName;

	/**
	 * 平台类型 0 华为 1 阿里 2 自建
	 */
	private Integer platformType;

	/**
	 * 应用标识
	 */
	private String appHandleCode;

	/**
	 * 配置内容
	 */
	private String items;

	/**
	 * 配置内容序列化后
	 */
	private TargetSourceItems targetSourceItems;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;


	/**
	 * 创建人
	 */
	private Long createBy;
	private String createByName;

	/**
	 * 修改人
	 */
	private Long updateBy;
	private String updateByName;
}
