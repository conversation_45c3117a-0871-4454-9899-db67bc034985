package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.command.CreateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.query.ListShareDataSourcesQuery;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareDataSourcesDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.common.core.util.PageResponse;

public interface ShareDataSourcesApplicationService {

	/**
	 * 创建共享数据源
	 *
	 * @param command 共享数据源信息
	 */
	void createShareDataSources(CreateShareDataSourcesCommand command);

	/**
	 * 查询共享源列表
	 */
	PageResponse<ShareSourceDTO>  listShareDataSources(ListShareDataSourcesQuery query);

	ShareDataSourcesDomainEntity queryShareDataSourcesDetail(String id);

	void updateShareDataSources(UpdateShareDataSourcesCommand command);

	void deleteShareDataSources(Long id);
}
