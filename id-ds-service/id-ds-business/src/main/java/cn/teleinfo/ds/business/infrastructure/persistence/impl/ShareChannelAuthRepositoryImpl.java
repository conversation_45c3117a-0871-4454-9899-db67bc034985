package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannelAuthDomainEntity;
import cn.teleinfo.ds.business.domain.repository.ShareChannelAuthRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelAuthEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareChannelAuthJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@AllArgsConstructor
@Component
public class ShareChannelAuthRepositoryImpl implements ShareChannelAuthRepository {

	private final ShareChannelAuthJpaRepository shareChannelAuthJpaRepository;

	@Override
	public List<ShareChannelsVersionAuthDTO> queryShareChannelsVersionAuthDTO(String shareChannelId) {
		return shareChannelAuthJpaRepository.queryShareChannelsVersionAuthDTO(shareChannelId);
	}

	@Override
	public List<ShareChannelsVersionAuthDTO> queryShareChannelsVersionAuthDTO(String shareChannelId, String main_version, String minor_version) {
		return shareChannelAuthJpaRepository.queryShareChannelsVersionAuthDTO(shareChannelId, main_version, minor_version);
	}

	@Override
	public void save(ShareChannelAuthDomainEntity shareChannelAuthDomainEntity) {
		var entity = BeanUtil.copyProperties(shareChannelAuthDomainEntity, ShareChannelAuthEntity.class);
		shareChannelAuthJpaRepository.save(entity);
	}

}
