package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ShareTaskApplicationsResponse {

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务类型(1：手动任务 2：定时任务)
	 */
	private Integer taskType;

	/**
	 * 任务状态( 1:申请中 2:已驳回 3:已授权)
	 */
	private Integer taskStatus;

	/**
	 * 提交人
	 */
	private Long committedBy;

	/**
	 * 提交时间
	 */
	private String createdTime;
}
