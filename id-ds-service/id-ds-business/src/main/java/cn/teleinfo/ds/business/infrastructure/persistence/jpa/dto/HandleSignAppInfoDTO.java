package cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class HandleSignAppInfoDTO {

	/**
	 * id
	 */
	private Long id;

	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 所属企业
	 */
	private String entName;

	/**
	 * 应用前缀
	 */
	private String handlePrefix;

	/**
	 * 应用身份标识
	 */
	private String handleCode;

	/**
	 * 部署地址
	 */
	private String deployAddress;

	/**
	 * 系统版本
	 */
	private String sysVersion;

	/**
	 * 更新时间
	 */
	private Timestamp updatedTime;

}
