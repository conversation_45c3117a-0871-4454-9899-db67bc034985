package cn.teleinfo.ds.business.interfaces.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UpdatePlatformConnectionRequest {
	/**
	 * 平台类型 0 华为云 1 阿里云 2 自建
	 */
	@NotNull(message = "平台类型不能为空")
	private Integer platformType;

	/**
	 * 连接信息
	 */
	@NotNull(message = "连接信息不能为空")
	private HcsPlatformConnectionRequest platformConnection;
}
