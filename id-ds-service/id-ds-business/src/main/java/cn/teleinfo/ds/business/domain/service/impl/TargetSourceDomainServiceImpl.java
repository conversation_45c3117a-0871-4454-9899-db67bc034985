package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplications;
import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceItems;
import cn.teleinfo.ds.business.domain.repository.*;
import cn.teleinfo.ds.business.domain.service.PlatformConnectionDomainService;
import cn.teleinfo.ds.business.domain.service.TargetSourceDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.TargetSourceDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.TargetSourceView;
import cn.teleinfo.ds.common.core.constant.UserConstants;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@AllArgsConstructor
public class TargetSourceDomainServiceImpl implements TargetSourceDomainService {

	private final TargetSourceRepository targetSourceRepository;

	private final PlatformConnectionDomainService platformConnectionDomainService;

	private final ShareTaskApplicationsRepository shareTaskApplicationsRepository;

	private final SharedTaskRepository sharedTaskRepository;

	private final HcsRepository hcsRepository;

	private final AppInfoRepository appInfoRepository;

	private final RoleService roleService;


	/**
	 * 保存目标源
	 *
	 * @param targetSource 目标源聚合体
	 */
	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public Long save(TargetSource targetSource) {
		TargetSourceDomainEntity entity = targetSource.getTargetSourceDomainEntity();

		Integer count = targetSourceRepository.findByAppHandleCodeAndTargetSourceNameCount(
				entity.getAppHandleCode(), entity.getTargetSourceName());
		if (count > 0) {
			throw new CheckedException("目标源名称重复！");
		}

		return targetSourceRepository.save(entity);
	}

	@Override
	public PageResponse<TargetSourceDomainEntity> listTargetSource(TargetSource targetSource, LocalDateTime start,
																   LocalDateTime end, Integer page, Integer size) {
		TargetSourceDomainEntity entity = targetSource.getTargetSourceDomainEntity();
		List<String> userHandleCodes = null;
		Long id = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(id);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				//获取该用户关联的所有应用
				List<String> handleCodes = appInfoRepository.findHandleCodeByUserId(id);
				if (!handleCodes.isEmpty()) {
					userHandleCodes = handleCodes;
				}
			}
		}
		return targetSourceRepository.listTargetSource(entity.getAppHandleCode(), entity.getTargetSourceName(),
				entity.getPlatformType(), userHandleCodes, start, end, page, size);
	}

	@Override
	public TargetSourceDomainEntity findById(Long id) {
		TargetSourceDomainEntity targetSource = targetSourceRepository.findById(id);
		if (targetSource == null) {
			throw new CheckedException("目标源不存在或已删除！");
		}
		return targetSource;
	}

	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void updateTargetSource(TargetSourceDomainEntity entity) {
		// 当前登录人
		Long userId = SecurityUtils.getUser().getId();

		// 验证应用下目标源名称唯一。
		List<TargetSourceDomainEntity> shareDatasource = targetSourceRepository
				.findAllByAppHandleCodeAndTargetSourceName(entity.getAppHandleCode(), entity.getTargetSourceName())
				.stream().filter(e -> !entity.getId().equals(e.getId())).toList();
		if (!shareDatasource.isEmpty()) {
			throw new CheckedException("目标源名称重复！");
		}

		TargetSourceDomainEntity targetSourceDomainEntity = targetSourceRepository.findById(entity.getId());
		targetSourceDomainEntity.setAppHandleCode(entity.getAppHandleCode());
		targetSourceDomainEntity.setTargetSourceName(entity.getTargetSourceName());
		targetSourceDomainEntity.setPlatformType(entity.getPlatformType());
		targetSourceDomainEntity.setItems(entity.getItems());
		targetSourceDomainEntity.setUpdateBy(userId);
		targetSourceDomainEntity.setUpdateTime(LocalDateTime.now());
		targetSourceRepository.save(targetSourceDomainEntity);
	}

	@Override
	public void delTargetSource(Long id) {
		List<ShareTaskApplications> shareTaskApplications = shareTaskApplicationsRepository
				.findShareTaskApplicationsByTargetSourceIdAndStatus(id, 1);
		if (!shareTaskApplications.isEmpty()) {
			throw new CheckedException("该目标源已绑定任务，无法进行删除！");
		}

		List<SharedTaskDomainEntity> sharedTaskDomainEntityList = sharedTaskRepository.findAllByTargetSourceId(id);
		if (!sharedTaskDomainEntityList.isEmpty()) {
			throw new CheckedException("该目标源已绑定任务，无法进行删除！");
		}

		targetSourceRepository.delTargetSource(id);
	}

	@Override
	public TargetSourceDetailDTO queryTargetSourceDetail(String id) {
		TargetSourceView targetSourceView = targetSourceRepository.queryTargetSourceDetail(id);
		TargetSourceDetailDTO targetSourceDetailDTO = new TargetSourceDetailDTO();
		targetSourceDetailDTO.setId(targetSourceView.getId());
		targetSourceDetailDTO.setTargetSourceName(targetSourceView.getTargetSourceName());
		targetSourceDetailDTO.setAppName(targetSourceView.getAppName());
		targetSourceDetailDTO.setAppCode(targetSourceView.getAppCode());
		targetSourceDetailDTO.setPlatformType(targetSourceView.getPlatformType());
		targetSourceDetailDTO.setUpdateTime(targetSourceView.getUpdateTime());
		targetSourceDetailDTO.setUpdateUser(targetSourceView.getUpdateUser());

		TargetSourceItems targetSourceItems = JSONUtil.toBean(targetSourceView.getItems(), TargetSourceItems.class);

		targetSourceDetailDTO.setConnType(targetSourceItems.getConnType());
		targetSourceDetailDTO.setDatabaseUrl(targetSourceItems.getDatabaseUrl());
		targetSourceDetailDTO.setPort(targetSourceItems.getPort());
		targetSourceDetailDTO.setDatabaseName(targetSourceItems.getDatabaseName());
		targetSourceDetailDTO.setDatabaseUsername(targetSourceItems.getDatabaseUsername());
		targetSourceDetailDTO.setDatabasePassword(targetSourceItems.getDatabasePassword());

		return targetSourceDetailDTO;
	}

	@Override
	public List<TargetSourceDomainEntity> findByAppHandleCode(String appHandleCode) {
		return targetSourceRepository.findByAppHandleCode(appHandleCode);
	}
}
