package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.entity.ProvincePrefixDomainEntity;
import cn.teleinfo.ds.business.domain.repository.ProvincePrefixRepository;
import cn.teleinfo.ds.business.domain.service.ProvincePrefixDomainService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ProvincePrefixDomainServiceImpl implements ProvincePrefixDomainService {
	private final ProvincePrefixRepository provincePrefixRepository;

	@Override
	public ProvincePrefixDomainEntity findByProvincePrefix(String provincePrefix) {
		return provincePrefixRepository.findByProvincePrefix(provincePrefix);
	}
}
