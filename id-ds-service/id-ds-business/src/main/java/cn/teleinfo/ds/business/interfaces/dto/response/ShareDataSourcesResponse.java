package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ShareDataSourcesResponse {
	private Long id;

	/**
	 * 应用名称
	 * t_connection platform_type
	 */
	private String appName;

	/**
	 * 数据源类型
	 * t_connection platform_type
	 */
	private Integer sourceType;


	/**
	 * 连接状态 0 异常 1 正常
	 */
	private Long connState;

	/**
	 * 操作时间
	 */
	private LocalDateTime opTime;

	/**
	 * 操作人id
	 */
	private Long opUserId;

	/**
	 * 操作人 username
	 */
	private String opUserName;

	/**
	 * 操作人昵称
	 */
	private String opNickName;
}
