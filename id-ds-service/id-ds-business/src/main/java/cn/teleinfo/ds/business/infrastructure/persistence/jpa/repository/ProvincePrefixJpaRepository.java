package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ProvincePrefixEntity;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ProvincePrefixJpaRepository extends BaseRepository<ProvincePrefixEntity, Long> {
	ProvincePrefixEntity findByProvincePrefix(@Param("provincePrefix") String provincePrefix);
}
