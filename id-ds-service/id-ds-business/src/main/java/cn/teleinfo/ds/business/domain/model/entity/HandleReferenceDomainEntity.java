package cn.teleinfo.ds.business.domain.model.entity;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class HandleReferenceDomainEntity {

	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除: 0 未删除 null 已删除
	 */
	private Integer isDeleted;

	/**
	 * 被关联标识
	 */
	private String referenceHandle;

	/**
	 * 被关联属性
	 */
	private String referenceHandleProp;

	/**
	 * 关联标识属性索引
	 */
	private Integer referenceHandlePropIndex;

	/**
	 * 查询属性
	 */
	private String queryProp;

	/**
	 * 查询属性索引
	 */
	private Integer queryPropIndex;

	/**
	 * 参数属性
	 */
	private String paramProp;

	/**
	 * 参数属性索引
	 */
	private Integer paramPropIndex;

	/**
	 * 标识属性ID
	 */
	private Long handleItemId;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 应用身份编码
	 */
	private String appHandleCode;
}
