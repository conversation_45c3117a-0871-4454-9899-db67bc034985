package cn.teleinfo.ds.business.domain.model.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.AppInfoEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AppInfoTreeDomainEntity {
	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 组织机构名称
	 */
	private String orgName;

	/**
	 * 组织机构编码
	 */
	private String orgCode;

	/**
	 * 省份前缀
	 */
	private String provincePrefix;

	private List<AppInfoEntity> appInfo = new ArrayList<>();
}
