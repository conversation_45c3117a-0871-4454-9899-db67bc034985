package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ShareChannelsResponse {

	/**
	 * id
	 */
	private String id;

	/**
	 * 共享通道id
	 */
	private String shareChannelId;

	/**
	 * 数据通道id
	 */
	private String dataChannelId;

	/**
	 * 共享通道名称
	 */
	private String shareChannelName;

	/**
	 * 所属标识
	 */
	private String handle;

	/**
	 * 实例数据类型
	 */
	private Integer dataType;

	/**
	 * 版本
	 */
	private String version;

	/**
	 * 主版本号
	 */
	//private Integer mainVersion;

	/**
	 * 次版本号
	 */
	//private Integer minorVersion;

	/**
	 * 操作时间
	 */
	private LocalDateTime updatedTime;

	/**
	 * 探测状态
	 */
	private Integer detectionStatus;

	/**
	 * 生效状态
	 */
	private Integer channelStatus;

}
