package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.domain.model.aggregate.ShareHandle;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareHandleView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsDTOView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsXqDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShareTaskApplicationsJpaRepository extends BaseRepository<ShareTaskApplicationsEntity, Long> {

	@Query(nativeQuery = true, value = """
			SELECT
			    s.id,
			    s.task_name AS taskName,
			    s.task_no AS taskNo,
			    s.task_type AS taskType,
			    s.applications_status AS applicationsStatus,
			     s.update_time AS updatedTime,
			    u.`name` AS committedUser,
				a.app_name as appHandleName,
				u2.`name` AS updateByName
			FROM t_share_task_applications s
			LEFT JOIN sys_user u ON s.create_by = u.user_id and u.del_flag = '0' 
			LEFT JOIN t_app_info a ON s.app_handle_code = a.handle_code and a.is_deleted = 0 
			LEFT jOIN sys_user u2 ON s.update_by = u2.user_id and u2.del_flag = '0' 
			WHERE IF(:taskName != '' AND :taskName is not null, s.task_name like CONCAT('%',:taskName,'%'), 1=1 )
			and IF(:taskNo != '' AND :taskNo is not null, s.task_no like CONCAT('%',:taskNo,'%'), 1=1 )
			and IF(:taskType IS NOT NULL, s.task_type = :taskType, 1=1 )
			and IF(:applicationsStatus IS NOT NULL, s.applications_status = :applicationsStatus, 1=1 )
			AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, s.update_time BETWEEN :startTime AND :endTime , 1=1)
			AND IF(:appHandleCode IS NOT NULL, s.app_handle_code like CONCAT('%',:appHandleCode,'%'), 1=1)
			AND IF(COALESCE(:userHandleCode) IS NOT NULL, s.app_handle_code in (:userHandleCode), 1=1)
			AND s.is_deleted = 0
			order by s.update_time desc
			""", countQuery = """
			SELECT
				count(*)
				FROM t_share_task_applications s
				LEFT JOIN sys_user u ON s.update_by = u.user_id and u.del_flag = '0' 
				LEFT JOIN t_app_info a ON s.app_handle_code = a.handle_code and a.is_deleted = 0 
				LEFT jOIN sys_user u2 ON s.update_by = u2.user_id and u2.del_flag = '0' 
					WHERE IF(:taskName != '' AND :taskName is not null, s.task_name like CONCAT('%',:taskName,'%'), 1=1 )
					and IF(:taskNo != '' AND :taskNo is not null, s.task_no like CONCAT('%',:taskNo,'%'), 1=1 )
					and IF(:taskType IS NOT NULL, s.task_type = :taskType, 1=1 )
					and IF(:applicationsStatus IS NOT NULL, s.applications_status = :applicationsStatus, 1=1 )
					AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, s.update_time BETWEEN :startTime AND :endTime , 1=1)
					AND IF(:appHandleCode IS NOT NULL, s.app_handle_code like CONCAT('%',:appHandleCode,'%'), 1=1)
					AND IF(COALESCE(:userHandleCode) IS NOT NULL, s.app_handle_code in (:userHandleCode), 1=1)
					AND s.is_deleted = 0
				""")
	Page<ShareTaskApplicationsView> findShareTaskApplications(@Param("taskName") String taskName,
															  @Param("taskNo") String taskNo,
															  @Param("taskType") Integer taskType,
															  @Param("applicationsStatus") Integer applicationsStatus,
															  @Param("startTime") LocalDateTime startTime,
															  @Param("endTime") LocalDateTime endTime,
															  @Param("appHandleCode") String appHandleCode,
															  @Param("userHandleCode") List<String> userHandleCode,
															  Pageable pageable);

	@Query(nativeQuery = true, value = """
			SELECT
   			    s.id,
   			    s.task_name AS taskName,
   			    s.task_no AS taskNo,
   			    s.task_type AS taskType,
   			    s.target_source_id as targetSourceId,
   			    s.applications_status AS applicationsStatus,
   			     DATE_FORMAT(s.update_time, '%Y-%m-%d %H:%i:%s') AS updateTime,
   			    u.`name` AS updateByName,
   			    S.app_handle_code as appHandleCode,
   			    s.ent_prefix as entPrefix,
   				s.update_time AS updateTime,
   				s.graph as graph,
   				a.app_name AS appName,
   				b.org_name AS entName,
   				c.target_source_name as targetSource,
   				c.items as items,
   				s.cron_expression as cronExpression,
   				d.audit_remark as auditRemark,\s
   				u2.`name` AS committedUser \s
   			FROM t_share_task_applications s
   			LEFT JOIN sys_user u2 ON s.create_by = u2.user_id and u2.del_flag = '0'\s
   			LEFT JOIN t_app_info a ON s.app_handle_code = a.handle_code and a.is_deleted = 0\s
   			LEFT JOIN t_ent_prefix b ON s.ent_prefix = b.ent_prefix\s
   			LEFT JOIN t_target_source c ON s.target_source_id = c.id and c.is_deleted = 0 \s
   			LEFT JOIN t_share_task_auth d ON s.id = d.share_task_applications_id and d.is_deleted = 0\s
   			left join  sys_user u3 on s.create_by = u3.user_id and u3.del_flag = '0'\s
   			left join t_share_task_auth sta on s.id = sta.share_task_applications_id\s
   			LEFT JOIN sys_user u ON sta.audit_user_id = u.user_id and u.del_flag = '0' 
			WHERE s.id = :id
			AND s.is_deleted = 0
			""")
	ShareTaskApplicationsDTOView findShareTaskApplicationsById(@Param("id") Long id);

    List<ShareTaskApplicationsEntity> findAllByTargetSourceIdAndApplicationsStatus(Long targetSourceId, Integer applicationsStatus);


	@Query(nativeQuery = true, value = """

			select
				distinct
				stad.id,
				ep.org_name as entName,
				pp.org_name as provinceName,
				ai.app_name as appName,
				h.name handleName,
				h.handle
			  from
				t_share_task_applications_details stad
			  left join t_ent_prefix ep on
				ep.ent_prefix = stad.ent_prefix
			  left join t_province_prefix pp on
				pp.province_prefix = stad.province_prefix
			  left join t_app_info ai on
				ai.handle_code = stad.app_handle_code
			  left join t_handle h on
				h.handle = stad.handle
				where stad.id = :id
			    """
	)
	ShareHandleView findShareHandleByHandle(@Param("id") Long id);
}