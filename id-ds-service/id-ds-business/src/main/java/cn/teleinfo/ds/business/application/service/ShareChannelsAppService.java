package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.command.CreateShareChannelVersionCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareChannelsApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ListShareChannelApplicationsQuery;
import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsApplicationsDetail;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsVersion;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannelDetectLog;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.util.List;

public interface ShareChannelsAppService {

	/**
	 * 共享通道列表查询
	 */
	PageResponse<ShareChannelsDTO> listShareChannels(ListShareChannelsQuery query);

	/**
	 * 共享通道详情查询
	 */
	ShareChannelsDetailsDTO queryShareChannelsDetails(String id);

	/**
	 * 共享通道版本清单
	 */
	List<ShareChannelsDetailsDTO> listShareChannelsVersion(String shareChannelId);

	/**
	 * 共享通道版本sql查询
	 */
	ShareChannelsVersionSqlView queryVersionSql(String shareChannelId, String version);

	/**
	 * 共享通道版本
	 */
	List<ShareChannelsVersion> shareChannelsVersion(String shareChannelId);

	/**
	 * 共享通道审核列表查询
	 */
	PageResponse<ShareChannelsApplicationsView> listShareChannelsApplications(ListShareChannelApplicationsQuery query);

	/**
	 * 共享通道审核详情查询
	 */
	ShareChannelsApplicationsDetail queryShareChannelsApplicationDetails(String applicationId);

	/**
	 * 共享通道定时生成
	 */
	void createShareChannels();

	/**
	 * 共享通道手动生成
	 */
	void createShareChannel(Long shareChannelId);

	/**
	 * 共享通道审核
	 */
	void reviewShareChannels(String applicationId, UpdateShareChannelsApplicationsCommand command);

	/**
	 * 共享通道维护版本信息
	 */
	List<String> queryChannelVersions(String shareChannelId);

	/**
	 * 共享通道维护保存
	 */
	void shareChannelVersionSave(CreateShareChannelVersionCommand command);

	/**
	 * 共享通道版本切换
	 */
	void shareChannelVersionChange(String shareChannelId, String version);

	void detectShareChannels();

	ShareChannelDetectLog getChannelLog(Long shareChannelId);

	void detectShareChannel(Long id);
}
