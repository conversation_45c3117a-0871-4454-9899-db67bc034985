package cn.teleinfo.ds.business.domain.model.entity;

public enum AuthStatus {
	// 授权状态( 1:申请中 2:已驳回 3:已授权)

	PASS(3),
	REJECT(2);

	private final int code;

	AuthStatus(int code) {
		this.code = code;
	}

	public int code() {
		return code;
	}

	public static AuthStatus findByCode(int code) {
		for (AuthStatus value : AuthStatus.values()) {
			if (value.code() == code) {
				return value;
			}
		}

		throw new IllegalArgumentException("无效的授权状态: " + code);
	}
}
