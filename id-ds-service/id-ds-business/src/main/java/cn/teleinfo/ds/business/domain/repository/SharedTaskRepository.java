package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskBasicInfoValueDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskShareDataValueDTO;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.time.LocalDateTime;
import java.util.List;

public interface SharedTaskRepository {

	Long save(SharedTaskDomainEntity sharedTaskDomainEntity);

	PageResponse<SharedTaskDomainEntity> listSharedTask(SharedTaskDomainEntity entity,
														LocalDateTime start, LocalDateTime end, Integer page, Integer size,
														String taskCode, String appHandleCode, List<String> userHandleCodes,LocalDateTime editStart, LocalDateTime editEnd);

	SharedTaskDomainEntity findById(Long id);

    void deleteById(Long id);

	List<SharedTaskDomainEntity> findAllByTargetSourceId(Long id);

	SharedTaskBasicInfoValueDTO getSharedTaskBasicInfoValueDTO(Long id);
}
