package cn.teleinfo.ds.business.application.command;

import lombok.Data;

@Data
public class CreateShareDataSourcesItemsCommand {

	private String appHandleCode;
	private String appName;

	/**
	 * 资源空间
	 */
	private String projectId;
	private String projectName;

	/**
	 * 数据治理中心实例 id
	 */
	private String instanceId;
	private String instanceName;

	/**
	 * 工作空间
	 */
	private String workspace;
	private String workspaceName;

	/**
	 * 数据集成-集群
	 */
	private String clusterId;
	private String clusterName;

	/**
	 * 数据集成-贴源层连接名称
	 */
	private String stgConnId;
	private String stgConnName;
	/**
	 * 数据集成-贴源层数据库名称
	 */
	private String stgDatabaseId;
	private String stgDatabaseName;

	/**
	 * 数据集成-规范层连接名称
	 */
	private String stdDataConnId;
	private String stdDataConnName;
	/**
	 * 数据集成-规范层数据库名称
	 */
	private String stdDataDatabaseId;
	private String stdDataDatabaseName;
	/**
	 * 数据开发-规范层连接名称
	 */
	private String stdDataDayuConnId;
	private String stdDataDayuConnName;
	/**
	 * 数据开发-规范层数据库名称
	 */
	private String stdDataDayuDatabaseId;
	private String stdDataDayuDatabaseName;
}
