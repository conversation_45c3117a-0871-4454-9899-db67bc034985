package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareDataSourcesDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.time.LocalDateTime;

public interface ShareDataSourcesDomainService {
	void createShareDataSources(Integer platformType, String appHandleCode, String items);

	PageResponse<ShareSourceDTO> listShareDataSources(ShareDataSourcesDomainEntity entity, LocalDateTime start, LocalDateTime end,
													  Integer current, Integer size);

	ShareDataSourcesDomainEntity findByAppHandleCode(String appHandleCode);

	ShareDataSourcesDomainEntity queryShareDataSourcesDetail(String id);

	void updateShareDataSources(Integer platformType, String appHandleCode, String items, Long id);

	void deleteShareDataSources(Long id);
}
