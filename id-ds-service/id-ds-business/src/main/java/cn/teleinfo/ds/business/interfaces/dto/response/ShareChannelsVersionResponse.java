package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ShareChannelsVersionResponse {

	/**
	 * id
	 */
	private String id;

	/**
	 * 共享通道id
	 */
	private String shareChannelId;

	/**
	 * 数据通道id
	 */
	private String dataChannelId;

	/**
	 * 共享通道名称
	 */
	private String shareChannelName;

	/**
	 * 所属标识
	 */
	private String handle;

	/**
	 * 数据类型
	 */
	private String dataType;

	/**
	 * 版本
	 */
	private String version;

	/**
	 * 应用名
	 */
	private String appName;

	/**
	 * 企业名
	 */
	private String entName;

	/**
	 * 探测状态
	 */
	private String detectionStatus;

	/**
	 * 自动sql
	 */
	private String defaultSql;

	/**
	 * 手动sql
	 */
	private String customSql;

	/**
	 * 变更原因
	 */
	private String changeReason;

	/**
	 * 启用状态
	 */
	private Integer channelStatus;

	/**
	 * 任务状态
	 */
	private Integer taskStatus;

	/**
	 * 修改人
	 */
	private String editorUser;

	/**
	 * 修改时间
	 */
	private Timestamp updatedTime;

	/**
	 * 审核记录
	 */
	private List<ShareChannelAuthResponse> items;

}
