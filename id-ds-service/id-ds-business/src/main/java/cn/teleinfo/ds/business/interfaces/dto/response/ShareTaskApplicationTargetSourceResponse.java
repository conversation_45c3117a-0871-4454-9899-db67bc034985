package cn.teleinfo.ds.business.interfaces.dto.response;

import jakarta.persistence.Column;
import lombok.Data;

// 共享任务申请。选择目标源
@Data
public class ShareTaskApplicationTargetSourceResponse {

	/**
	 * 目标源 Id
	 */
	private Long id;

	/**
	 * 目标源名称
	 */
	private String targetSourceName;

	/**
	 * 平台类型 0 华为 1 阿里 2 自建
	 */
	private Integer platformType;

	/**
	 * 应用标识
	 */
	private String appHandleCode;

	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 企业名称
	 */
	private String entName;
}
