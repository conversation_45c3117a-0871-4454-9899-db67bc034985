package cn.teleinfo.ds.business.domain.model.entity;

public enum ExecutionType {
	// 执行类型：1-正式执行，2-测试执行

	FORMAL(1, "正式执行"),
	TEST(2, "测试执行");
	private final int code;
	private final String message;

	ExecutionType(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public int code() {
		return code;
	}

	public String message() {
		return message;
	}

	public static ExecutionType findByCode(int code) {
		for (ExecutionType value : ExecutionType.values()) {
			if (value.code() == code) {
				return value;
			}
		}

		throw new IllegalArgumentException("无效的执行类型: " + code);
	}
}
