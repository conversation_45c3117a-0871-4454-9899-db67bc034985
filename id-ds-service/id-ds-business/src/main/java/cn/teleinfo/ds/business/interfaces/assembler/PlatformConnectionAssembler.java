package cn.teleinfo.ds.business.interfaces.assembler;


import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.command.UpdateConnectionSettingCommand;
import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionDomainEntity;
import cn.teleinfo.ds.business.interfaces.dto.request.UpdatePlatformConnectionRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.HcsPlatformConnectionResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.PlatformConnectionResponse;
import org.springframework.stereotype.Component;

/**
 * 系统设置
 * 连接管理
 * 装配器
 */
@Component
public class PlatformConnectionAssembler {

	public PlatformConnectionResponse toResponse(PlatformConnectionDomainEntity conn) {
		var r = new PlatformConnectionResponse();
		r.setPlatformConnection(JSONUtil.toBean(conn.getPlatformConnection(), HcsPlatformConnectionResponse.class));
		r.setPlatformType(conn.getPlatformType().code());
		return r;
	}

	public UpdateConnectionSettingCommand toUpdateConnectionSettingCommand(UpdatePlatformConnectionRequest request) {
		UpdateConnectionSettingCommand command = new UpdateConnectionSettingCommand();
		command.setPlatformConnection(JSONUtil.toJsonStr(request.getPlatformConnection()));
		command.setPlatformType(request.getPlatformType());
		return command;
	}
}
