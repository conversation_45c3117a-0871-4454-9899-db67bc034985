package cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ShareDataSourcesDetailDTO {

	private String id;

	/**
	 * 共享源类型
	 */
	private String platformType;

	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 应用标识
	 */
	private String appCode;

	/**
	 * 资源空间
	 */
	private String projectId;

	/**
	 * 数据治理中心实例
	 */
	private String instanceId;

	/**
	 * 工作空间
	 */
	private String workspace;

	/**
	 * 数据集成-集群名称
	 */
	private String clusterId;

	/**
	 * 数据集成-贴源层连接名称
	 */
	private String stgConnName;

	/**
	 * 数据集成-贴源层数据库名称
	 */
	private String stgDatabaseName;

	/**
	 * 数据集成-规范层连接名称
	 */
	private String stdDataConnName;

	/**
	 * 数据集成-规范层数据库名称
	 */
	private String stdDataDatabaseName;

	/**
	 * 数据开发-规范层连接名称
	 */
	private String stdDataDayuConnName;

	/**
	 * 数据开发-规范层数据库名称
	 */
	private String stdDataDayuDatabaseName;

}
