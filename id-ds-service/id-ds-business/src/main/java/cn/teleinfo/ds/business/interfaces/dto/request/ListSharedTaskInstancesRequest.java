package cn.teleinfo.ds.business.interfaces.dto.request;

import cn.teleinfo.ds.common.core.util.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ListSharedTaskInstancesRequest  extends PageRequest {

	private  Long sharedTaskId;

	private String taskInstanceNo;

	private String taskInstanceName;

	private Integer executionType;

	private Integer runStatus;

	private String taskNo;

	private String taskName;

	private String appHandleCode;

	private String userHandleCode;

	private LocalDateTime startTime;

	private LocalDateTime endTime;
}
