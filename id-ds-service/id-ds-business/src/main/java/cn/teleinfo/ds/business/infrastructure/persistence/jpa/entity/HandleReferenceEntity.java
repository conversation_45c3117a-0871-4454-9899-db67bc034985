package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;

/**
 * 标识属性关联信息
 */
@Getter
@Setter
@Entity
@Table(name = "t_handle_reference")
@SQLDelete(sql = "update t_handle_reference set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class HandleReferenceEntity extends BaseEntity {

    /**
     * 被关联标识
     */
    @Column(name = "reference_handle")
    private String referenceHandle;

    /**
     * 被关联属性
     */
    @Column(name = "reference_handle_prop")
    private String referenceHandleProp;

    /**
     * 关联标识属性索引
     */
    @Column(name = "reference_handle_prop_index")
    private Integer referenceHandlePropIndex;

    /**
     * 查询属性
     */
    @Column(name = "query_prop")
    private String queryProp;

    /**
     * 查询属性索引
     */
    @Column(name = "query_prop_index")
    private Integer queryPropIndex;

    /**
     * 参数属性
     */
    @Column(name = "param_prop")
    private String paramProp;

    /**
     * 参数属性索引
     */
    @Column(name = "param_prop_index")
    private Integer paramPropIndex;

    /**
     * 标识属性ID
     */
    @Column(name = "handle_item_id")
    private Long handleItemId;

    /**
     * 省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * 应用身份编码
     */
    @Column(name = "app_handle_code")
    private String appHandleCode;
}