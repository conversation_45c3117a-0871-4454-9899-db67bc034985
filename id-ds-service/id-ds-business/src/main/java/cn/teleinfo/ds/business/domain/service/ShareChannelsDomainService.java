package cn.teleinfo.ds.business.domain.service;

import ch.qos.logback.classic.Logger;
import cn.teleinfo.ds.business.application.command.CreateShareChannelVersionCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareChannelsApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ListShareChannelApplicationsQuery;
import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsApplicationsDetail;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsVersion;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannelDetectLog;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.nio.file.Path;
import java.util.List;

public interface ShareChannelsDomainService {

	PageResponse<ShareChannelsDTO> listShareChannels(ListShareChannelsQuery query);

	ShareChannelsDetailsDTO queryDetailsById(String id);

	List<ShareChannelsDetailsDTO> listShareChannelsVersion(String shareChannelId);

	ShareChannelsVersionSqlView queryVersionSql(String shareChannelId, String version);

	List<ShareChannelsVersion> shareChannelsVersion(String shareChannelId);

	PageResponse<ShareChannelsApplicationsView> listShareChannelsApplications(ListShareChannelApplicationsQuery query);

	ShareChannelsApplicationsDetail queryShareChannelsApplicationDetails(String applicationId);

	void createShareChannels();

	void createShareChannel(Long shareChannelId);

	void reviewShareChannels(String applicationId, UpdateShareChannelsApplicationsCommand command);

	List<String> queryChannelVersions(String shareChannelId);

	void shareChannelVersionSave(CreateShareChannelVersionCommand command);

	ShareChannel checkShareChannelVersionChange(String shareChannelId, String version);

	void shareChannelVersionChange(String shareChannelId, String version, ShareChannel enableChannel);

	ShareChannel findEnableChannel(Long id);
	ShareChannel findEnableChannelByDataChannelId(Long dataChannelId);

	void detectShareChannels();

	/**
	 * 还原通道内的表
	 */
	String shareChannelConnected(List<ShareChannel> channels);

	String shareChannelConnected4TaskExecute(List<ShareChannel> channels, List<Path> path);

	ShareChannelDetectLog getChannelLog(Long shareChannelId);

	String detectShareChannel(Long id);
}
