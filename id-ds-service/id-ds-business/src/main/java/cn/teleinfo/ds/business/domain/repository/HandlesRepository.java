package cn.teleinfo.ds.business.domain.repository;


import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskView;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.time.LocalDateTime;
import java.util.List;

public interface HandlesRepository {

	PageResponse<HandleListView> listHandles(String appHandleCode, String name, String handle, List<String> userHandleCode, Integer page, Integer size, LocalDateTime startTime, LocalDateTime entTime);

	HandleDomainEntity findByHandle(String handle);

	SharedTaskView getHandleDetails(String handle);

	HandleListView findHandleDetailById(Long id);
}
