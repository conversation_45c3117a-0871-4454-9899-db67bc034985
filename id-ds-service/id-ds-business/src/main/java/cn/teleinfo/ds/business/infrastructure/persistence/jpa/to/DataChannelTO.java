package cn.teleinfo.ds.business.infrastructure.persistence.jpa.to;


import java.time.LocalDateTime;


public interface DataChannelTO {

	Long getId();

	Long getSourceId();

	String getDataChannelName();

	String getObjectHandle();

	Integer getItemType();

	Long getDataChannelId();

	Integer getDataType();

	String getResolveSql();

	String getQuerySql();

	String getProvincePrefix();

	String getEntPrefix();

	String getAppHandleCode();

	Integer getIsShare();

	LocalDateTime getCreateTime();

	LocalDateTime getUpdateTime();

	Integer getIsDeleted();

}
