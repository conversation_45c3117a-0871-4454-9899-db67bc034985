package cn.teleinfo.ds.business.application.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.command.CreateTargetSourceCommand;
import cn.teleinfo.ds.business.application.command.UpdateTargetSourceCommand;
import cn.teleinfo.ds.business.application.query.ListTargetSourceQuery;
import cn.teleinfo.ds.business.application.service.TargetSourceApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceItems;
import cn.teleinfo.ds.business.domain.model.valueobject.AppHandleCode;
import cn.teleinfo.ds.business.domain.service.HcsDomainService;
import cn.teleinfo.ds.business.domain.service.PlatformConnectionDomainService;
import cn.teleinfo.ds.business.domain.service.TargetSourceDomainService;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.HcsBaseResponse;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.TargetSourceDetailDTO;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.checkerframework.checker.units.qual.A;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class TargetSourceApplicationServiceImpl implements TargetSourceApplicationService {

	private final TargetSourceDomainService targetSourceDomainService;
	private final PlatformConnectionDomainService platformConnectionDomainService;
	private final HcsDomainService hcsDomainService;

	/**
	 * 创建目标源
	 *
	 * @param command command
	 */
	@Override
	@Transactional
	public void createTargetSource(CreateTargetSourceCommand command) {
		TargetSourceDomainEntity entity = BeanUtil.copyProperties(command, TargetSourceDomainEntity.class);

		PlatformConnection platformConnection = platformConnectionDomainService.findByPlatformType(entity.getPlatformType());

		TargetSource targetSource = new TargetSource(entity);

		// 创建连接-保存到数据库
		Long id = targetSourceDomainService.save(targetSource);
		targetSource.getTargetSourceDomainEntity().setId(id);

		// 创建连接
		TargetSourceItems targetSourceItems = targetSource.getTargetSourceDomainEntity().getTargetSourceItems();
		targetSource.genLinkName(); // 生成 linkName

		hcsDomainService.createLink(platformConnection, targetSourceItems.getDatabaseUrl(),
				targetSourceItems.getPort(), targetSourceItems.getDatabaseName(), targetSourceItems.getDatabaseUsername(),
				targetSourceItems.getDatabasePassword(), targetSourceItems.getLinkName());

		targetSourceDomainService.updateTargetSource(targetSource.getTargetSourceDomainEntity());
	}

	/**
	 * 查询目标源
	 *
	 * @param query query
	 */
	@Override
	public PageResponse<TargetSourceDomainEntity> listTargetSource(ListTargetSourceQuery query) {
		TargetSourceDomainEntity entity = BeanUtil.copyProperties(query, TargetSourceDomainEntity.class);
		TargetSource targetSource = new TargetSource(entity);
		return targetSourceDomainService.listTargetSource(targetSource, query.getStartTime(), query.getEndTime(),
				query.getCurrent(), query.getSize());
	}

	/**
	 * 更新目标源
	 *
	 * @param command command
	 */
	@Override
	@Transactional
	public void updateTargetSource(UpdateTargetSourceCommand command) {
		TargetSourceDomainEntity entity = BeanUtil.copyProperties(command, TargetSourceDomainEntity.class);

		PlatformConnection platformConnection = platformConnectionDomainService.findByPlatformType(entity.getPlatformType());

		TargetSource targetSource = new TargetSource(entity);

		TargetSourceItems targetSourceItems = targetSource.getTargetSourceDomainEntity().getTargetSourceItems();
		targetSource.genLinkName(); // 生成 linkName

		// 更新连接
		hcsDomainService.updateLink(platformConnection, targetSourceItems.getDatabaseUrl(),
				targetSourceItems.getPort(), targetSourceItems.getDatabaseName(), targetSourceItems.getDatabaseUsername(),
				targetSourceItems.getDatabasePassword(), targetSourceItems.getLinkName());

		targetSourceDomainService.updateTargetSource(entity);
	}

	/**
	 * 删除目标源
	 *
	 * @param id id
	 */
	@Override
	public void delTargetSource(Long id) {
		TargetSourceDomainEntity entity = targetSourceDomainService.findById(id);

		PlatformConnection platformConnection = platformConnectionDomainService.findByPlatformType(entity.getPlatformType());

		TargetSource targetSource = new TargetSource(entity);

		targetSource.genLinkName(); // 生成 linkName

		hcsDomainService.delLink(platformConnection,targetSource.getTargetSourceDomainEntity().getTargetSourceItems().getLinkName());

		targetSourceDomainService.delTargetSource(id);
	}

	@Override
	public TargetSourceDetailDTO queryTargetSourceDetail(String id) {
		return targetSourceDomainService.queryTargetSourceDetail(id);
	}

}
