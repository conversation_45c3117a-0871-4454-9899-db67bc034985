package cn.teleinfo.ds.business.domain.util;

import cn.hutool.core.util.StrUtil;

public class CronParseUtil {

	public static String parseToHumanReadable(String cronExpr) {
		if (StrUtil.isEmpty(cronExpr)) {
			return "";
		}
		String[] parts = cronExpr.split("\\s+");
		if (parts.length != 6) {
			return "无效的Cron表达式，应有6个部分";
		}

		try {
			String second = parseTimeField(parts[0], "秒");
			String minute = parseTimeField(parts[1], "分");
			String hour = parseHourField(parts[2]);  // 修改点：直接返回24小时制
			String day = parseDay(parts[3]);
			String month = parseMonth(parts[4]);
			String dayOfWeek = parseDayOfWeek(parts[5]);

			// 构建自然语言描述
			StringBuilder description = new StringBuilder();

			if (!month.equals("每个月")) {
				description.append(month);
			}

			if (!dayOfWeek.isEmpty()) {
				description.append(dayOfWeek);
			} else if (!day.equals("每天")) {
				description.append(day);
			} else {
				description.append("每天");
			}

			// 时间部分保持24小时制显示
			description.append(String.format("的%s:%s:%s执行",
					hour,
					minute.replace("分", ""),  // 移除"分"字改用冒号
					second.replace("秒", ""))); // 移除"秒"字改用冒号

			return description.toString();
		} catch (Exception e) {
			return "解析失败: " + e.getMessage();
		}
	}

	// 修改点：直接返回24小时制数字
	private static String parseHourField(String field) {
		if (field.equals("*") || field.equals("?")) {
			return "每小时";
		}
		if (field.contains("-")) {
			return field.replace("-", "点到") + "点";
		}
		return field + "点";
	}

	// 分钟和秒字段处理（返回纯数字）
	private static String parseTimeField(String field, String unit) {
		if (field.equals("*") || field.equals("?")) {
			return "00"; // 用00代替"每分/每秒"
		}
		return field.length() == 1 ? "0" + field : field; // 补零
	}

	// 星期字段处理（保持Quartz标准：4=周三）
	private static String parseDayOfWeek(String dayOfWeek) {
		if (dayOfWeek.equals("?")) return "";
		if (dayOfWeek.equals("*")) return "每天";

		String[] quartzDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
		try {
			if (dayOfWeek.contains("-")) {
				String[] range = dayOfWeek.split("-");
				return "每周" + quartzDays[Integer.parseInt(range[0])-1] + "到" +
						quartzDays[Integer.parseInt(range[1])-1];
			}
			return "每周" + quartzDays[Integer.parseInt(dayOfWeek)-1];
		} catch (Exception e) {
			return ""; // 忽略解析错误
		}
	}

	private static String parseDay(String day) {
		if (day.equals("*") || day.equals("?")) {
			return "每天";
		}
		return day + "号";
	}

	private static String parseMonth(String month) {
		if (month.equals("*")) {
			return "每个月";
		}
		try {
			int monthNum = Integer.parseInt(month);
			if (monthNum >= 1 && monthNum <= 12) {
				return monthNum + "月";
			}
		} catch (NumberFormatException e) {
			// 处理特殊月份表示法
		}
		return month;
	}


	public static void main(String[] args) {
		String[] cronExpressions = {
				"0 0 8-10 * * ?",    // 每天8-10点
				"0 0/30 9-17 ? * MON-FRI", // 工作日9-17点每30分钟
				"0 0 12 ? * 2-6",    // 每周二至周六中午12点
				"01 21 02 ? * 4",    // 每周三凌晨2:21:01
				"0 15 10 ? * MON"   // 每周一上午10:15
		};

		for (String expr : cronExpressions) {
			System.out.println("Cron表达式: " + expr);
			System.out.println("解释: " + parseToHumanReadable(expr));
			System.out.println("----------------------");
		}
	}
}
