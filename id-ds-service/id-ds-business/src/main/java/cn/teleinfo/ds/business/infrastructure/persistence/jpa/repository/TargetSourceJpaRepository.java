package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.TargetSourceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.TargetSourceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.TargetSourceView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TargetSourceJpaRepository extends BaseRepository<TargetSourceEntity, Long> {

	TargetSourceEntity findFirstByAppHandleCodeAndTargetSourceName(String appHandleCode, String targetSourceName);

	@Query(nativeQuery = true,
			value = "SELECT " +
					"a.id, " +
					"a.target_source_name as targetSourceName, " +
					"a.platform_type AS platformType, " +
					"a.app_handle_code as appCode, " +
					"b.app_name AS appName, " +
					"a.items as items, " +
					"a.update_time as updateTime, " +
					"c.name as updateUser " +
					"from t_target_source a " +
					"left join t_app_info b on a.app_handle_code = b.handle_code and b.is_deleted = 0 " +
					"left join sys_user c on a.update_by = c.user_id and c.del_flag = '0' " +
					"where a.id = :id and a.is_deleted = 0 "
	)
	TargetSourceView queryTargetSourceDetail(@Param("id") String id);

	List<TargetSourceEntity> findAllByAppHandleCodeAndTargetSourceName(String appHandleCode, String targetSourceName);

	List<TargetSourceEntity> findByAppHandleCode(String appHandleCode);

	@Query(nativeQuery = true,
			value = """
					SELECT
					 	t.id,
					  	t.`target_source_name` AS targetSourceName,
					  	t.`platform_type` AS platformType,
						t.`app_handle_code` AS appHandleCode,
						t.`items` AS items,
						t.`create_time` AS createTime,
						t.`create_by` AS createBy,
						t.`update_time` AS updateTime,
						t.`update_by` AS updateBy,
						(select u.name from sys_user u where u.user_id = t.`create_by`) AS createByName,
						(select u.name from sys_user u where u.user_id = t.`update_by`) AS updateByName
					 FROM t_target_source t 
					WHERE t.is_deleted = 0 
					  AND (:appHandleCode IS NULL OR :appHandleCode = '' OR app_handle_code = :appHandleCode)
					  AND (:targetSourceName IS NULL OR target_source_name LIKE CONCAT('%', :targetSourceName, '%'))
					  AND (:platformType IS NULL OR platform_type = :platformType)
					  AND (:startTime IS NULL OR update_time >= :startTime)
					  AND (:endTime IS NULL OR update_time <= :endTime)
					  AND (COALESCE(:userHandleCodes, NULL) IS NULL OR app_handle_code IN (:userHandleCodes))
					""",
			countQuery = """
					SELECT COUNT(*) FROM t_target_source
					WHERE is_deleted = 0 
					  AND (:appHandleCode IS NULL OR :appHandleCode = '' OR app_handle_code = :appHandleCode)
					  AND (:targetSourceName IS NULL OR target_source_name LIKE CONCAT('%', :targetSourceName, '%'))
					  AND (:platformType IS NULL OR platform_type = :platformType)
					  AND (:startTime IS NULL OR update_time >= :startTime)
					  AND (:endTime IS NULL OR update_time <= :endTime)
					  AND (COALESCE(:userHandleCodes, NULL) IS NULL OR app_handle_code IN (:userHandleCodes))
					""")
	Page<TargetSourceListView> findList(
			@Param("appHandleCode") String appHandleCode,
			@Param("targetSourceName") String targetSourceName,
			@Param("platformType") Integer platformType,
			@Param("userHandleCodes") List<String> userHandleCodes,
			@Param("startTime") LocalDateTime startTime,
			@Param("endTime") LocalDateTime endTime,
			Pageable pageable
	);
}
