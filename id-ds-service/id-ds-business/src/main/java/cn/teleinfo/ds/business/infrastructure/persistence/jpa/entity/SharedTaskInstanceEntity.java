package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.time.LocalDateTime;

/**
 * 共享任务实例表
 */
@Getter
@Setter
@Entity
@Table(name = "t_shared_task_instance")
@SQLDelete(sql = "update t_shared_task_instance set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class SharedTaskInstanceEntity extends BaseEntity {

	private Long id;

    /**
     * 关联共享任务ID
     */
    @Column(name = "shared_task_id", nullable = false)
    private Long sharedTaskId;

    /**
     * 任务执行编号，如RW202506001
     */
    @Column(name = "task_instance_no", nullable = false)
    private String taskInstanceNo;

    /**
     * 任务名称
     */
    @Column(name = "task_name", nullable = false)
    private String taskName;

    /**
     * 任务类型：1-手动任务，2-定时任务
     */
    @Column(name = "task_type", nullable = false)
    private Integer taskType;

    /**
     * 任务状态：1-启用，0-禁用
     */
    @Column(name = "task_status", nullable = false)
    private Integer taskStatus;

    /**
     * 执行类型：1-正式执行，2-测试执行
     */
    @Column(name = "execution_type", nullable = false)
    private Integer executionType;

    /**
     * 运行状态：1-运行中，2-成功，3-失败，0-未运行
     */
    @Column(name = "run_status")
    private Integer runStatus;

    /**
     * 目标源ID
     */
    @Column(name = "target_source_id")
    private Long targetSourceId;

    /**
     * 数据库名
     */
    @Column(name = "database_name")
    private String databaseName;

    /**
     * 应用身份编码
     */
    @Column(name = "app_handle_code")
    private String appHandleCode;

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * CRON表达式，如0 0 12 * * ?（仅定时任务使用）
     */
    @Column(name = "cron_expression")
    private String cronExpression;

    /**
     * 运行时间
     */
    @Column(name = "run_time")
    private LocalDateTime runTime;

    /**
     * 运行时长(秒)
     */
    @Column(name = "run_duration")
    private Integer runDuration;

    /**
     * 共享数据总量
     */
    @Column(name = "shared_data_count")
    private Integer sharedDataCount;

    /**
     * 任务日志路径
     */
    @Column(name = "log_path", columnDefinition = "text")
    private String logPath;

    /**
     * 操作人
     */
    @Column(name = "operator", nullable = false)
    private String operator;
}