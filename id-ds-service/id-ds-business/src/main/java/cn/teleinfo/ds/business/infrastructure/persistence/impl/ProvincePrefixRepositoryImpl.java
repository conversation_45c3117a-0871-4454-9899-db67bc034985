package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.ProvincePrefixDomainEntity;
import cn.teleinfo.ds.business.domain.repository.ProvincePrefixRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ProvincePrefixEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ProvincePrefixJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class ProvincePrefixRepositoryImpl implements ProvincePrefixRepository {
	private final ProvincePrefixJpaRepository provincePrefixJpaRepository;

	/**
	 * 查询所有省级目录
	 */
	@Override
	public List<ProvincePrefixDomainEntity> findAll() {
		List<ProvincePrefixEntity> all = provincePrefixJpaRepository.findAll();
		return BeanUtil.copyToList(all, ProvincePrefixDomainEntity.class);
	}

	@Override
	public ProvincePrefixDomainEntity findByProvincePrefix(String provincePrefix) {
		ProvincePrefixEntity provincePrefixEntity = provincePrefixJpaRepository.findByProvincePrefix(provincePrefix);
		return BeanUtil.copyProperties(provincePrefixEntity,ProvincePrefixDomainEntity.class);
	}
}
