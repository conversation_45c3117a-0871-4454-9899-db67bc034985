package cn.teleinfo.ds.business.domain.model.aggregate;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ExecutionType;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.RunStatus;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesItem;
import cn.teleinfo.ds.business.domain.model.entity.SharedSubTaskInstance;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDetail;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskInstanceDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SubTaskCurrentStep;
import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.common.core.exception.CheckedException;

import cn.teleinfo.ds.common.core.util.SqlUtils;
import cn.teleinfo.ds.common.log.util.BusinessLoggerOption;
import cn.teleinfo.ds.common.log.util.BusinessLoggerUtils;
import cn.teleinfo.ds.common.security.service.PigUser;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import lombok.Getter;
import net.sf.jsqlparser.JSQLParserException;

import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 共享任务聚合跟
 */
@Getter
public class SharedTask {

	// 执行类型
	private final ExecutionType executionType;

	// 共享任务
	private final SharedTaskDomainEntity sharedTask;

	// 对象标识
	private final List<HandleDomainEntity> handles;

	// 应用
	private final AppInfoDomainEntity appInfo;

	// 目标源
	private final TargetSourceDomainEntity targetSource;

	// 链接信息
	private final PlatformConnectionDomainEntity sysConnection;

	// 输出表
	// handle : SQL
	// 每个 handle 对应的建表语句
	private final HashMap<String, List<String>> outputTables;

	// 执行实例
	private final SharedTaskInstanceDomainEntity sharedTaskInstance;

	// 每个任务都会初始化一个日志组件记录本次执行的日志
	private Logger taskLogger;
	private final Path fullLogPath;

	public SharedTask(SharedTaskDomainEntity sharedTask, List<HandleDomainEntity> handles,
					  AppInfoDomainEntity appInfo, TargetSourceDomainEntity targetSource,
					  PlatformConnectionDomainEntity sysConnection, BusinessLoggerOption businessLoggerOption, ExecutionType executionType) {
		this.executionType = executionType;
		this.outputTables = new HashMap<>();

		// 过滤选择的字段
		// 购物车可能只选择部分字段
		// detail 内为选择字段
		// handle 内为全部字段
		List<HandleDomainEntity> resultHandles = new ArrayList<>();
		for (SharedTaskDetail detail : sharedTask.getDetails()) {
			for (HandleDomainEntity handle : handles) {
				if (StrUtil.equals(detail.getHandle(), handle.getHandle())) {
					HandleDomainEntity h = BeanUtil.copyProperties(handle, HandleDomainEntity.class);

					if (StrUtil.isEmpty(detail.getFields()) && !JSONUtil.isTypeJSON(detail.getFields())) {
						throw new CheckedException("对象标识属性错误");
					}

					List<HandleItemDomainEntity> selectedItems = new ArrayList<>();

					// 已经选择字段
					List<String> selectedFields = JSONUtil.toList(detail.getFields(), String.class);
					for (String selectedField : selectedFields) {
						for (HandleItemDomainEntity handleItem : handle.getHandleItems()) {
							if (StrUtil.equals(selectedField, handleItem.getField())) {
								selectedItems.add(handleItem);
							}
						}
					}

					h.setHandleItems(selectedItems);
					resultHandles.add(h);

					this.outputTables.put(handle.getHandle(), new ArrayList<>());
				}
			}
		}

		appInfo.getShareDataSources().setShareDataSourcesItem(JSONUtil.toBean(appInfo.getShareDataSources().getItems(), ShareDataSourcesItem.class));

		this.fullLogPath = BusinessLoggerUtils.fullLogPath(businessLoggerOption.getLogPath(), sharedTask.getTaskNo());
		this.taskLogger = BusinessLoggerUtils.createLogger(fullLogPath, Level.DEBUG);

		this.sharedTask = sharedTask;
		this.handles = resultHandles;
		this.appInfo = appInfo;
		this.targetSource = targetSource;
		this.sysConnection = sysConnection;


		SharedTaskInstanceDomainEntity instance = new SharedTaskInstanceDomainEntity();
		instance.setSharedTaskId(this.sharedTask.getId());
		instance.setTaskInstanceNo(System.currentTimeMillis() + ""); // TODO 任务执行编号 暂定时间戳
		instance.setTaskName(this.sharedTask.getTaskName());
		instance.setTaskNo(this.sharedTask.getTaskNo());
		instance.setTaskType(this.sharedTask.getTaskType());
		instance.setTaskStatus(this.sharedTask.getTaskStatus());
		instance.setExecutionType(ExecutionType.FORMAL.code());
		instance.setRunStatus(RunStatus.NO.code());
		instance.setTargetSourceId(this.sharedTask.getTargetSourceId());
		instance.setDatabaseName(this.sharedTask.getDatabaseName());
		instance.setAppHandleCode(this.sharedTask.getAppHandleCode());
		instance.setEntPrefix(this.sharedTask.getEntPrefix());
		instance.setCronExpression(this.sharedTask.getCronExpression());
		instance.setLogPath(fullLogPath.toString());

		PigUser user = SecurityUtils.getUser();
		if (user == null) {
			instance.setOperator("--");
		} else {
			instance.setOperator(user.getId() + "");
		}

		this.sharedTaskInstance = instance;

		List<SharedSubTaskInstance> subs = new ArrayList<>();
		for (HandleDomainEntity handle : this.handles) {
			SharedSubTaskInstance sub = new SharedSubTaskInstance();
			sub.setTaskInstanceNo(instance.getTaskInstanceNo());
			sub.setHandle(handle.getHandle());
			sub.setSubTaskStatus(RunStatus.NO.code());
			sub.setCurrentStep(SubTaskCurrentStep.MAPPING.code());
			subs.add(sub);
		}
		this.sharedTaskInstance.setSubs(subs);

	}


	// 共享任务开始执行记录执行时间
	public void start() {
		taskLogger.info("共享任务开始执行 runTime={}", DateUtil.date());
		List<String> handles = this.handles.stream().map(HandleDomainEntity::getHandle).toList();
		taskLogger.info("本次共享的对象标识是 handles={}", CollUtil.join(handles, "&"));

		LocalDateTime now = LocalDateTime.now();
		sharedTaskInstance.setRunStatus(RunStatus.RUN.code());
		sharedTaskInstance.setRunTime(now);
		sharedTaskInstance.setRunDuration(0); // 秒
		for (SharedSubTaskInstance sub : sharedTaskInstance.getSubs()) {
			sub.setRunTime(now);
			sub.setRunDuration(0);
			sub.setSharedDataCount(0);
			sub.setSubTaskStatus(RunStatus.RUN.code());
		}
	}

	// 更新子任务执行时
	public void updateSubRuntime(String handle, SubTaskCurrentStep step, Integer sharedDataCount, RunStatus runStatus, String errorMessage) {
		LocalDateTime now = LocalDateTime.now();

		for (SharedSubTaskInstance sub : sharedTaskInstance.getSubs()) {
			if (StrUtil.equals(sub.getHandle(), handle)) {
				sub.setCurrentStep(step.code()); // 执行到哪一步

				Duration duration = Duration.between(sub.getRunTime(), now);
				long runDuration = duration.toSeconds();
				sub.setRunDuration((int) runDuration);
				sub.setSharedDataCount(sub.getSharedDataCount() + sharedDataCount);
				sub.setSubTaskStatus(runStatus.code());
				sub.setErrorMessage(errorMessage);

			}
		}
	}

	public void updateAllSubRuntime(SubTaskCurrentStep step, Integer sharedDataCount, RunStatus runStatus, String errorMessage) {
		for (SharedSubTaskInstance sub : sharedTaskInstance.getSubs()) {
			updateSubRuntime(sub.getHandle(), step, sharedDataCount, runStatus, errorMessage);
		}
	}

	// 更新任务执行时
	public void updateRuntime(RunStatus runStatus, String errorMessage) {
		LocalDateTime now = LocalDateTime.now();

		Duration duration = Duration.between(sharedTaskInstance.getRunTime(), now);
		long runDuration = duration.toSeconds();
		sharedTaskInstance.setRunStatus(runStatus.code());
		sharedTaskInstance.setRunDuration((int) runDuration); // 秒

		int sharedDataCount = 0;
		for (SharedSubTaskInstance sub : sharedTaskInstance.getSubs()) {
			sharedDataCount += sub.getSharedDataCount();
			sub.setSubTaskStatus(runStatus.code());
		}

		sharedTaskInstance.setSharedDataCount(sharedDataCount);
	}

	// 根据表名查询建表语句
	public String findSQLByTableName(String tableName) {
		for (Map.Entry<String, List<String>> entry : this.outputTables.entrySet()) {
			var sqls = entry.getValue();
			for (String sql : sqls) {
				try {
					Set<String> tableNames = SqlUtils.findTableName(sql);
					if (tableNames.contains(tableName)) {
						return sql;
					}

				} catch (JSQLParserException e) {
					return null;
				}
			}
		}

		return null;
	}

	public void destroyLogger() {
		BusinessLoggerUtils.destroy(taskLogger);
	}

	public void restartLogger() {
		this.taskLogger = BusinessLoggerUtils.createLogger(this.fullLogPath, Level.DEBUG);
	}
}
