package cn.teleinfo.ds.business.domain.model.entity;

public enum SubTaskCurrentStep {
	/**
	 * 当前执行步骤（失败时记录在哪一步失败）
	 * 1 分析湖仓映射关系
	 * 2. 查询批量共享通道 SQL
	 * 3. 数据写入对象标识
	 * 4. 创建离线管道任务
	 * 5. 执行任务写入目标源';
	 */
	NO(0, "未正式运行"),
	MAPPING(1, "分析湖仓映射关系"),
	SHARE_CHANNEL_SQL(2, "查询批量共享通道 SQL"),
	WRITE_HANDLE(3, "数据写入对象标识"),
	OFFLINE_TASK(4, "创建离线管道任务"),
	WRITE_TARGET_SOURCE(5, "执行任务写入目标源"),
	END(6, "执行结束");


	private final int code;
	private final String message;

	SubTaskCurrentStep(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public int code() {
		return code;
	}

	public String message() {
		return message;
	}

	public static SubTaskCurrentStep findByCode(int code) {
		for (SubTaskCurrentStep value : SubTaskCurrentStep.values()) {
			if (value.code() == code) {
				return value;
			}
		}

		throw new IllegalArgumentException("执行步骤: " + code);
	}
}
