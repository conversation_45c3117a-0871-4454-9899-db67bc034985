package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionType;
import cn.teleinfo.ds.business.domain.repository.PlatformConnectionRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.PlatformConnectionEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.PlatformConnectionJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@AllArgsConstructor
public class PlatformConnectionRepositoryImpl implements PlatformConnectionRepository {

	private final PlatformConnectionJpaRepository repository;

	/**
	 * 查询连接设置
	 *
	 * @param platformType 平台类型
	 * @return 连接设置信息
	 */
	@Override
	public PlatformConnectionDomainEntity findByPlatformType(PlatformConnectionType platformType) {
		var option = repository.findByPlatformType(platformType.code());

		if (option.isEmpty()) {
			return null;
		}

		var conn = option.get();

		PlatformConnectionDomainEntity entity = new PlatformConnectionDomainEntity();
		entity.setId(conn.getId());
		entity.setPlatformType(platformType);
		entity.setPlatformConnection(conn.getPlatformConnection());

		return entity;
	}

	/**
	 * 更新连接设置
	 */
	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void updateConnectionSetting(Integer platformType, String platformConnection) {
		var option = repository.findByPlatformType(platformType);
		var entity = option.orElse(new PlatformConnectionEntity());
		entity.setPlatformType(platformType);
		entity.setPlatformConnection(platformConnection);
		repository.save(entity);
	}
}
