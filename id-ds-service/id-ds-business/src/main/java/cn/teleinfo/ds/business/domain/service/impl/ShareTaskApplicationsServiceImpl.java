package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplications;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsService;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareTaskApplicationsRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ShareTaskApplicationsServiceImpl implements ShareTaskApplicationsService {
	@Override
	public ShareTaskApplications createShareTaskApplications(ShareTaskApplicationsCommand command) {
		 return new ShareTaskApplications().setShareTaskApplications(command);
	}
}
