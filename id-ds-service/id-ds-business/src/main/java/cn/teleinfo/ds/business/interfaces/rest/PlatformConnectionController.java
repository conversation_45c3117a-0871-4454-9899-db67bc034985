package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.command.UpdateConnectionSettingCommand;
import cn.teleinfo.ds.business.application.service.PlatformConnectionApplicationService;
import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionDomainEntity;
import cn.teleinfo.ds.business.interfaces.assembler.PlatformConnectionAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.UpdatePlatformConnectionRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.PlatformConnectionResponse;
import cn.teleinfo.ds.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统设置
 * 连接管理
 */
@RestController
@RequestMapping("/sys/connection")
@AllArgsConstructor
public class PlatformConnectionController {

	private final PlatformConnectionApplicationService platformConnectionApplicationService;
	private final PlatformConnectionAssembler platformConnectionAssembler;

	/**
	 * 查询系统连接配置
	 *
	 * @param platformType 平台类型
	 * @return 系统连接配置
	 */
	@GetMapping
	public R<PlatformConnectionResponse> getConnectionSetting(@RequestParam("platformType") Integer platformType) {
		PlatformConnectionDomainEntity conn = platformConnectionApplicationService.getConnectionSetting(platformType);
		return R.ok(platformConnectionAssembler.toResponse(conn));
	}

	/**
	 * 更新系统连接配置
	 *
	 * @param request 系统连接配置信息
	 * @return
	 */
	@PutMapping
	public R updateConnectionSetting(@RequestBody @Valid UpdatePlatformConnectionRequest request) {
		UpdateConnectionSettingCommand command = platformConnectionAssembler.toUpdateConnectionSettingCommand(request);
		platformConnectionApplicationService.updateConnectionSetting(command);
		return R.ok();
	}
}
