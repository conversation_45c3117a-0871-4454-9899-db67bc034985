package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.command.CreateTargetSourceCommand;
import cn.teleinfo.ds.business.application.command.UpdateTargetSourceCommand;
import cn.teleinfo.ds.business.application.query.ListTargetSourceQuery;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.TargetSourceDetailDTO;
import cn.teleinfo.ds.business.interfaces.dto.request.CreateTargetSourceRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ListTargetSourceRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.UpdateTargetSourceRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ListTargetSourceResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.TargetSourceDetailResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

@Component
public class TargetSourceApplicationAssembler {

	public CreateTargetSourceCommand toCreateTargetSourceCommand(CreateTargetSourceRequest request) {
		CreateTargetSourceCommand command = new CreateTargetSourceCommand();

		command.setPlatformType(request.getPlatformType());
		command.setAppHandleCode(request.getAppHandleCode());
		command.setTargetSourceName(request.getTargetSourceName());

		try {
			ObjectMapper mapper = new ObjectMapper();
			command.setItems(mapper.writeValueAsString(request.getItems()));
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return command;
	}

	public ListTargetSourceQuery toListTargetSourceQuery(ListTargetSourceRequest request) {
		return BeanUtil.copyProperties(request, ListTargetSourceQuery.class);
	}

	public PageResponse<ListTargetSourceResponse> toListTargetSourceResponse(
			PageResponse<TargetSourceDomainEntity> response) {
		var records = response.getRecords()
			.stream()
			.map(t -> BeanUtil.copyProperties(t, ListTargetSourceResponse.class))
			.toList();
		return new PageResponse<>(records, response.getTotal(), response.getSize(), response.getCurrent(),
				response.getPages());
	}

	public UpdateTargetSourceCommand toUpdateTargetSourceCommand(UpdateTargetSourceRequest request) {
		UpdateTargetSourceCommand command = new UpdateTargetSourceCommand();
		command.setId(request.getId());
		command.setPlatformType(request.getPlatformType());
		command.setTargetSourceName(request.getTargetSourceName());
		command.setAppHandleCode(request.getAppHandleCode());
		try {
			ObjectMapper mapper = new ObjectMapper();
			command.setItems(mapper.writeValueAsString(request.getItems()));
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return command;
	}

	public TargetSourceDetailResponse toTargetSourceDetailResponse(TargetSourceDetailDTO targetSourceDetail) {
		return BeanUtil.copyProperties(targetSourceDetail, TargetSourceDetailResponse.class);
	}

}
