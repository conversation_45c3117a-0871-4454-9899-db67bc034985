package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.query.DataChannelsListQuery;
import cn.teleinfo.ds.business.application.service.DataChannelsService;
import cn.teleinfo.ds.business.domain.service.DataChannelsDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.DataChannelsView;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class DataChannelsServiceImpl implements DataChannelsService {

	private final DataChannelsDomainService service;

	/**
	 * 查询数据通道列表
	 *
	 * @param query 查询条件
	 * @return 分页数据
	 */
	@Override
	public PageResponse<DataChannelsView> listDataChannels(DataChannelsListQuery query) {
		return service.listDataChannels(query);
	}
}
