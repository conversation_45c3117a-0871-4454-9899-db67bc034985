package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.entity.UserInfo;
import cn.teleinfo.ds.business.domain.repository.UserRepository;
import cn.teleinfo.ds.business.domain.service.UserDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.UserEntity;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class UserDomainServiceImpl implements UserDomainService {
	private final UserRepository userRepository;


	@Override
	public UserInfo findById(String username) {
		UserInfo user = userRepository.findById(username);
		if (user == null) {
			throw new CheckedException("用户不存在或已删除！");
		}
		return user;
	}
}
