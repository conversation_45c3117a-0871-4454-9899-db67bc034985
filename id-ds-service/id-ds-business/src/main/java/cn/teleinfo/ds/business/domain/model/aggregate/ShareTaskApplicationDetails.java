package cn.teleinfo.ds.business.domain.model.aggregate;


import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationDetailValue;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationValue;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskAuthApplicationValue;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ShareTaskApplicationDetails {

	/**
	 * 共享任务申请详情
	 */
	private ShareTaskApplicationValue basicInfo;

	/**
	 * 授权记录
	 */
	private List<ShareTaskAuthApplicationValue> authorizeRecords;

	/**
	 * 共享数据
	 */
	private List<ShareTaskApplicationDetailValue> shareData;

	private ApplicationInfo applicationInfo;
}
