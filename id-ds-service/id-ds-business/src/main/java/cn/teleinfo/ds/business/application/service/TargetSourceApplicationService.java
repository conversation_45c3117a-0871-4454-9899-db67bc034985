package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.command.CreateTargetSourceCommand;
import cn.teleinfo.ds.business.application.command.UpdateTargetSourceCommand;
import cn.teleinfo.ds.business.application.query.ListTargetSourceQuery;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.TargetSourceDetailDTO;
import cn.teleinfo.ds.common.core.util.PageResponse;

public interface TargetSourceApplicationService {

	/**
	 * 创建目标源
	 *
	 * @param command command
	 */
	void createTargetSource(CreateTargetSourceCommand command);

	/**
	 * 查询目标源
	 *
	 * @param query query
	 */
	PageResponse<TargetSourceDomainEntity> listTargetSource(ListTargetSourceQuery query);

	/**
	 * 更新目标源
	 *
	 * @param command command
	 */
	void updateTargetSource(UpdateTargetSourceCommand command);

	/**
	 * 删除目标源
	 * @param id id
	 */
	void delTargetSource(Long id);

	TargetSourceDetailDTO queryTargetSourceDetail(String id);
}
