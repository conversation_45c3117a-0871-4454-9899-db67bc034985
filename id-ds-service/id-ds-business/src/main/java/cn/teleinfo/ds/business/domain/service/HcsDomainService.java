package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmLinkResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.HcsBaseResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ListScriptResultsResponseDTO;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ScriptInfoDTO;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Links;

import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigCommodityOrder;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigDataSourceView;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ColumnsList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.DatabasesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.TablesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.Workspacebody;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;

import java.util.List;

public interface HcsDomainService {
	/**
	 * 数据集成-规范层连接名称
	 */
	List<Links> findCdmConnections(PlatformConnection platformConnection, String projectId, String clusterId);


	/**
	 * 数据开发-规范层连接名称
	 */
	List<ApigDataSourceView> findDayuConnections(PlatformConnection platformConnection, String projectId, String workspace, Integer offset, Integer limit);

	/**
	 * 数据开发-规范层数据库名称
	 */
	List<DatabasesList> findDayuConnectionsDatabases(PlatformConnection platformConnection, String projectId, String workspace, String connectionId, Integer offset, Integer limit);

	/**
	 * 获取资源空间列表
	 */
	List<AuthProjectResult> findProjects(PlatformConnection platformConnection);

	/**
	 * 获取数据治理中心实例列表
	 */
	List<ApigCommodityOrder> findDasInstances(PlatformConnection platformConnection, String projectId, Integer current, Integer size);

	/**
	 * 获取工作空间列表
	 */
	List<Workspacebody> findDasWorkspaces(PlatformConnection platformConnection, String projectId, String instanceId, Integer current, Integer size);

	/**
	 * 获取CDM集群名称列表
	 */
	List<Clusters> findCdmClusters(PlatformConnection platformConnection, String projectId);

	/**
	 * 获取作业列表
	 */
	List<Job> findJobs(PlatformConnection platformConnection, String projectId, String clusterId);

	/**
	 * 创建脚本
	 */
	void createScript(PlatformConnection platformConnection, String projectId, String scriptName, String scriptContent, String workspace, String databaseName, String connectionName);

	/**
	 * 执行脚本
	 */
	String executeScript(PlatformConnection platformConnection, String projectId, String scriptName, String workspace);

	/**
	 * 查询脚本实例执行结果
	 */
	ListScriptResultsResponseDTO listScriptResults(PlatformConnection platformConnection, String projectId, String scriptName, String workspace, String instanceId);

	/**
	 * 获取数据源中的表
	 */
	List<TablesList> listTables(PlatformConnection platformConnection, String projectId, String connectionId, String databaseName, String tableName, String workSpace);

	/**
	 * 获取表的字段
	 */
	List<ColumnsList> listTableColumns(PlatformConnection platformConnection, String projectId, String connectionId, String workSpace, String tableId);

	/**
	 * 删除脚本
	 */
	void deleteScript(PlatformConnection platformConnection, String projectId, String scriptName, String workspace);

	ScriptInfoDTO scriptList(PlatformConnection platformConnection, String projectId, String workspace, Integer limit, Integer offset);


	ScriptInfo findScript(PlatformConnection platformConnection, String projectId, String workspace, String scriptName);

	/**
	 * 查询连接
	 */
	CdmLinkResponse listLink(PlatformConnection platformConnection, String linkName);

	/**
	 * 创建连接
	 */
	void createLink(PlatformConnection platformConnection, String host, Integer port, String database,
							   String username, String password, String linkName);

	/**
	 * 更新连接
	 */
	void updateLink(PlatformConnection platformConnection, String host, Integer port, String database, String username, String password, String linkName);

	/**
	 * 删除连接
	 */
	void delLink(PlatformConnection platformConnection, String linkName );
}
