package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleListView;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleDetailResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.time.LocalDateTime;

public interface HandlesDomainService {

	/**
	 * 对象标识列表
	 */
	PageResponse<HandleListView> listHandles(Handle handle, Integer page, Integer size, LocalDateTime startTime, LocalDateTime endTime, String userHandleFilter);

	/**
	 * 查询对象标识
	 */
	HandleDomainEntity findByHandle(String handle);

	HandleDetailResponse handleDetail(Long id);

}
