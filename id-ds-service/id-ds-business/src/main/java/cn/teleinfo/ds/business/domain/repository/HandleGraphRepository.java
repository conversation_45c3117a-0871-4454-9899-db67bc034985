package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleReferenceDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.Graph;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.GraphHandlesDTO;

import java.util.List;

public interface HandleGraphRepository {
        Graph handleChildren(String handle);

        Graph handleGraph(String handle);

        /**
         * 对象标识模糊查询
         */
        List<GraphHandlesDTO> getGraphHandles(String handle);

        Handle handleItems(String handle);

        // 新增：返回领域对象的方法
        HandleDomainEntity findHandleDomainByHandle(String handle);

        List<HandleItemDomainEntity> findHandleItemsByHandleId(Long handleId);

        List<HandleReferenceDomainEntity> findReferencesByHandleItemId(Long itemId);

        String findAppNameByHandleCode(String appHandleCode);

        String findEntNameByEntPrefix(String entPrefix);
}
