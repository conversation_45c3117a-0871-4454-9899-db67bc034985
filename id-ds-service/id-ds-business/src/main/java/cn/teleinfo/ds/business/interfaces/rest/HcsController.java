package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.service.HcsService;
import cn.teleinfo.ds.business.interfaces.assembler.HcsAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.FindCdmClustersRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.FindCdmConnectionsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.FindDasInstancesRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.FindDasWorkspacesRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.FindDayuConnectionsDatabasesRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.FindDayuConnectionsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.FindProjectsRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.FindCdmClustersResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindCdmConnectionsResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindDasInstancesResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindDasWorkspacesResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindDayuConnectionsDatabasesResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindDayuConnectionsResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindProjectsResponse;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigCommodityOrder;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigDataSourceView;
import com.huaweicloud.sdk.dataartsstudio.v1.model.DatabasesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.Workspacebody;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import cn.teleinfo.ds.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/hcs")
@AllArgsConstructor
public class HcsController {

	private final HcsService service;
	private final HcsAssembler assembler;


	/**
	 * 获取资源空间列表
	 * HcsIamTest.keystoneListAuthProjects
	 */
	@GetMapping("iam/auth/projects")
	public R<List<FindProjectsResponse>> findProjects(@Valid FindProjectsRequest request) {
		List<AuthProjectResult> lists = service.findProjects(request.getPlatformType());
		return R.ok(assembler.toFindProjectsResponse(lists));
	}

	/**
	 * 获取数据治理中心实例列表
	 * <p>
	 * HcsDataArtsStudioTest.listDataArtsStudioInstances
	 */
	@GetMapping("data-arts-studio/instances")
	public R<List<FindDasInstancesResponse>> findDasInstances(@Valid FindDasInstancesRequest request) {
		List<ApigCommodityOrder> lists = service.findDasInstances(request.getPlatformType(), request.getProjectId(), request.getCurrent(), request.getSize());
		return R.ok(assembler.toFindDasInstancesResponse(lists));
	}

	/**
	 * 获取工作空间列表
	 * <p>
	 * HcsDataArtsStudioTest.ListManagerWorkSpacesRequest
	 */
	@GetMapping("data-arts-studio/workspaces")
	public R<List<FindDasWorkspacesResponse>> findDasWorkspaces(@Valid FindDasWorkspacesRequest request) {
		List<Workspacebody> lists = service.findDasWorkspaces(request.getPlatformType(), request.getProjectId(), request.getInstanceId(), request.getCurrent(), request.getSize());
		return R.ok(assembler.toFindDasWorkspaces(lists));

	}

	/**
	 * 获取CDM集群名称列表
	 * <p>
	 * HcsCdmTest.listClusters
	 */
	@GetMapping("cdm/clusters")
	public R<List<FindCdmClustersResponse>> findCdmClusters(@Valid FindCdmClustersRequest request) {
		List<Clusters> lists = service.findCdmClusters(request.getPlatformType(), request.getProjectId());
		return R.ok(assembler.toFindCdmClusters(lists));
	}

	/**
	 * 获取数据集成-连接名称列表
	 */
	@GetMapping("/cdm/connections")
	public R<List<FindCdmConnectionsResponse>> findCdmConnections(@Valid FindCdmConnectionsRequest request) {
		List<Links> lists = service.findCdmConnections(request.getPlatformType(), request.getProjectId(), request.getClusterId());
		return R.ok(assembler.toFindCdmConnections(lists));
	}

	/**
	 * 数据开发-规范层连接名称
	 * <p>
	 * HcsDataArtsStudioTest.listDataconnections
	 */
	@GetMapping("dayu/connections")
	public R<List<FindDayuConnectionsResponse>> findDayuConnections(@Valid FindDayuConnectionsRequest request) {
		List<ApigDataSourceView> lists = service.findDayuConnections(request.getPlatformType(), request.getProjectId(), request.getWorkspace(), request.getCurrent(), request.getSize());
		return R.ok(assembler.toFindDayuConnections(lists));
	}

	/**
	 * 数据开发-规范层数据库名称
	 * <p>
	 * HcsDataArtsStudioTest.listDatabases
	 */
	@GetMapping("dayu/connections/databases")
	public R<List<FindDayuConnectionsDatabasesResponse>> findDayuConnectionsDatabases(@Valid FindDayuConnectionsDatabasesRequest request) {
		List<DatabasesList> lists = service.findDayuConnectionsDatabases(request.getPlatformType(), request.getProjectId(), request.getWorkspace(), request.getConnectionId(), request.getCurrent(), request.getSize());
		return R.ok(assembler.toFindDayuConnectionsDatabases(lists));
	}
}
