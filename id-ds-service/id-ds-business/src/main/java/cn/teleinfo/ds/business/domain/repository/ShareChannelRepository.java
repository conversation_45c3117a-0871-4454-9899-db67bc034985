package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.application.query.ListShareChannelApplicationsQuery;
import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.domain.model.entity.DataChannelDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannelDetectLog;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsDetailView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.util.List;

public interface ShareChannelRepository {

	PageResponse<ShareChannelsDTO> listShareChannels(ListShareChannelsQuery query, Integer current, Integer size);

	ShareChannelsDetailsDTO queryDetailsById(String id);

	List<ShareChannelsDetailsDTO> listShareChannelsVersion(String shareChannelId);

	ShareChannelsVersionSqlView queryVersionSql(String shareChannelId, String main_version, String minor_version);

	List<ShareChannelsVersionDTO> queryShareChannelsVersionDTO(String shareChannelId);

	PageResponse<ShareChannelsApplicationsView> listShareChannelsApplications(ListShareChannelApplicationsQuery query,
																			  Integer current, Integer size);

	ShareChannelsApplicationsDetailView queryShareChannelsApplicationDetails(String applicationId);

	List<ShareChannel> findShareChannelsByDataChannelId(Long dataChannelId);

	void saveShareChannel(ShareChannel shareChannel);

	ShareChannel findByShareChannelIdAndChannelStatus(Long shareChannelId);
	ShareChannel findByDataChannelIdAndChannelStatus(Long dataChannelId);

	DataChannelDomainEntity findDataChannel(Long shareChannelId);

	ShareChannel findMaxVersion(Long shareChannelId);

	List<String> queryChannelVersions(String shareChannelId);

	ShareChannel findByShareChannelIdAndVersion(Long shareChannelId, Integer mainVersion, Integer minorVersion);

	List<ShareChannel> findAllShareChannel();

	List<ShareChannel> findByShareChannelIdAndDetectionStatus(Long shareChannelId, Integer status);


	ShareChannelDetectLog findShareChannelLog(Long shareChannelId);

	ShareChannelDetectLog findShareChannelLog(Long shareChannelId, Integer mainVersion, Integer minorVersion);

	void saveShareChannelLog(ShareChannelDetectLog shareChannelDetectLog);

	void updateDetectStatus(ShareChannelDetectLog shareChannelDetectLog);

	ShareChannel findById(Long id);

	List<ShareChannel> findAllByShareChannelId(Long shareChannelId);

	void saveAll(List<ShareChannel> changeList);
}
