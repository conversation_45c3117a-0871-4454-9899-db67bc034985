package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.repository.AppInfoRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.AppInfoEntDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.AppInfoEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.AppInfoEntView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.AppInfoJpaRepository;
import cn.teleinfo.ds.business.interfaces.dto.response.AppInfoTreeResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.checkerframework.checker.units.qual.A;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class AppInfoRepositoryImpl implements AppInfoRepository {

	private final AppInfoJpaRepository appInfoJpaRepository;

	/**
	 * 查询企业前缀下的应用
	 *
	 * @param entPrefix 企业前缀
	 * @return
	 */
	@Override
	public List<AppInfoDomainEntity> findAllByEntPrefix(String entPrefix) {
		List<AppInfoEntity> all = appInfoJpaRepository.findByEntPrefix(entPrefix);
		return BeanUtil.copyToList(all, AppInfoDomainEntity.class);
	}

	/**
	 * 应用信息列表查询
	 *
	 * @param query 过滤条件
	 */
	@Override
	public PageResponse<HandleSignAppInfoDTO> listHandleSignAppInfo(ListAppInfoQuery query, Integer page,
																	Integer size) {
		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		var results = appInfoJpaRepository.listHandleSignAppInfo(query.getAppName(), query.getHandleCode(),
				query.getUserId(), pageable);

		return new PageResponse<>(results.toList(), results.getTotalElements(), (long) size, (long) page,
				(long) results.getTotalPages());
	}

	/**
	 * 应用信息详情
	 *
	 * @param id id
	 */
	@Override
	public HandleSignAppInfoDTO queryHandleSignAppInfoDetail(String id) {
		return appInfoJpaRepository.queryHandleSignAppInfoDetail(id);
	}

	@Override
	public AppInfoDomainEntity findByHandleCode(String handleCode) {
		AppInfoEntity appInfo = appInfoJpaRepository.findByHandleCode(handleCode);
		return BeanUtil.copyProperties(appInfo, AppInfoDomainEntity.class);
	}

	@Override
	public List<AppInfoTreeResponse> queryAppInfoTree(Long userId) {
		// 1. 获取企业前缀视图数据
		List<AppInfoEntDTO> entPrefixViews = appInfoJpaRepository.findEntPrefix(userId);
		if (CollectionUtil.isEmpty(entPrefixViews)) {
			return Collections.emptyList(); // 使用不可变空列表
		}

		// 2. 获取并过滤应用信息
		Map<String, List<AppInfoEntity>> appsByEntPrefix = appInfoJpaRepository.findAll().stream()
				.filter(app -> app.getEntPrefix() != null && app.getIsDeleted() == 0)
				.collect(Collectors.groupingBy(AppInfoEntity::getEntPrefix));

		// 3. 构建响应列表
		return entPrefixViews.stream()
				.map(view -> {
					AppInfoTreeResponse response = new AppInfoTreeResponse();
					BeanUtil.copyProperties(view, response);
					response.setAppInfo(appsByEntPrefix.getOrDefault(view.getEntPrefix(), Collections.emptyList()));
					return response;
				})
				.toList();
	}

	@Override
	public AppInfoDomainEntity findById(Long id) {
		Optional<AppInfoEntity> optional = appInfoJpaRepository.findById(id);
		return optional.map(appInfo -> BeanUtil.copyProperties(appInfo, AppInfoDomainEntity.class)).orElse(null);

	}

	@Override
	public List<AppInfoEntity> findAll() {
		return appInfoJpaRepository.findAll();
	}

	@Override
	public List<String> findHandleCodeByUserId(Long id) {
		return appInfoJpaRepository.findHandleCodeByUserId(id);
	}
}
