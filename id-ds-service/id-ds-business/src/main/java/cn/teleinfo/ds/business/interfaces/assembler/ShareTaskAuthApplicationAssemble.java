package cn.teleinfo.ds.business.interfaces.assembler;

import cn.teleinfo.ds.business.application.command.ReviewCommand;
import cn.teleinfo.ds.business.domain.model.entity.AuthStatus;
import cn.teleinfo.ds.business.interfaces.dto.request.ReviewRequest;
import org.springframework.stereotype.Component;

@Component
public class ShareTaskAuthApplicationAssemble {

	public ReviewCommand toReviewCommand(Long shareTaskApplicationsId, ReviewRequest request) {
		ReviewCommand command = new ReviewCommand();
		command.setShareTaskApplicationsId(shareTaskApplicationsId);
		command.setAuditRemark(request.getAuditRemark());
		command.setAuthStatus(AuthStatus.findByCode(request.getAuthStatus()));
		return command;
	}
}
