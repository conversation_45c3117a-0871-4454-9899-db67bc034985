package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceItems;
import cn.teleinfo.ds.business.domain.repository.TargetSourceRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.TargetSourceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.TargetSourceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.TargetSourceView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.TargetSourceJpaRepository;
import cn.teleinfo.ds.common.core.util.PageResponse;
import jakarta.persistence.criteria.Predicate;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class TargetSourceRepositoryImpl implements TargetSourceRepository {

	private final TargetSourceJpaRepository targetSourceJpaRepository;

	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public Long save(TargetSourceDomainEntity targetSourceDomainEntity) {
		TargetSourceEntity entity = BeanUtil.copyProperties(targetSourceDomainEntity, TargetSourceEntity.class);
		targetSourceJpaRepository.save(entity);
		return entity.getId();
	}

	/**
	 * 查询应用下目标源名称是否存在
	 *
	 * @param appHandleCode    app handle code
	 * @param targetSourceName 目标源名称
	 * @return > 0 存在
	 */
	@Override
	public Integer findByAppHandleCodeAndTargetSourceNameCount(String appHandleCode, String targetSourceName) {
		TargetSourceEntity entity = targetSourceJpaRepository.findFirstByAppHandleCodeAndTargetSourceName(appHandleCode,
				targetSourceName);
		if (entity != null) {
			return 1;
		}

		return 0;
	}

	@Override
	public PageResponse<TargetSourceDomainEntity> listTargetSource(String appHandleCode, String targetSourceName,
																   Integer platformType, List<String> userHandleCode,
																   LocalDateTime start, LocalDateTime end, Integer page, Integer size) {
		int pageIndex = (page > 0) ? page - 1 : 0;
		Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");
		Pageable pageable = PageRequest.of(pageIndex, size, sort);

		Page<TargetSourceListView> p = targetSourceJpaRepository.findList(appHandleCode, targetSourceName, platformType, userHandleCode, start, end, pageable);

		List<TargetSourceDomainEntity> records = p.stream().map(view -> {
			TargetSourceDomainEntity targetSourceDomainEntity = new TargetSourceDomainEntity();
			targetSourceDomainEntity.setId(view.getId());
			targetSourceDomainEntity.setTargetSourceName(view.getTargetSourceName());
			targetSourceDomainEntity.setPlatformType(view.getPlatformType());
			targetSourceDomainEntity.setAppHandleCode(view.getAppHandleCode());
			targetSourceDomainEntity.setItems(view.getItems());
			targetSourceDomainEntity.setCreateTime(view.getCreateTime());
			targetSourceDomainEntity.setUpdateTime(view.getUpdateTime());
			targetSourceDomainEntity.setCreateBy(view.getCreateBy());
			targetSourceDomainEntity.setCreateByName(view.getCreateByName());
			targetSourceDomainEntity.setUpdateBy(view.getUpdateBy());
			targetSourceDomainEntity.setUpdateByName(view.getUpdateByName());
			return targetSourceDomainEntity;
		}).toList();

		return new PageResponse<>(records, p.getTotalElements(), (long) size, (long) page, (long) p.getTotalPages());
	}

	@Override
	public TargetSourceDomainEntity findById(Long id) {
		Optional<TargetSourceEntity> option = targetSourceJpaRepository.findById(id);
		return option.map(entity -> {
			TargetSourceDomainEntity e = BeanUtil.copyProperties(entity, TargetSourceDomainEntity.class);
			e.setTargetSourceItems(JSONUtil.toBean(e.getItems(), TargetSourceItems.class));
			return e;
		}).orElse(null);
	}

	@Override
	public TargetSourceView queryTargetSourceDetail(String id) {
		return targetSourceJpaRepository.queryTargetSourceDetail(id);
	}

	@Override
	public void delTargetSource(Long id) {
		targetSourceJpaRepository.deleteById(id);
	}

	@Override
	public List<TargetSourceDomainEntity> findAllByAppHandleCodeAndTargetSourceName(String appHandleCode, String targetSourceName) {
		return BeanUtil.copyToList(targetSourceJpaRepository.findAllByAppHandleCodeAndTargetSourceName(
				appHandleCode, targetSourceName), TargetSourceDomainEntity.class);
	}

	@Override
	public List<TargetSourceDomainEntity> findByAppHandleCode(String appHandleCode) {
		List<TargetSourceEntity> targetSourceEntities = targetSourceJpaRepository.findByAppHandleCode(appHandleCode);
		if (targetSourceEntities != null) {
			return BeanUtil.copyToList(targetSourceEntities, TargetSourceDomainEntity.class);
		}

		return null;
	}
}
