package cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection;

import java.time.LocalDateTime;

public interface SharedTaskInstanceListView {
    Long getId();
    Long getSharedTaskId();
    String getTaskInstanceNo();
    String getTaskNo();
    String getTaskName();
    Integer getTaskType();
    Integer getExecutionType();
    Integer getRunStatus();
    LocalDateTime getRunTime();
	String getLogPath();
	String getAppHandleName();
}