package cn.teleinfo.ds.business.domain.service.impl;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTaskInstance;
import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.repository.*;
import cn.teleinfo.ds.business.domain.service.ShareChannelsDomainService;
import cn.teleinfo.ds.business.domain.service.SharedTaskInstanceDomainService;
import cn.teleinfo.ds.business.domain.util.CronParseUtil;
import cn.teleinfo.ds.business.infrastructure.config.AppConfig;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.*;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskShareDataValueDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskStatusView;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationDetailDataResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceDetailResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceResponse;
import cn.teleinfo.ds.common.core.util.SqlUtils;
import cn.teleinfo.ds.common.log.util.BusinessLoggerOption;
import cn.teleinfo.ds.common.log.util.BusinessLoggerUtils;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import cn.teleinfo.ds.common.core.constant.UserConstants;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Submission;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@AllArgsConstructor
public class SharedTaskInstanceDomainServiceImpl implements SharedTaskInstanceDomainService {

	private final SharedTaskInstanceRepository repository;
	private final SharedTaskDetailsRepository sharedTaskDetailsRepository;

//	private final ShareTaskApplicationsDetailRepository detailRepository;

	private final RoleService roleService;

	private final AppInfoRepository appInfoRepository;

	private final SharedSubTaskInstanceRepository sharedSubTaskInstanceRepository;

	private final BusinessLoggerOption businessLoggerOption;

	private final HandlesRepository handlesRepository;
	private final ShareChannelRepository shareChannelRepository;
	private final PlatformConnectionRepository platformConnectionRepository;
	private final TargetSourceRepository targetSourceRepository;
	private final ShareChannelsDomainService shareChannelsDomainService;
	private final ShareDataSourcesRepository shareDataSourcesRepository;
	private final HcsRepository hcsRepository;
	private final AppConfig appConfig;


	@Override
	public PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query) {
		Long id = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(id);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				//获取该用户关联的所有应用
				List<String> handleCodes = appInfoRepository.findHandleCodeByUserId(id);
				if (!handleCodes.isEmpty()) {
					query.setUserHandleCode(handleCodes);
				}
			}
		}
		return repository.listSharedTaskInstances(query, query.getCurrent(), query.getSize());
	}

	@Override
	public SharedTaskInstanceDetailResponse getShareTaskInstanceDetail(Long instanceId) {
		SharedTaskInstanceView instanceById = repository.getSharedTaskInstanceById(instanceId);
		if (ObjectUtil.isEmpty(instanceById)) {
			return null;
		}
		SharedTaskInstanceDetailResponse instance = new SharedTaskInstanceDetailResponse();
		SharedTaskInstanceResponse instanceResponse = new SharedTaskInstanceResponse();
		BeanUtils.copyProperties(instanceById, instanceResponse);
		String cron = instanceById.getCronExpression();
		//翻译cron表达式
		instanceResponse.setCronDetail(CronParseUtil.parseToHumanReadable(cron));
		instanceResponse.setOperator(instanceResponse.getOperatorName());
		instance.setBasicInfo(instanceResponse);

		List<SharedTaskShareDataValueDTO> details = sharedTaskDetailsRepository.getSharedTaskShareDataValueDTO(instanceById.getSharedTaskId());
		if (CollectionUtil.isEmpty(details)) {
			return instance;
		}
		// 获取共享数据
		ArrayList<ShareTaskApplicationDetailDataResponse> detailValues = new ArrayList<>();
		details.forEach(detail -> {
			ShareTaskApplicationDetailDataResponse value = new ShareTaskApplicationDetailDataResponse();
			value.setId(detail.getId());
			value.setHandleName(detail.getHandleName());
			value.setHandle(detail.getHandle());
			value.setProvinceName(detail.getProvinceName());
			value.setEntName(detail.getEntName());
			value.setAppName(detail.getAppName());
			//value.setFields(detail.ge);

			detailValues.add(value);
		});

		instance.setShareData(detailValues);
		instance.setLog(readLog(instanceById.getLogPath()));
		return instance;
	}

	@Override
	public void deleteBySharedTaskId(Long id) {
		repository.deleteById(id);
	}

	/**
	 * 读取日志文件内容
	 *
	 * @param logPath 日志文件路径
	 * @return 日志文件内容
	 */
	public String readLog(String logPath) {
		if (StringUtils.isBlank(logPath)) {
			throw new RuntimeException("日志路径为空!");
		}

		String normalizedPath;
		try {
			normalizedPath = new File(logPath).getCanonicalPath();
		} catch (IOException e) {
			throw new RuntimeException("无法解析日志路径: " + logPath, e);
		}

		StringBuilder sb = new StringBuilder();
		try (BufferedReader br = new BufferedReader(new FileReader(normalizedPath))) {
			String line;
			while ((line = br.readLine()) != null) {
				sb.append(line).append('\n');
			}
		} catch (IOException e) {
			throw new RuntimeException("读取日志文件失败: " + normalizedPath, e);
		}

		return sb.toString();
	}

	@Override
	public void save(SharedTaskInstanceDomainEntity sharedTaskInstance) {
		Long id = repository.save(sharedTaskInstance);

		for (SharedSubTaskInstance sub : sharedTaskInstance.getSubs()) {
			sub.setSharedTaskInstanceId(id);
			Long subId = sharedSubTaskInstanceRepository.save(sub);
			sub.setId(subId);
		}

		sharedTaskInstance.setId(id);
	}

	/**
	 * 更新任务实例信息
	 * 任务执行时间
	 * 状态
	 * 共享数据总量等等
	 *
	 * @param sharedTaskInstance
	 */
	@Override
	public void updateRuntimeInfo(SharedTaskInstanceDomainEntity sharedTaskInstance) {
		for (SharedSubTaskInstance sub : sharedTaskInstance.getSubs()) {
			sharedSubTaskInstanceRepository.updateRuntimeInfo(sub);
		}

		repository.updateRuntimeInfo(sharedTaskInstance);
	}

	@Override
	public List<SharedTaskStatusView> getSharedTaskStatus(Long id, String type) {
		return repository.getSharedTaskStatus(id, type);
	}


	/**
	 * 共享任务执行
	 *
	 * @param sharedTaskInstance 执行实例聚合根；包含需要执行的子任务
	 * @return 本次执行实例的执行结果
	 */
	@Override
	public SharedTaskInstanceDomainEntity exec(SharedTaskInstance sharedTaskInstance) {

		var runTime = LocalDateTime.now(); // 共享任务执行时间
		var task = sharedTaskInstance.getSharedTask();

		// 1. 初始化日志
		var loggerPath = BusinessLoggerUtils.fullLogPath(businessLoggerOption.getLogPath(), task.getTaskNo());
		var logger = BusinessLoggerUtils.createLogger(loggerPath, Level.INFO);

		logger.info("共享任务开始执行 task={} runTime={}", task.getId(), runTime);

		// 2. 保存任务实例
		sharedTaskInstance.initTaskInstance(runTime, loggerPath.toString());
		var instance = sharedTaskInstance.getTaskInstance();
		Long id = repository.save(instance);
		instance.setId(id);

		try {

			// 3. 查询子任务
			List<SharedSubTaskInstance> subs = new ArrayList<>();
			List<SharedTaskDetail> details = task.getDetails();
			for (SharedTaskDetail detail : details) {
				var subTaskInstance = initSharedSubTaskInstance(task, instance, detail, logger);
				subs.add(subTaskInstance);
			}

			// 4. 分析湖仓映射关系
			// 多线程探测存在问题。如果多个标识存在相同通道会导致竞争。
			for (SharedSubTaskInstance subTaskInstance : subs) {
				var logPath = BusinessLoggerUtils.fullLogPath(businessLoggerOption.getLogPath(), task.getTaskNo(), subTaskInstance.getHandle());

				subTaskInstance.setRunTime(LocalDateTime.now());
				subTaskInstance.setSubTaskStatus(RunStatus.RUN.code());
				subTaskInstance.setLogPath(logPath.toString());
				subTaskInstance.setLoggerPath(logPath);

				Long subId = sharedSubTaskInstanceRepository.save(subTaskInstance); // 保存子任务实例
				subTaskInstance.setId(subId);
			}

			// 分析湖仓映射关系
			if (!shareChannelConnected(subs, logger, loggerPath)) {
				Duration duration = Duration.between(runTime, LocalDateTime.now());
				long seconds = duration.toSeconds();
				instance.setRunStatus(RunStatus.FAIL.code());
				instance.setRunDuration((int) seconds);
				repository.save(instance);
				return instance;
			}

			logger = BusinessLoggerUtils.createLogger(loggerPath, Level.INFO); // 重新创建

			// 5.子任务执行
			logger.info("子任务执行开始...");
			List<CompletableFuture<SharedSubTaskInstance>> futures = subs.stream()
					.map(sub -> CompletableFuture.supplyAsync(() -> execSubTask(sub)))
					.toList();

			// 6. 主任务等待子任务执行结束
			CompletableFuture<Void> future = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

			CompletableFuture<List<SharedSubTaskInstance>> result = future.thenApply(v -> futures.stream().map(CompletableFuture::join).toList());

			List<SharedSubTaskInstance> subTaskInstances = result.get();

			logger.info("子任务执行结束...");


			RunStatus runStatus = RunStatus.SUCCESS;
			StringBuilder logContent = new StringBuilder();
			int sharedDataCount = 0;
			for (SharedSubTaskInstance sub : subTaskInstances) {
				sharedDataCount += sub.getSharedDataCount();

				String s = FileUtil.readString(sub.getLoggerPath().toFile(), Charset.defaultCharset());
				logContent.append(s).append("\n");

				if (sub.getSubTaskStatus().equals(RunStatus.FAIL.code())) {
					runStatus = RunStatus.FAIL;
					logger.info("子任务 {} 执行失败, 失败原因 {}", sub.getHandle(), sub.getErrorMessage());
				}

				if (sub.getSubTaskStatus().equals(RunStatus.SUCCESS.code())) {
					logger.info("子任务 {} 执行成功", sub.getHandle());
				}

			}

			Duration duration = Duration.between(runTime, LocalDateTime.now());
			long seconds = duration.toSeconds();

			instance.setRunStatus(runStatus.code()); // 部分失败则主任务失败。
			instance.setRunDuration((int) seconds);
			instance.setSharedDataCount(sharedDataCount);

			repository.save(instance);

			BusinessLoggerUtils.destroy(logger);
			FileUtil.appendString(logContent.toString(), loggerPath.toFile(), Charset.defaultCharset());

			logger.info("共享任务执行结束 task={} runTime={}", task.getId(), runTime);
		} catch (Exception e) {
			logger.error("共享任务执行失败", e);

			Duration duration = Duration.between(runTime, LocalDateTime.now());
			long seconds = duration.toSeconds();
			instance.setRunStatus(RunStatus.FAIL.code());
			instance.setRunDuration((int) seconds);
			repository.save(instance);

		} finally {
			// 销毁日志
			BusinessLoggerUtils.destroy(logger);
		}

		return instance;
	}

	private SharedSubTaskInstance initSharedSubTaskInstance(SharedTaskDomainEntity task, SharedTaskInstanceDomainEntity instance,
															SharedTaskDetail detail, Logger logger) {
		SharedSubTaskInstance subTaskInstance = new SharedSubTaskInstance();
		subTaskInstance.setSharedTaskInstanceId(instance.getId());
		subTaskInstance.setTaskInstanceNo(instance.getTaskInstanceNo());
		subTaskInstance.setHandle(detail.getHandle());
		subTaskInstance.setSubTaskStatus(RunStatus.NO.code());
		subTaskInstance.setCurrentStep(SubTaskCurrentStep.NO.code());
		subTaskInstance.setRunTime(LocalDateTime.now());
		subTaskInstance.setRunDuration(0);
		subTaskInstance.setSharedDataCount(0);
		subTaskInstance.setExecutionType(instance.getExecutionType());


		if (StrUtil.isEmpty(detail.getFields()) && !JSONUtil.isTypeJSON(detail.getFields())) {
			var errorMessage = StrUtil.format("对象标识属性错误 handle={} fields={}", detail.getHandle(), detail.getFields());
			logger.error(errorMessage);

			subTaskInstance.setSubTaskStatus(RunStatus.FAIL.code());
			subTaskInstance.setErrorMessage(errorMessage);
			return subTaskInstance;
		}

		HandleDomainEntity handle = handlesRepository.findByHandle(detail.getHandle());
		if (handle == null) {
			var errorMessage = StrUtil.format("对象标识不存在或已删除 handle={}", detail.getHandle());
			logger.error(errorMessage);

			subTaskInstance.setSubTaskStatus(RunStatus.FAIL.code());
			subTaskInstance.setErrorMessage(errorMessage);
			return subTaskInstance;
		}

		logger.info("对象标识 handle={} name={}", detail.getHandle(), handle.getName());

		// 过滤同步字段
		if (handle.getHandleItems() == null || handle.getHandleItems().isEmpty()) {
			var errorMessage = StrUtil.format("对象标识不存在属性 handle={}", detail.getHandle());
			logger.error(errorMessage);

			subTaskInstance.setSubTaskStatus(RunStatus.FAIL.code());
			subTaskInstance.setErrorMessage(errorMessage);
			return subTaskInstance;
		}

		List<HandleItemDomainEntity> selectedItems = new ArrayList<>();

		// 选择需要同步的属性
		List<String> selectedFields = JSONUtil.toList(detail.getFields(), String.class);
		for (String selectedField : selectedFields) {
			for (HandleItemDomainEntity handleItem : handle.getHandleItems()) {
				if (StrUtil.equals(selectedField, handleItem.getField())) {
					selectedItems.add(handleItem);
				}
			}
		}

		if (selectedItems.isEmpty()) {
			var errorMessage = StrUtil.format("对象标识未选择同步属性 handle={}", detail.getHandle());
			logger.error(errorMessage);

			subTaskInstance.setSubTaskStatus(RunStatus.FAIL.code());
			subTaskInstance.setErrorMessage(errorMessage);
			return subTaskInstance;
		}

		// 查询共享通道
		HashMap<Long, ShareChannel> map = new HashMap<>();
		selectedItems.stream()
				.map(item -> {
					logger.info("对象标识需要同步属性 handle={} field={}", detail.getHandle(), item.getField());
					return item.getDataChannelId();
				}).distinct().forEach(dataChannelId -> {
					var enableChannel = shareChannelRepository.findByDataChannelIdAndChannelStatus(dataChannelId);
					if (enableChannel != null) {
						map.put(dataChannelId, enableChannel);
					}
				});

		selectedItems.forEach(item -> {
			var channel = map.get(item.getDataChannelId());
			if (channel == null) {
				var errorMessage = StrUtil.format("对象标识属性，不存在共享通道 handle={} field={}", detail.getHandle(), item.getField());
				logger.error(errorMessage);

				subTaskInstance.setSubTaskStatus(RunStatus.FAIL.code());
				subTaskInstance.setErrorMessage(errorMessage);
				return;
			}

			logger.info("属性共享通道 handle={} field={} channel={}", detail.getHandle(), item.getField(), channel.getShareChannelName());
			item.setShareChannel(channel);
		});


		handle.setHandleItems(selectedItems);

		// 查询共享源
		ShareDataSourcesDomainEntity shareDataSource = shareDataSourcesRepository.findByAppHandleCode(task.getAppHandleCode());
		if (shareDataSource == null) {
			var errorMessage = StrUtil.format("共享源不存在或已删除 handle={}", detail.getHandle());
			logger.error(errorMessage);

			subTaskInstance.setSubTaskStatus(RunStatus.FAIL.code());
			subTaskInstance.setErrorMessage(errorMessage);
			return subTaskInstance;
		}

		logger.info("共享源 handle={} shareDataSource={} ", detail.getHandle(), shareDataSource.getId());

		shareDataSource.setShareDataSourcesItem(JSONUtil.toBean(shareDataSource.getItems(), ShareDataSourcesItem.class));
		handle.setShareDataSources(shareDataSource);

		// 查询目标源
		var targetSource = targetSourceRepository.findById(task.getTargetSourceId());

		if (targetSource == null) {
			var errorMessage = StrUtil.format("目标源不存在或已删除 handle={}", detail.getHandle());
			logger.error(errorMessage);

			subTaskInstance.setSubTaskStatus(RunStatus.FAIL.code());
			subTaskInstance.setErrorMessage(errorMessage);
			return subTaskInstance;
		}

		logger.info("目标源 handle={} targetSource={} ", detail.getHandle(), targetSource.getTargetSourceName());

		var code = PlatformConnectionType.findByCode(targetSource.getPlatformType());
		var conn = platformConnectionRepository.findByPlatformType(code);

		if (conn == null) {
			var errorMessage = StrUtil.format("平台链接不存在或已删除 targetSource={}", targetSource.getTargetSourceName());
			logger.error(errorMessage);

			subTaskInstance.setSubTaskStatus(RunStatus.FAIL.code());
			subTaskInstance.setErrorMessage(errorMessage);
			return subTaskInstance;
		}

		subTaskInstance.setHandleEntity(handle);
		subTaskInstance.setTargetSource(targetSource);
		subTaskInstance.setConnection(conn);
		return subTaskInstance;
	}

	/**
	 * 子任务执行
	 *
	 * @param subTaskInstance 子任务实例
	 * @param taskNo          任务编码
	 * @return 子任务实例
	 */
	private SharedSubTaskInstance execSubTask(SharedSubTaskInstance subTaskInstance) {
		var runTime = LocalDateTime.now();

		var logger = BusinessLoggerUtils.createLogger(subTaskInstance.getLoggerPath(), Level.INFO);
		logger.info("子任务开始执行 handle={}", subTaskInstance.getHandle());

		subTaskInstance.setRunTime(runTime);
		subTaskInstance.setSubTaskStatus(RunStatus.RUN.code());
		subTaskInstance.setLogPath(subTaskInstance.getLoggerPath().toString());
		Long id = sharedSubTaskInstanceRepository.save(subTaskInstance); // 保存子任务实例
		subTaskInstance.setId(id);

		try {
			// 查询批量共享通道 SQL
			if (!shareChannelSql(subTaskInstance, logger)) {
				return subTaskInstance;
			}

			// 数据写入对象标识
			if (!writeHandle(subTaskInstance, logger)) {
				return subTaskInstance;
			}

			// 测试执行到此结束
			if (ExecutionType.TEST.code() == subTaskInstance.getExecutionType()) {
				subTaskInstance.setCurrentStep(SubTaskCurrentStep.END.code());
				saveSharedSubTaskInstance(subTaskInstance, RunStatus.SUCCESS, "测试结束");
				return subTaskInstance;
			}

			// 创建离线管道任务
			if (!offlineTask(subTaskInstance, logger)) {
				return subTaskInstance;
			}

			// 执行任务写入目标源
			if (!writeTargetSource(subTaskInstance, logger)) {
				return subTaskInstance;
			}

		} catch (Exception e) {
			logger.error("子任务执行失败 handle={}", subTaskInstance.getHandle(), e);

			saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, e.getMessage());
		} finally {
			BusinessLoggerUtils.destroy(logger);
		}

		return subTaskInstance;
	}

	private boolean writeTargetSource(SharedSubTaskInstance subTaskInstance, Logger logger) {
		logger.info("执行任务写入目标源 handle={}", subTaskInstance.getHandle());
		subTaskInstance.setCurrentStep(SubTaskCurrentStep.WRITE_TARGET_SOURCE.code());
		sharedSubTaskInstanceRepository.save(subTaskInstance);

		var platformConnection = new PlatformConnection(subTaskInstance.getConnection());
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = List.of(platformConnection.getHcsConnContent().getCdmEndpoint());

		ShareDataSourcesDomainEntity shareDataSources = subTaskInstance.getHandleEntity().getShareDataSources();
		ShareDataSourcesItem shareDataSourcesItem = shareDataSources.getShareDataSourcesItem();
		String projectId = shareDataSourcesItem.getProjectId();
		String clusterId = shareDataSourcesItem.getClusterId();

		// 启动作业
		List<CompletableFuture<JobResult>> futures = subTaskInstance.getJobNames().stream()
				.map(jobName -> CompletableFuture.supplyAsync(() -> {
					logger.info("启动作业 jobName={} ", jobName);
					return runJob(ak, sk, projectId, endpoints, clusterId, jobName, logger);
				})).toList();

		CompletableFuture<List<JobResult>> future = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).thenApply(v ->
				futures.stream().map(CompletableFuture::join).toList());

		try {
			// 获取所有表作业执行结果
			List<JobResult> jobResults = future.get();

			var flag = true; // 全部成功才算成功
			var sharedDataCount = 0;
			var message = new StringBuilder();

			for (JobResult result : jobResults) {
				if (!result.getSuccess()) {
					flag = false;
				}

				sharedDataCount += result.getWriteRows();
				message.append(result.getJobName())
						.append("\n")
						.append(result.getErrorMsg())
						.append("\n");
			}

			subTaskInstance.setSharedDataCount((int) sharedDataCount);
			subTaskInstance.setCurrentStep(SubTaskCurrentStep.END.code());
			saveSharedSubTaskInstance(subTaskInstance, RunStatus.SUCCESS, message.toString());

			return flag;

		} catch (Exception e) {
			saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, e.getMessage());
			return false;
		}
	}

	@AllArgsConstructor
	@Getter
	private static class JobResult {
		private String jobName;
		private Boolean success;
		private String errorMsg;
		private Long writeRows; // 写入行数
	}

	private JobResult runJob(String ak, String sk, String projectId, List<String> endpoints,
							 String clusterId, String jobName, Logger logger) {
		try {
			HcsBaseResponse startJobResp = hcsRepository.startJob(ak, sk, projectId, endpoints, clusterId, jobName);
			if (StrUtil.isNotEmpty(startJobResp.getErrorCode())) {
				logger.error("执行任务写入目标源，失败 jobName={} code={} message={}", jobName, startJobResp.getErrorCode(), startJobResp.getErrorMsg());
				return new JobResult(jobName, false, startJobResp.getErrorMsg(), 0L);
			}

			sleep(); // 随机睡眠一段时间

			while (true) {
				CdmJobStatusResponse cdmJobStatusResponse = hcsRepository.showJobStatus(ak, sk, projectId, endpoints, clusterId, jobName);

				if (StrUtil.isNotEmpty(cdmJobStatusResponse.getErrorCode())) {
					logger.error("执行任务写入目标源，失败 jobName={} code={} message={}", jobName, cdmJobStatusResponse.getErrorCode(), cdmJobStatusResponse.getErrorMsg());
					return new JobResult(jobName, false, startJobResp.getErrorMsg(), 0L);
				}

				Submission submission = cdmJobStatusResponse.getSubmissions().get(0);

				if (StrUtil.equals(HcsCmdRunStatus.BOOTING.code(), submission.getStatus())) {
					logger.info("作业启动中");

				} else if (StrUtil.equals(HcsCmdRunStatus.FAILURE_ON_SUBMIT.code(), submission.getStatus())) {
					logger.error("作业提交失败 {}", JSONUtil.toJsonStr(cdmJobStatusResponse));

					return new JobResult(jobName, false, JSONUtil.toJsonStr(cdmJobStatusResponse), 0L);
				} else if (StrUtil.equals(HcsCmdRunStatus.RUNNING.code(), submission.getStatus())) {
					logger.info("作业运行中");

				} else if (StrUtil.equals(HcsCmdRunStatus.SUCCEEDED.code(), submission.getStatus())) {
					Long writeRows = submission.getCounters().getOrgApacheSqoopSubmissionCounterSqoopCounters().getRowsWritten();
					logger.info("作业运行成功，写入数据 {}", writeRows);

					return new JobResult(jobName, true, "", writeRows);
				} else if (StrUtil.equals(HcsCmdRunStatus.FAILED.code(), submission.getStatus())) {
					logger.error("作业运行失败 {}", JSONUtil.toJsonStr(cdmJobStatusResponse));

					return new JobResult(jobName, false, JSONUtil.toJsonStr(cdmJobStatusResponse), 0L);
				} else if (StrUtil.equals(HcsCmdRunStatus.UNKNOWN.code(), submission.getStatus())) {
					logger.info("作业状态未知");
				}

				sleep(); // 随机睡眠一段时间
			}
		} catch (InterruptedException e) {
			return new JobResult(jobName, false, "作业被中断。" + e.getMessage(), 0L);
		}
	}

	// 写入数据到标识表
	private boolean writeHandle(SharedSubTaskInstance subTaskInstance, Logger logger) {
		logger.info("数据写入对象标识 handle={}", subTaskInstance.getHandle());
		subTaskInstance.setCurrentStep(SubTaskCurrentStep.WRITE_HANDLE.code());
		sharedSubTaskInstanceRepository.save(subTaskInstance);

		if (!genExtractSQL(subTaskInstance, logger)) {
			return false;
		}

		return writeOutputTables(subTaskInstance, logger);
	}

	// 分析湖仓映射关系
	private boolean shareChannelConnected(List<SharedSubTaskInstance> subs, Logger logger, Path logPath) {
		logger.info("分析湖仓映射关系开始...");

		Map<Long, ShareChannel> map = new HashMap<>();
		for (SharedSubTaskInstance sub : subs) {
			sub.setCurrentStep(SubTaskCurrentStep.MAPPING.code());
			sharedSubTaskInstanceRepository.save(sub);

			for (HandleItemDomainEntity handleItem : sub.getHandleEntity().getHandleItems()) {
				map.put(handleItem.getDataChannelId(), handleItem.getShareChannel());
			}
		}

		List<ShareChannel> channels = new ArrayList<>();
		map.forEach((k, v) -> {
			channels.add(v);
		});


		// 还原通道内的表
		List<Path> paths = new ArrayList<>(channels.size());
		String runResult = null;
		try {
			runResult = shareChannelsDomainService.shareChannelConnected4TaskExecute(channels, paths);
		} catch (Exception e) {
			logger.info("分析湖仓映射关系失败", e.getMessage());
			return false;
		}

		if (!paths.isEmpty()) {
			BusinessLoggerUtils.destroy(logger);
			// 追加通道探测日志
			for (Path path : paths) {
				if (path != null) {
					String s = FileUtil.readString(path.toFile(), Charset.defaultCharset());
					FileUtil.appendString(s, logPath.toFile(), Charset.defaultCharset());
				}
			}
		}

		return StrUtil.equals("success", runResult);
	}

	// 查询批量共享通道 SQL
	private boolean shareChannelSql(SharedSubTaskInstance subTaskInstance, Logger logger) {
		logger.info("查询批量共享通道 SQL handle={}", subTaskInstance.getHandle());

		subTaskInstance.setCurrentStep(SubTaskCurrentStep.SHARE_CHANNEL_SQL.code());
		sharedSubTaskInstanceRepository.save(subTaskInstance);

		if (!genOutputTablesSQL(subTaskInstance, logger)) {
			return false;
		}

		return createOutputTables(subTaskInstance, logger);
	}

	// 创建对象标识表
	private boolean createOutputTables(SharedSubTaskInstance subTaskInstance, Logger logger) {
		String scriptName = "outputSQL_" + subTaskInstance.getHandle().substring(subTaskInstance.getHandle().lastIndexOf("/") + 1);
		return execSQL(subTaskInstance, logger, subTaskInstance.getOutputTablesSQLContent(), scriptName);
	}

	// 执行脚本
	private boolean execSQL(SharedSubTaskInstance subTaskInstance, Logger logger, String SQL, String scriptName) {
		logger.info("开始执行脚本 scriptName={} scriptContent={}", scriptName, SQL);
		var handle = subTaskInstance.getHandleEntity();

		PlatformConnection platformConnection = new PlatformConnection(subTaskInstance.getConnection());
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = List.of(platformConnection.getHcsConnContent().getDgcEndpoint());

		// 共享源
		var shareDataSources = handle.getShareDataSources();
		var shareDataSourcesItem = shareDataSources.getShareDataSourcesItem();
		var projectId = shareDataSourcesItem.getProjectId();
		var workspace = shareDataSourcesItem.getWorkspace();
		var stdDataConnName = shareDataSourcesItem.getStdDataConnName();
		var stdDataDatabaseName = shareDataSourcesItem.getStdDataDatabaseName();

		ScriptInfo script;

		try {
			script = hcsRepository.findScript(ak, sk, projectId, endpoints, workspace, scriptName);
		} catch (Exception e) {
			var errorMessage = "查询脚本失败";
			logger.error(errorMessage, e);

			saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, e.getMessage());
			return false;
		}

		if (script != null) {
			try {
				hcsRepository.deleteScript(ak, sk, projectId, endpoints, scriptName, workspace);
			} catch (Exception e) {
				var errorMessage = "删除脚本失败";
				logger.error(errorMessage, e);

				saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, e.getMessage());
				return false;
			}
		}

		// 创建脚本
		try {
			hcsRepository.createScript(ak, sk, projectId, endpoints, scriptName, SQL, workspace, stdDataDatabaseName, stdDataConnName);
		} catch (Exception e) {
			var errorMessage = "创建脚本失败";
			logger.error(errorMessage, e);

			saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, e.getMessage());
			return false;
		}

		// 执行脚本
		String instanceId = null;
		try {
			instanceId = hcsRepository.executeScript(ak, sk, projectId, endpoints, scriptName, workspace);
		} catch (Exception e) {
			var errorMessage = "执行脚本失败";
			logger.error(errorMessage, e);

			saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, e.getMessage());
			return false;
		}

		boolean flag = false;
		while (true) {
			// 查询脚本实例执行结果
			ListScriptResultsResponseDTO results = null;

			try {
				results = hcsRepository.listScriptResults(ak, sk, projectId, endpoints, scriptName, workspace, instanceId);

				var status = results.getStatus();
				var message = results.getMessage();

				if (StrUtil.equals(HcsScriptRunStatus.LAUNCHING.code(), status)) {
					logger.info("脚本正在提交 scriptName={} status={}", scriptName, status);
				} else if (StrUtil.equals(HcsScriptRunStatus.RUNNING.code(), status)) {
					logger.info("脚本正在执行 scriptName={} status={}", scriptName, status);
				} else if (StrUtil.equals(HcsScriptRunStatus.FINISHED.code(), status)) {
					logger.info("脚本执行成功 scriptName={} status={}", scriptName, status);
					flag = true;
					break;
				} else if (StrUtil.equals(HcsScriptRunStatus.FAILED.code(), status)) {
					logger.error("脚本执行失败 scriptName={} status={} message={}", scriptName, status, message);
					saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, message);
					break;
				} else {
					logger.warn("执行脚本结果未知，获取到未知状态 scriptName={} status={}", scriptName, status);
				}
			} catch (Exception e) {
				logger.error("查询脚本错误", e);
			}

			try {
				sleep(); // 随机等待一段时间
			} catch (InterruptedException e) {
				logger.error("子任务被中断 handle={}", subTaskInstance.getHandle(), e);

				saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, e.getMessage());
				return false;
			}
		}

		return flag;
	}

	// 生成对象标识 SQL
	private boolean genOutputTablesSQL(SharedSubTaskInstance subTaskInstance, Logger logger) {
		HandleDomainEntity handle = subTaskInstance.getHandleEntity();
		StringBuilder allSQL = new StringBuilder();
		subTaskInstance.setOutputTables(new HashMap<>());

		logger.info("生成对象标识建表 SQL handle={}", subTaskInstance.getHandle());

		// 99.1000.1/YMZ12N35 => YMZ12N35
		String masterTableName = handle.getHandle().substring(handle.getHandle().lastIndexOf("/") + 1).toUpperCase();

		masterTableName = "TSTD_" + masterTableName + "_BASIC";

		// 基础属性
		List<HandleItemDomainEntity> basic = new ArrayList<>();

		// 扩展属性
		List<HandleItemDomainEntity> extend = new ArrayList<>();

		for (HandleItemDomainEntity item : handle.getHandleItems()) {
			if (FieldSourceType.BASIC.code() == item.getFieldSourceType()) {
				basic.add(item);
			}

			if (FieldSourceType.EXTEND.code() == item.getFieldSourceType()) {
				extend.add(item);
			}
		}

		// 基础属性
		StringBuilder basicCreateSQL = new StringBuilder();
		String basicDropSQL = "-- 创建【" + handle.getName() + "】基础属性表\n" +
				"DROP TABLE IF EXISTS " + masterTableName + ";\n";

		basicCreateSQL.append("CREATE TABLE IF NOT EXISTS ").append(masterTableName).append(" (\n");

		for (int i = 0; i < basic.size(); i++) {
			var item = basic.get(i);

			var field = StrUtil.toUpperCase(item.getField());

			basicCreateSQL.append("    ").append(field).append(" STRING COMMENT '").append(field).append("'");

			if (i < basic.size() - 1) {
				basicCreateSQL.append(",\n");
			} else {
				basicCreateSQL.append("\n");
			}
		}
		basicCreateSQL.append(") COMMENT '").append(handle.getName()).append("基础属性表';\n\n");

		var basicSql = basicCreateSQL.toString();
		allSQL.append(basicDropSQL).append(basicSql).append("\n");

		subTaskInstance.getOutputTables().put(masterTableName, basicSql);

		// 扩展属性 => 搞一个新表
		if (extend.isEmpty()) {
			logger.warn("对象标识无扩展属性 handle={}", handle.getHandle());
		} else {
			for (HandleItemDomainEntity item : extend) {
				var extendTableName = "TSTD_" + masterTableName + "_EXTEND_" + item.getField().toUpperCase();

				var sql = StrUtil.isEmpty(item.getShareChannel().getCustomSql()) ? item.getShareChannel().getDefaultSql() : item.getShareChannel().getCustomSql();

				List<String> fields = SqlUtils.extractSelectSqlField(sql);
				if (fields == null) {
					var message = StrUtil.format("扩展属性共享通道 SQL 错误。 handle={} SQL={}", handle.getHandle(), sql);
					logger.error(message);
					saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, message);
					return false;
				}

				StringBuilder extendCreteSQL = new StringBuilder();
				String extendDropSQL = "-- 创建【" + handle.getName() + "】扩展属性表\n" +
						"DROP TABLE IF EXISTS " + extendTableName + ";\n";

				extendCreteSQL.append("CREATE TABLE IF NOT EXISTS ").append(extendTableName).append(" (\n");

				for (int i = 0; i < fields.size(); i++) {
					String field = StrUtil.toUpperCase(fields.get(i));

					if (i == 0) {
						extendCreteSQL.append("    ").append(field).append(" STRING COMMENT '").append(field).append("'");
					} else {
						extendCreteSQL.append(",\n    ").append(field).append(" STRING COMMENT '").append(field).append("'");
					}
				}

				extendCreteSQL.append("\n) COMMENT '").append(handle.getName()).append("扩展属性表-").append(item.getField()).append("';\n\n");

				var extendSql = extendCreteSQL.toString();
				allSQL.append(extendDropSQL).append(extendSql).append("\n");

				subTaskInstance.getOutputTables().put(extendTableName, extendSql);
			}
		}

		subTaskInstance.setOutputTablesSQLContent(allSQL.toString());

		logger.info("生成对象标识建表 SQL SQL={}", subTaskInstance.getOutputTablesSQLContent());

		return true;
	}

	// 生成提取 SQL
	private boolean genExtractSQL(SharedSubTaskInstance subTaskInstance, Logger logger) {
		logger.info("开始生成提取 SQL handle={}", subTaskInstance.getHandle());

		var sqlBuilder = new StringBuilder();

		var handle = subTaskInstance.getHandleEntity();

		// 99.1000.1/YMZ12N35 => YMZ12N35
		String masterTableName = handle.getHandle().substring(handle.getHandle().lastIndexOf("/") + 1).toUpperCase();
		masterTableName = "TSTD_" + masterTableName + "_BASIC";

		// 基础属性
		List<HandleItemDomainEntity> basic = new ArrayList<>();

		// 扩展属性
		List<HandleItemDomainEntity> extend = new ArrayList<>();

		for (HandleItemDomainEntity item : handle.getHandleItems()) {
			if (FieldSourceType.BASIC.code() == item.getFieldSourceType()) {
				basic.add(item);
			}

			if (FieldSourceType.EXTEND.code() == item.getFieldSourceType()) {
				extend.add(item);
			}
		}

		// 基础属性。对应同一个通道。
		HandleItemDomainEntity item = basic.get(0);

		var basicSQL = StrUtil.isEmpty(item.getShareChannel().getCustomSql())
				? item.getShareChannel().getDefaultSql() :
				item.getShareChannel().getCustomSql();

		String basicCreteSQL = subTaskInstance.getOutputTables().get(masterTableName);

		String basicInsertSQL = null;
		try {
			basicInsertSQL = SqlUtils.genInsertSQLByCreateSQLAndSelectSQL(basicCreteSQL, basicSQL);
		} catch (Exception e) {
			var message = StrUtil.format("解析 SQL 失败  handle={} basicCreteSQL={} basicSQL={}", handle.getHandle(), basicCreteSQL, basicSQL);
			logger.error(message, e);

			saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, e.getMessage());

			return false;
		}

		if (basicInsertSQL == null) {
			var message = StrUtil.format("解析 SQL 失败  handle={} basicCreteSQL={} basicSQL={}", handle.getHandle(), basicCreteSQL, basicSQL);
			logger.error(message);

			saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, message);

			return false;
		}

		sqlBuilder.append("-- 生成基础属性表的数据抽取语句 ").append(masterTableName).append("\n");
		sqlBuilder.append(basicInsertSQL).append(";").append("\n");

		// 扩展属性
		for (HandleItemDomainEntity extendItem : extend) {
			var extendSQL = StrUtil.isEmpty(extendItem.getShareChannel().getCustomSql())
					? extendItem.getShareChannel().getDefaultSql() :
					extendItem.getShareChannel().getCustomSql();

			var extendTableName = "TSTD_" + masterTableName + "_EXTEND_" + extendItem.getField().toUpperCase();

			String extendCreteSQL = subTaskInstance.getOutputTables().get(extendTableName);

			String extendInsertSQL = null;
			try {
				extendInsertSQL = SqlUtils.genInsertSQLByCreateSQLAndSelectSQL(extendCreteSQL, extendSQL);
			} catch (Exception e) {
				var message = StrUtil.format("解析扩展字段 SQL 失败  handle={} field={} extendCreteSQL={} extendSQL={}",
						handle.getHandle(), extendItem.getField(), extendCreteSQL, extendSQL);
				logger.error(message, e);

				saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, message);

				return false;
			}
			if (extendInsertSQL == null) {
				var message = StrUtil.format("解析扩展字段 SQL 失败  handle={} field={} extendCreteSQL={} extendSQL={}",
						handle.getHandle(), extendItem.getField(), extendCreteSQL, extendSQL);
				logger.error(message);

				saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, message);

				return false;
			}

			sqlBuilder.append("-- 生成扩展属性表的数据抽取语句 ").append(extendTableName).append("\n");
			sqlBuilder.append(extendInsertSQL).append(";").append("\n");
		}


		var s = StrUtil.replace(sqlBuilder.toString(), "INSERT INTO ", "INSERT OVERWRITE TABLE ");
		subTaskInstance.setExtractSQLContent(s);

		return true;
	}

	private boolean offlineTask(SharedSubTaskInstance subTaskInstance, Logger logger) {
		logger.info("创建离线管道任务 handle={}", subTaskInstance.getHandle());

		subTaskInstance.setCurrentStep(SubTaskCurrentStep.OFFLINE_TASK.code());
		sharedSubTaskInstanceRepository.save(subTaskInstance);

		subTaskInstance.setJobNames(new ArrayList<>());

		var targetSourceItems = subTaskInstance.getTargetSource().getTargetSourceItems();

		var platformConnection = new PlatformConnection(subTaskInstance.getConnection());
		var ak = platformConnection.getHcsConnContent().getAk();
		var sk = platformConnection.getHcsConnContent().getSk();
		var endpoints = List.of(platformConnection.getHcsConnContent().getCdmEndpoint());

		ShareDataSourcesDomainEntity shareDataSources = subTaskInstance.getHandleEntity().getShareDataSources();
		ShareDataSourcesItem shareDataSourcesItem = shareDataSources.getShareDataSourcesItem();
		String projectId = shareDataSourcesItem.getProjectId();
		String stdDataConnName = shareDataSourcesItem.getStdDataConnName();
		String stdDataDatabaseName = shareDataSourcesItem.getStdDataDatabaseName();
		String clusterId = shareDataSourcesItem.getClusterId();


		for (Map.Entry<String, String> entry : subTaskInstance.getOutputTables().entrySet()) {
			var tableName = entry.getKey();
			var sql = entry.getValue();

			var table = new CreateJobTableDTO();
			var t = SqlUtils.extractCreateSqlTable(sql);


			if (t == null) {
				var message = StrUtil.format("对象标识建表语句错误 sql={}", sql);
				logger.error(message);

				saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, message);

				return false;
			}

			table.setTableName(t.getTableName());
			table.setColumnList(t.getColumnList());

			logger.info("开始创建作业 table={} columnList={}", table.getTableName(), CollUtil.join(table.getColumnList(), "$"));

			// 在目标源创建表
			var mysql = SqlUtils.hiveToMysql(sql);

			mysql = "DROP TABLE IF EXISTS " + tableName + ";\n" + mysql;

			// TODO 目标源数据库链接替换为代理
			String databaseUrl = targetSourceItems.getDatabaseUrl();
			if (StrUtil.equals("99.1000", appConfig.getProvincePrefix())) {
				databaseUrl = "10.14.145.200";
			}

			String jdbcUrl = StrUtil.format("jdbc:postgresql://{}:{}/{}?currentSchema={}&sslmode=disable",
					databaseUrl, targetSourceItems.getPort(), targetSourceItems.getDatabaseName(), targetSourceItems.getDatabaseName());

			try {
				hcsRepository.createTable(jdbcUrl, targetSourceItems.getDatabaseUsername(), targetSourceItems.getDatabasePassword(), mysql);
			} catch (Exception e) {
				logger.error("目标源创建表失败", e);
				saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, e.getMessage());
				return false;
			}

			// 创建作业
			String jobName = table.getTableName().toLowerCase();

			CdmJobResponse cdmJobResponse = hcsRepository.showJobs(ak, sk, projectId, endpoints, clusterId, jobName);
			if (StrUtil.isNotEmpty(cdmJobResponse.getErrorCode()) && !StrUtil.equals(CdmErrCode.JOB_NOT_EXIST.code(), cdmJobResponse.getErrorCode())) {
				logger.error("查询作业，失败 code={} message={}", cdmJobResponse.getErrorCode(), cdmJobResponse.getErrorMsg());
			}

			if (cdmJobResponse.getJobs() != null && !cdmJobResponse.getJobs().isEmpty()) {
				for (Job job : cdmJobResponse.getJobs()) {
					HcsBaseResponse resp = hcsRepository.deleteJob(ak, sk, projectId, endpoints, clusterId, job.getName());
					if (StrUtil.isNotEmpty(resp.getErrorCode())) {
						logger.error("删除作业，失败 code={} message={}", resp.getErrorCode(), resp.getErrorMsg());
					}
				}
			}

			HcsBaseResponse resp = hcsRepository.createJob(ak, sk, projectId, endpoints, clusterId, table, stdDataConnName, stdDataDatabaseName,
					targetSourceItems.getLinkName(), targetSourceItems.getDatabaseName());
			if (StrUtil.isNotEmpty(resp.getErrorCode())) {
				logger.error("创建作业，失败 code={} message={}", resp.getErrorCode(), resp.getErrorMsg());

				saveSharedSubTaskInstance(subTaskInstance, RunStatus.FAIL, resp.getErrorMsg());

				return false;
			}

			subTaskInstance.getJobNames().add(jobName);
		}

		return true;
	}


	private boolean writeOutputTables(SharedSubTaskInstance subTaskInstance, Logger logger) {
		String scriptName = "extractSQL_" + subTaskInstance.getHandle().substring(subTaskInstance.getHandle().lastIndexOf("/") + 1);
		return execSQL(subTaskInstance, logger, subTaskInstance.getExtractSQLContent(), scriptName);
	}

	private void saveSharedSubTaskInstance(SharedSubTaskInstance subTaskInstance, RunStatus runStatus, String message) {
		Duration duration = Duration.between(subTaskInstance.getRunTime(), LocalDateTime.now());
		long seconds = duration.toSeconds();
		subTaskInstance.setSubTaskStatus(runStatus.code());
		subTaskInstance.setRunDuration((int) seconds);
		subTaskInstance.setErrorMessage(message);

		sharedSubTaskInstanceRepository.save(subTaskInstance);
	}

	private void sleep() throws InterruptedException {
		long millis = RandomUtil.randomLong(5000, 8000);
		Thread.sleep(millis);
	}

}
