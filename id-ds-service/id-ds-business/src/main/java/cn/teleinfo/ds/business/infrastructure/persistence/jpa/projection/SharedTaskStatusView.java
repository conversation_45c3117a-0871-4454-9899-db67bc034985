package cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection;

import java.time.LocalDateTime;

public interface SharedTaskStatusView {
	String getId();
	String getSubId();
	String getHandle();
	String getHandleName();
	String getSubTaskStatus();
	String getCurrentStep();
	LocalDateTime getRunTime();
	String getRunDuration();
	String getSharedDataCount();
	String getErrorMessage();
	String getExecutionType();
	String getTaskInstanceNo();
	String getSourceName();
	Integer getShareType();
}