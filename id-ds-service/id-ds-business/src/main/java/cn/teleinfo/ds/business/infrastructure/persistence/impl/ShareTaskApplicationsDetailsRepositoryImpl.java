package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.repository.ShareTaskApplicationsDetailsRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsDetailsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareTaskApplicationsDetailsJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Optional;

@AllArgsConstructor
@Component
public class ShareTaskApplicationsDetailsRepositoryImpl implements ShareTaskApplicationsDetailsRepository {
	private final ShareTaskApplicationsDetailsJpaRepository shareTaskApplicationsDetailsJpaRepository;

	@Override
	public ShareTaskApplicationsDetailsDomainEntity findById(Long id) {
		Optional<ShareTaskApplicationsDetailsEntity> optional = shareTaskApplicationsDetailsJpaRepository.findById(id);
		return optional.map(d -> BeanUtil.copyProperties(d, ShareTaskApplicationsDetailsDomainEntity.class))
				.orElse(null);
	}
}
