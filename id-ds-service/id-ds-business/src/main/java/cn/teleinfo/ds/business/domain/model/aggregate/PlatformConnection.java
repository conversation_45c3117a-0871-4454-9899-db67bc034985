package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.entity.HcsConnection;
import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionDomainEntity;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class PlatformConnection {
	private PlatformConnectionDomainEntity platformConnection;

	private HcsConnection hcsConnContent;

	public void check() {
		if (platformConnection == null) {
			throw new CheckedException("未配置系统连接");
		}

		if (hcsConnContent == null) {
			throw new CheckedException("未配置系统连接");
		}
	}

	public PlatformConnection(PlatformConnectionDomainEntity platformConnectionDomainEntity) {
		this.platformConnection = platformConnectionDomainEntity;

		this.hcsConnContent = switch (platformConnectionDomainEntity.getPlatformType()) {
			case HCS -> JSONUtil.toBean(platformConnectionDomainEntity.getPlatformConnection(), HcsConnection.class);
			case ALI, CUSTOM -> throw new CheckedException("暂不支持其他平台");
		};
	}


}

