package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.GraphHandlesDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface HandleJpaRepository extends BaseRepository<HandleEntity, Long> {

	@Query(nativeQuery = true, value = "select max(update_time) from t_handle ")
	LocalDateTime findMaxUpdatedTime();

	List<Long> findIdBy();

	HandleEntity findByHandle(String handle);

	@Query(nativeQuery = true, value = "select " +
			"d.org_name as provinceName, " +
			"c.org_name as entName, " +
			"b.app_name as appName, " +
			"a.name as handleName, " +
			"a.handle as handleCode " +
			"from  t_handle a " +
			"left join t_app_info b on a.app_handle_code = b.handle_code and b.is_deleted = 0 " +
			"left join t_ent_prefix c on a.ent_prefix = c.ent_prefix " +
			"left join t_province_prefix d on a.province_prefix = d.province_prefix " +
			"where IF(:handle != '' and :handle is not null, " +
			"a.name like CONCAT('%',:handle,'%') or a.handle like CONCAT('%',:handle,'%'), 1=1) " +
			"and a.is_deleted = 0 ")
	List<GraphHandlesDTO> getGraphHandlesByHandle(@Param("handle") String handle);


	@Query(nativeQuery = true, value = """
			SELECT
			    h.id AS id,
				h.name AS handleName,
			    h.handle AS handle,
				ep.app_name as appName,
				h.entity_type as entityType,
				h.update_time as updateTime
			    from t_handle h
				left join t_app_info  ep on ep.handle_code = h.app_handle_code
			where IF(:handle != '' and :handle IS NOT NULL, h.handle = :handle, 1=1 )
			  AND IF(:appHandleCode != '' and :appHandleCode  IS NOT NULL, h.app_handle_code = :appHandleCode, 1=1 )
			  AND IF(COALESCE(:userHandleCode) IS NOT NULL, h.app_handle_code in (:userHandleCode), 1=1)
			  and IF(:name != '' AND :name is not null, h.name like CONCAT('%',:name,'%'), 1=1 )
			  AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, h.update_time BETWEEN :startTime AND :endTime , 1=1)			   
				AND h.is_deleted = 0 order by h.update_time desc
			    """, countQuery = """
			SELECT
				count(*)
				from t_handle h
				left join t_app_info  ep on ep.handle_code = h.app_handle_code
			    where IF(:handle != '' and :handle IS NOT NULL, h.handle = :handle, 1=1 )
			  AND IF(:appHandleCode != '' and :appHandleCode  IS NOT NULL, h.app_handle_code = :appHandleCode, 1=1 )
			  AND IF(COALESCE(:userHandleCode) IS NOT NULL, h.app_handle_code in (:userHandleCode), 1=1)
			  and IF(:name != '' AND :name is not null, h.name like CONCAT('%',:name,'%'), 1=1 )
			  AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, h.update_time BETWEEN :startTime AND :endTime , 1=1)			   
				AND h.is_deleted = 0
				"""
	)
	Page<HandleListView> findListHandles(@Param("appHandleCode") String appHandleCode, @Param("name") String name, @Param("handle")String handle, @Param("userHandleCode") List<String> userHandleCode, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, Pageable pageable);

	@Query(nativeQuery = true,value = """
			SELECT * from t_handle where id in :ids;
			""")
    List<HandleEntity> findAllByIds(@Param("ids") List<Long> ids);

	@Query(nativeQuery = true,
			value = "select distinct " +
					"a.id as id, " +
					"a.name as name, " +
					"a.handle as handle, " +
					"b.org_name as provinceName, " +
					"c.org_name as entName, " +
					"d.app_name as appName " +
					"from t_handle a " +
					"left join t_province_prefix b on a.province_prefix = b.province_prefix " +
					"left join t_ent_prefix c on a.ent_prefix = c.ent_prefix " +
					"left join t_app_info d on a.app_handle_code = d.handle_code and d.is_deleted = 0 " +
					"where a.handle = :handle and a.is_deleted = 0 "
	)
	SharedTaskView getHandleDetails(@Param("handle") String handle);

	@Query(nativeQuery = true,
			value = """
			SELECT
				distinct 
			    h.id AS id,
				h.name AS handleName,
			    h.handle AS handle,
				ep.app_name as appName,
				h.entity_type as entityType,
				h.update_time as updateTime
			    from t_handle h
				left join t_app_info  ep on ep.handle_code = h.app_handle_code
				where h.id = :id 
			"""
	)
	HandleListView findHandleDetailById(@Param("id") Long id);
}
