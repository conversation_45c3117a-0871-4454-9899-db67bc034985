package cn.teleinfo.ds.business.domain.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

// 领域层-实体
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HandleDomainEntity {
	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除: 0 未删除 null 已删除
	 */
	private Integer isDeleted;
	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 通配符
	 */
	private String wildcard;

	/**
	 * 标识名称
	 */
	private String name;

	/**
	 * 标识
	 */
	private String handle;

	/**
	 * 实体类型 1业务实体 2资源实体
	 */
	private Integer entityType;

	/**
	 * 应用标识身份
	 */
	private String appHandleCode;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;
	private String provinceName;
	/**
	 * 应用名称
	 */
	private String appName;
	/**
	 * 企业名称
	 */
	private String entName;

	/**
	 * 对象标识属性
	 */
	private List<HandleItemDomainEntity> handleItems;

	/**
	 * 共享源
	 */
	private ShareDataSourcesDomainEntity shareDataSources;

	public HandleDomainEntity(String name, String appHandleCode, String handle) {
		this.name = name;
		this.appHandleCode = appHandleCode;
		this.handle = handle;
	}

}
