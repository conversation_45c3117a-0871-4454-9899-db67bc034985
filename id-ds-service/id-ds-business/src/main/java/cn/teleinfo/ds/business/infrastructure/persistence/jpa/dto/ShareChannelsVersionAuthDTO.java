package cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareChannelsVersionAuthDTO {

	/**
	 * 共享通道id
	 */
	private String shareChannelId;

	/**
	 * 审核时间
	 */
	private Timestamp updatedTime;

	/**
	 * 审核状态
	 */
	private Integer channelStatus;

	/**
	 * 审核人
	 */
	private String auditUser;

	/**
	 * 失败原因
	 */
	private String auditRemark;

	/**
	 * 主版本号
	 */
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	private Integer minorVersion;

}
