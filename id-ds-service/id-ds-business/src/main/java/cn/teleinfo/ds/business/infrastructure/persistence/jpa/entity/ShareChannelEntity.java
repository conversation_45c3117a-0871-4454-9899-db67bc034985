package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

/**
 * 共享通道信息表
 */
@Getter
@Setter
@Entity
@Table(name = "t_share_channel")
@SQLDelete(sql = "update t_share_channel set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class ShareChannelEntity extends AuditableEntity {

	/**
	 * 共享通道ID
	 */
	@Column(name = "share_channel_id")
	private Long shareChannelId;

	/**
	 * 共享通道名称
	 */
	@Column(name = "share_channel_name")
	private String shareChannelName;

	/**
	 * 所属对象标识编码
	 */
	@Column(name = "object_handle")
	private String objectHandle;

	/**
	 * 数据通道ID
	 */
	@Column(name = "data_channel_id")
	private Long dataChannelId;

	/**
	 * 实例数据类型
	 */
	@Column(name = "data_type")
	private Integer dataType;

	/**
	 * 主版本号
	 */
	@Column(name = "main_version")
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	@Column(name = "minor_version")
	private Integer minorVersion;

	/**
	 * 自动sql
	 */
	@Column(name = "default_sql", columnDefinition = "text")
	private String defaultSql;

	/**
	 * 手动sql
	 */
	@Column(name = "custom_sql", columnDefinition = "text")
	private String customSql;

	/**
	 * 探测状态
	 */
	@Column(name = "detection_status")
	private Integer detectionStatus;

	/**
	 * 启用状态
	 */
	@Column(name = "channel_status")
	private Integer channelStatus;

	/**
	 * 省级前缀
	 */
	@Column(name = "province_prefix")
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	@Column(name = "ent_prefix")
	private String entPrefix;

	/**
	 * 应用身份编码
	 */
	@Column(name = "app_handle_code")
	private String appHandleCode;

	/**
	 * 变更原因
	 */
	@Column(name = "change_reason")
	private String changeReason;

}