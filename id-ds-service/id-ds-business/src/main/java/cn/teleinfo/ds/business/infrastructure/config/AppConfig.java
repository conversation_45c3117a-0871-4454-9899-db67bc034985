package cn.teleinfo.ds.business.infrastructure.config;

import lombok.Data;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
@Data
public class AppConfig {

	@Value("${ds.province-prefix}")
	private String provincePrefix;

	@Value("${ds.province-org-name}")
	private String provinceOrgName;


	@Bean
	public OkHttpClient okHttpClient() {
		return new OkHttpClient.Builder()

				.retryOnConnectionFailure(false)
				.connectTimeout(30, TimeUnit.SECONDS)
				.writeTimeout(10, TimeUnit.SECONDS)
				.readTimeout(20, TimeUnit.SECONDS)
				.build();
	}
}
