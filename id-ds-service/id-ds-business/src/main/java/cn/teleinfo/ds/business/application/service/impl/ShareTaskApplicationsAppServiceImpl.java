package cn.teleinfo.ds.business.application.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.application.service.ShareTaskApplicationsAppService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareDataDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.EntPrefixDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.UserInfo;
import cn.teleinfo.ds.business.domain.service.AppInfoDomainService;
import cn.teleinfo.ds.business.domain.service.EntPrefixDomainService;
import cn.teleinfo.ds.business.domain.service.HandlesDomainService;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsDetailsService;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsDomainService;
import cn.teleinfo.ds.business.domain.service.TargetSourceDomainService;
import cn.teleinfo.ds.business.domain.service.UserDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.UserEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.security.service.PigUser;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class ShareTaskApplicationsAppServiceImpl implements ShareTaskApplicationsAppService {

	private final ShareTaskApplicationsDomainService shareTaskApplicationsDomainService;
	private final AppInfoDomainService appInfoDomainService;
	private final EntPrefixDomainService entPrefixDomainService;
	private final TargetSourceDomainService targetSourceDomainService;
	private final UserDomainService userDomainService;
	private final ShareTaskApplicationsDetailsService shareTaskApplicationsDetailsService;
	private final HandlesDomainService handlesDomainService;


	/**
	 * 创建共享任务申请
	 *
	 * @param command 共享任务申请命令
	 */
	@Override
	public void createShareTaskApplications(ShareTaskApplicationsCommand command) {
		shareTaskApplicationsDomainService.createShareTaskApplications(command);
	}

	/**
	 * 更新共享任务申请
	 *
	 * @param applicationId 共享任务申请ID
	 * @param command       共享任务申请命令
	 */
	@Override
	public void updateShareTaskApplications(Long applicationId, ShareTaskApplicationsCommand command) {
		shareTaskApplicationsDomainService.updateShareTaskApplications(applicationId, command);
	}

	/**
	 * 查询共享任务申请列表
	 *
	 * @param query 共享任务申请查询条件
	 * @return 分页查询结果
	 */
	@Override
	public PageResponse<ShareTaskApplicationsView> listShareTaskApplications(ShareTaskApplicationsQuery query) {
		return shareTaskApplicationsDomainService.listShareTaskApplications(query);
	}

	/**
	 * 获取共享任务申请详情
	 *
	 * @param applicationId 共享任务申请ID
	 * @return 共享任务申请详情
	 */
	@Override
	public ShareTaskApplicationDetails getShareTaskApplicationDetail(Long applicationId) {
		return shareTaskApplicationsDomainService.getShareTaskApplicationDetail(applicationId);
	}

	/**
	 * 根据handle获取handleItems
	 *
	 * @param query handle
	 * @return handleItems
	 */
	@Override
	public HandleApplicationResponse getHandleItemsByHandle(HandleItemQuery query) {
		return shareTaskApplicationsDomainService.getHandleItemsByHandle(query);
	}

	@Override
	public void deleteShareTaskApplications(Long applicationId) {
		shareTaskApplicationsDomainService.deleteShareTaskApplications(applicationId);
	}

	@Override
	public List<TargetSource> findTargetSource() {
		List<TargetSource> targetSources = new ArrayList<>();

		PigUser user = SecurityUtils.getUser();

		UserInfo userInfo = userDomainService.findById(user.getUsername());
		if (userInfo.getAppIds() == null || userInfo.getAppIds().isEmpty()) {
			throw new CheckedException("当前用户未绑定应用！");
		}

		for (Long appId : userInfo.getAppIds()) {
			AppInfoDomainEntity appInfo = appInfoDomainService.findById(appId);
			if (appInfo != null) {
				if(StrUtil.isNotEmpty(appInfo.getEntPrefix())){
					EntPrefixDomainEntity ent = entPrefixDomainService.findByEntPrefix(appInfo.getEntPrefix());
					if (ent != null) {
						appInfo.setEntName(ent.getOrgName());
					}
				}


				List<TargetSourceDomainEntity> targetSourceDomainEntities = targetSourceDomainService.findByAppHandleCode(appInfo.getHandleCode());
				if (targetSourceDomainEntities != null) {
					for (TargetSourceDomainEntity targetSourceDomainEntity : targetSourceDomainEntities) {
						TargetSource targetSource = new TargetSource();
						targetSource.setTargetSourceDomainEntity(targetSourceDomainEntity);
						targetSource.setAppInfo(appInfo);
						targetSources.add(targetSource);
					}
				}
			}
		}


		return targetSources;
	}

	@Override
	public ShareTaskApplicationsDomainEntity getShareTaskApplicationGraph(Long applicationId) {
		ShareTaskApplicationsDomainEntity shareTaskApplicationsDomainEntity = shareTaskApplicationsDomainService
				.findShareTaskApplicationsDomainEntity(applicationId);

		TargetSourceDomainEntity targetSource = targetSourceDomainService.findById(shareTaskApplicationsDomainEntity.getTargetSourceId());
		shareTaskApplicationsDomainEntity.setTargetSource(targetSource);

		AppInfoDomainEntity appInfo = appInfoDomainService.findByHandleCode(shareTaskApplicationsDomainEntity.getAppHandleCode());
		shareTaskApplicationsDomainEntity.setAppInfo(appInfo);

		EntPrefixDomainEntity ent = entPrefixDomainService.findByEntPrefix(shareTaskApplicationsDomainEntity.getEntPrefix());
		shareTaskApplicationsDomainEntity.setEntPrefixDomainEntity(ent);

		if (shareTaskApplicationsDomainEntity.getDetailsDomainEntities() != null && !shareTaskApplicationsDomainEntity.getDetailsDomainEntities().isEmpty()) {
			for (ShareTaskApplicationsDetailsDomainEntity details : shareTaskApplicationsDomainEntity.getDetailsDomainEntities()) {
				ShareTaskApplicationsDetailsDomainEntity detailsDomainEntity = shareTaskApplicationsDetailsService.findById(details.getId());

				details.setDescription(detailsDomainEntity.getDescription());
				details.setFieldSourceType(detailsDomainEntity.getFieldSourceType());


				AppInfoDomainEntity detailsAppInfo = appInfoDomainService.findByHandleCode(detailsDomainEntity.getAppHandleCode());
				details.setAppInfo(detailsAppInfo);

				EntPrefixDomainEntity detailsEnt = entPrefixDomainService.findByEntPrefix(detailsDomainEntity.getEntPrefix());
				details.setEntPrefixDomainEntity(detailsEnt);

				List<String> fields = JSONUtil.toList(details.getFields(), String.class);
				List<HandleItemDomainEntity> handleItems = new ArrayList<>();


				HandleDomainEntity handle = handlesDomainService.findByHandle(detailsDomainEntity.getHandle());
				for (HandleItemDomainEntity item : handle.getHandleItems()) {
					if(fields.contains(item.getField())){
						handleItems.add(item);
					}
				}

				handle.setHandleItems(handleItems);
				details.setHandleDomainEntity(handle);
			}
		}

		return shareTaskApplicationsDomainEntity;
	}

	@Override
	public ShareDataDetails getHandleDetails(Long dataId) {
		return shareTaskApplicationsDomainService.getHandleDetails(dataId);
	}
}
