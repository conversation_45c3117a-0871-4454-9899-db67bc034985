package cn.teleinfo.ds.business.infrastructure.persistence.jpa.snowflake;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(SnowFlakeOption.class)
public class SnowFlakeConfiguration {

	@Bean
	public Snowflake snowflake(SnowFlakeOption option) {
		return IdUtil.getSnowflake(option.getWorkerId(), option.getDatacenterId());
	}

}
