package cn.teleinfo.ds.business.interfaces.dto.request;

import lombok.Data;

@Data
public class TargetSourceItemsRequest {

	/**
	 * 连接器类型
	 */
	private String connType;

	/**
	 * 数据库服务器
	 */
	private String databaseUrl;

	/**
	 * 端口
	 */
	private Integer port;

	/**
	 * 数据库名称
	 */
	private String databaseName;

	/**
	 * 数据库用户名
	 */
	private String databaseUsername;

	/**
	 * 数据库密码
	 */
	private String databasePassword;
}
