package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.domain.model.valueobject.Cart;
import cn.teleinfo.ds.business.interfaces.dto.request.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ShareTaskApplications {


	private Long id;

	/**
	 * 共享任务名称
	 */
	private String taskName;

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 任务类型(1：手动任务 2：定时任务)
	 */
	private Integer taskType;

	/**
	 * 目标源ID
	 */
	private Long targetSourceId;

	/**
	 * 任务类型(1：手动任务 2：定时任务)
	 */
	private Integer applicationsStatus;

	private Integer targetId;

	/**
	 * 应用系统编码
	 */
	private String appHandleCode;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 定时任务详情
	 */
	private String taskCron;

	/**
	 * 根节点
	 */
	private String rootHandle;

	/**
	 * 省份前缀
	 */
	private String provincePrefix;

	/**
	 * 购物车
	 */
	private List<Cart> cart;

	/**
	 * 图谱信息
	 */
	private String graph;

	/**
	 * 开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 定时任务表达式
	 */
	private String cronExpression;


	public Cart setCartObject(CartRequest cartObject) {
		Cart cart = new Cart();
		cart.setHandle(cartObject.getHandle());
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			cart.setFields(objectMapper.writeValueAsString(cartObject.getFields()));
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
		return cart;
	}

	/**
	 * 新增数据组装
	 *
	 * @param request 请求参数
	 * @return 共享任务信息
	 */
	public ShareTaskApplications setShareTaskApplications(ShareTaskApplicationsCommand request) {

		// 组装参数
		ShareTaskApplications shareTaskApplications = new ShareTaskApplications();
		if (request.getId() != null) {
			shareTaskApplications.setId(request.getId());
		}
		shareTaskApplications.setTaskName(request.getTaskName());
		// 默认任务状态为1 申请中
		shareTaskApplications.setApplicationsStatus(1);

		shareTaskApplications.setTaskType(request.getTaskType());
		shareTaskApplications.setTargetSourceId(request.getTargetSourceId());
		shareTaskApplications.setRootHandle(request.getRootHandle());
		shareTaskApplications.setAppHandleCode(request.getAppHandleCode());
		shareTaskApplications.setEntPrefix(request.getEntPrefix());
		shareTaskApplications.setProvincePrefix(request.getProvincePrefix());
		shareTaskApplications.setCronExpression(request.getCronExpression());
		// 图谱信息
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL); // NULL 值不在序列化。
		if (ObjectUtil.isNotNull(request.getGraph())) {
			try {
				String string = objectMapper.writeValueAsString(request.getGraph());
				shareTaskApplications.setGraph(string);
			} catch (JsonProcessingException e) {
				throw new RuntimeException(e);
			}
		}
		// 购物车信息
		if (CollectionUtil.isNotEmpty(request.getCart())) {
			ArrayList<Cart> carts = new ArrayList<>();
			for (CartRequest cartObject : request.getCart()) {
				Cart cart1 = new Cart();
				cart1.setHandle(cartObject.getHandle());
				try {
					cart1.setFields(objectMapper.writeValueAsString(cartObject.getFields()));
				} catch (JsonProcessingException e) {
					throw new RuntimeException(e);
				}

				carts.add(cart1);
			}
			shareTaskApplications.setCart(carts);
		}
		return shareTaskApplications;
	}


	public GraphRequest conversionGraph(String graph) {
		GraphRequest graphRequest = new GraphRequest();
		if (StrUtil.isNotBlank(graph)) {
			ObjectMapper objectMapper = new ObjectMapper();
			try {
				graphRequest = objectMapper.readValue(graph, GraphRequest.class);
			} catch (JsonProcessingException e) {
				throw new RuntimeException(e);
			}
		}
		return graphRequest;
	}

	public CartRequest conversionCart(String cart, String handle) {
		CartRequest cartRequests = new CartRequest();
		List carts;
		if (StrUtil.isNotBlank(cart)) {
			ObjectMapper objectMapper = new ObjectMapper();
			try {
				carts = objectMapper.readValue(cart, List.class);
				cartRequests.setFields(carts);
			} catch (JsonProcessingException e) {
				throw new RuntimeException(e);
			}
			cartRequests.setHandle(handle);
		}
		return cartRequests;
	}

}
