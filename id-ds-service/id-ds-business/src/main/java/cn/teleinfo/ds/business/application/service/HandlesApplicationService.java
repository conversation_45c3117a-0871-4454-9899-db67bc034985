package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.query.ListHandlesQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.aggregate.HandleDirectory;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleListView;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleDetailResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.util.List;

public interface HandlesApplicationService {

	/**
	 * 对象标识列表
	 */
	PageResponse<HandleListView> listHandles(ListHandlesQuery query);

	/**
	 * 查询对象标识
	 */
	HandleDomainEntity findByHandle(String handle);

	List<HandleDirectory> directory();

	Handle getHandleItemsByHandle(String handle);

	HandleDetailResponse handleDetail(Long id);

}
