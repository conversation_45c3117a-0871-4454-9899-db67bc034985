package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户表
 */
@Getter
@Setter
@Entity
@Table(name = "sys_user")
@SQLDelete(sql = "update sys_user set del_flag = 1 where id = ?")
@SQLRestriction("del_flag = 0")
public class UserEntity  {
	/**
	 *  用户ID
	 */
	@Id
	@Column(name = "user_id" )
	private Long userId;

	/**
	 *  用户名
	 */
	@Column(name = "username" )
	private String username;

	/**
	 *  密码
	 */
	@Column(name = "password" )
	private String password;

	/**
	 *  用户中心id
	 */
	@Column(name = "uc_open_id" )
	private String ucOpenId;

	/**
	 *  盐值
	 */
	@Column(name = "salt" )
	private String salt;

	/**
	 *  电话号码
	 */
	@Column(name = "phone" )
	private String phone;

	/**
	 *  头像
	 */
	@Column(name = "avatar" )
	private String avatar;

	/**
	 *  昵称
	 */
	@Column(name = "nickname" )
	private String nickname;

	/**
	 *  姓名
	 */
	@Column(name = "name" )
	private String name;

	/**
	 *  邮箱地址
	 */
	@Column(name = "email" )
	private String email;

	/**
	 *  所属部门ID
	 */
	@Column(name = "dept_id" )
	private Long deptId;

	/**
	 *  创建人
	 */
	@Column(name = "create_by" )
	private String createBy;

	/**
	 *  修改人
	 */
	@Column(name = "update_by" )
	private String updateBy;

	/**
	 *  创建时间
	 */
	@Column(name = "create_time" )
	private LocalDateTime createTime;

	/**
	 *  修改时间
	 */
	@Column(name = "update_time" )
	private LocalDateTime updateTime;

	/**
	 *  锁定标记，0未锁定，9已锁定
	 */
	@Column(name = "lock_flag" )
	private String lockFlag;

	/**
	 *  删除标记，0未删除，1已删除
	 */
	@Column(name = "del_flag" )
	private String delFlag;

	/**
	 *  微信登录openId
	 */
	@Column(name = "wx_openid" )
	private String wxOpenid;

	/**
	 *  小程序openId
	 */
	@Column(name = "mini_openid" )
	private String miniOpenid;

	/**
	 *  QQ openId
	 */
	@Column(name = "qq_openid" )
	private String qqOpenid;

	/**
	 *  码云标识
	 */
	@Column(name = "gitee_login" )
	private String giteeLogin;

	/**
	 *  开源中国标识
	 */
	@Column(name = "osc_id" )
	private String oscId;


}