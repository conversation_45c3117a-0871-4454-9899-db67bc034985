package cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShareSourceDTO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 平台类型 0 华为 1 阿里 2 自建
     */
    private Integer platformType;
    /**
     * 连接状态
     */
    private Integer connState;
    /**
     * 应用标识
     */
    private String appHandleCode;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 更新人ID（
     */
    private Long updateBy;
    /**
     * 创建时间
     */
	private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 更新人姓名
     */
    private String updateByName;
}
