package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.*;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsDetailView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShareChannelJpaRepository extends BaseRepository<ShareChannelEntity, Long> {
	@Query(nativeQuery = true,
			value = "SELECT " +
					"t1.id, " +
					"t1.share_channel_id as shareChannelId, " +
					"t3.data_channel_id as dataChannelId, " +
					"t3.data_channel_name as shareChannelName, " +
					"t1.object_handle as handle, " +
					"t3.data_type as dataType, " +
					//"t1.main_version as mainVersion, " +
					//"t1.minor_version as minorVersion, " +
					"IF(t1.channel_status = 1, CONCAT('V', t1.main_version, '.', t1.minor_version), '-') as version, " +
					"t1.update_time as updatedTime, " +
					"t1.detection_status as detectionStatus, " +
					"t1.channel_status as channelStatus " +
					"FROM t_share_channel t1 " +
					"LEFT JOIN t_share_channel t2 ON t1.share_channel_id = t2.share_channel_id " +
					"AND ((t2.channel_status = 1 AND t1.channel_status != 1) OR (t1.channel_status = t2.channel_status AND (t2.main_version > t1.main_version OR (t2.main_version = t1.main_version AND t2.minor_version > t1.minor_version)))) " +
					"AND t2.is_deleted = 0 " +
					"LEFT JOIN t_data_channel t3 ON t1.data_channel_id = t3.data_channel_id and t3.is_deleted = 0 " +
					"LEFT JOIN sys_user_app t4 ON t4.user_id = :userId " +
					"LEFT JOIN t_app_info t5 ON t5.id = t4.app_id and t5.is_deleted = 0 " +
					"WHERE t1.is_deleted = 0 " +
					"AND IF(:channelName != '' AND :channelName is not null, t3.data_channel_name like CONCAT('%',:channelName,'%'), 1=1 ) " +
					"AND IF(:handle != '' AND :handle is not null, t1.object_handle like CONCAT('%',:handle,'%'), 1=1 ) " +
					"AND IF(:detection_status != '' AND :detection_status is not null, t1.detection_status  = :detection_status, 1=1 ) " +
					"AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, t1.update_time BETWEEN :startTime AND :endTime , 1=1) " +
					"AND IF(:appHandle != '' AND :appHandle is not null, t1.app_handle_code like CONCAT('%',:appHandle,'%'), 1=1 ) " +
					"AND IF(:userId is not null, t5.handle_code = t1.app_handle_code, 1=1 ) " +
					"AND t2.id IS NULL " +
					"ORDER BY t1.update_time DESC ",
			countQuery = "SELECT count(1) " +
					"FROM t_share_channel t1 " +
					"LEFT JOIN t_share_channel t2 ON t1.share_channel_id = t2.share_channel_id " +
					"AND ((t2.channel_status = 1 AND t1.channel_status != 1) OR (t1.channel_status = t2.channel_status AND (t2.main_version > t1.main_version OR (t2.main_version = t1.main_version AND t2.minor_version > t1.minor_version)))) " +
					"AND t2.is_deleted = 0 " +
					"LEFT JOIN t_data_channel t3 ON t1.data_channel_id = t3.data_channel_id and t3.is_deleted = 0 " +
					"LEFT JOIN sys_user_app t4 ON t4.user_id = :userId " +
					"LEFT JOIN t_app_info t5 ON t5.id = t4.app_id and t5.is_deleted = 0 " +
					"WHERE t1.is_deleted = 0 " +
					"AND IF(:channelName != '' AND :channelName is not null, t3.data_channel_name like CONCAT('%',:channelName,'%'), 1=1 ) " +
					"AND IF(:handle != '' AND :handle is not null, t1.object_handle like CONCAT('%',:handle,'%'), 1=1 ) " +
					"AND IF(:detection_status != '' AND :detection_status is not null, t1.detection_status = :detection_status, 1=1 ) " +
					"AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, t1.update_time BETWEEN :startTime AND :endTime , 1=1) " +
					"AND IF(:appHandle != '' AND :appHandle is not null, t1.app_handle_code like CONCAT('%',:appHandle,'%'), 1=1 ) " +
					"AND IF(:userId is not null, t5.handle_code = t1.app_handle_code, 1=1 ) " +
					"AND t2.id IS NULL "
	)
	Page<ShareChannelsDTO> listShareChannels(@Param("channelName") String channelName,
											 @Param("handle") String handle,
											 @Param("detection_status") String detection_status,
											 @Param("startTime") LocalDateTime startTime,
											 @Param("endTime") LocalDateTime endTime,
											 @Param("appHandle") String appHandle,
											 @Param("userId") Long userId,
											 Pageable pageable);

	@Query(nativeQuery = true, value = "select distinct " +
			"a.id as id, " +
			"a.share_channel_id as shareChannelId, " +
			"d.data_channel_id as dataChannelId, " +
			"d.data_channel_name as shareChannelName, " +
			"a.object_handle as handle, " +
			"d.data_type as dataType, " +
			//"a.main_version as mainVersion, " +
			//"a.minor_version as minorVersion, " +
			"CONCAT('V', a.main_version, '.', a.minor_version) as version, " +
			"b.app_name as appName, " +
			"c.org_name as entName, " +
			"a.detection_status as detectionStatus, " +
			"a.default_sql as defaultSql, " +
			"a.custom_sql as customSql " +
			"from t_share_channel a " +
			"left join t_app_info b on a.app_handle_code = b.handle_code and b.is_deleted = 0 " +
			"left join t_ent_prefix c on b.ent_prefix = c.ent_prefix " +
			"left join t_data_channel d ON a.data_channel_id = d.data_channel_id and d.is_deleted = 0 " +
			"where a.id = :id " +
			"and a.is_deleted = 0 ")
	ShareChannelsDetailsDTO queryDetailsById(@Param("id") String id);

	@Query(nativeQuery = true, value = "select " +
			"default_sql as defaultSql, " +
			"custom_sql as customSql " +
			"from t_share_channel " +
			"where share_channel_id = :shareChannelId and main_version = :main_version " +
			"and minor_version = :minor_version " +
			"and is_deleted = 0 ")
	ShareChannelsVersionSqlView queryVersionSql(@Param("shareChannelId") String shareChannelId,
												@Param("main_version") String main_version,
												@Param("minor_version") String minor_version);

	@Query(nativeQuery = true, value = "select distinct " +
			"a.id as id, " +
			"a.share_channel_id as shareChannelId, " +
			"d.data_channel_id as dataChannelId, " +
			"d.data_channel_name as shareChannelName, " +
			"a.object_handle as handle, " +
			"d.data_type as dataType, " +
			"a.main_version as mainVersion, " +
			"a.minor_version as minorVersion, " +
			"b.app_name as appName, " +
			"c.org_name as entName, " +
			"a.detection_status as detectionStatus, " +
			"a.default_sql as defaultSql, " +
			"a.custom_sql as customSql " +
			"from t_share_channel a " +
			"left join t_app_info b on a.app_handle_code = b.handle_code and b.is_deleted = 0 " +
			"left join t_ent_prefix c on b.ent_prefix = c.ent_prefix " +
			"left join t_data_channel d ON a.data_channel_id = d.data_channel_id and d.is_deleted = 0 " +
			"where a.share_channel_id = :shareChannelId " +
			"and a.is_deleted = 0 ")
	List<ShareChannelsDetailsDTO> listShareChannelsVersion(@Param("shareChannelId") String shareChannelId);

	@Query(nativeQuery = true, value = "select distinct " +
			"a.id as id, " +
			"a.share_channel_id as shareChannelId, " +
			"d.data_channel_id as dataChannelId, " +
			"d.data_channel_name as shareChannelName, " +
			"a.object_handle as handle, " +
			"d.data_type as dataType, " +
			"a.main_version as mainVersion, " +
			"a.minor_version as minorVersion, " +
			"b.app_name as appName, " +
			"c.org_name as entName, " +
			"a.detection_status as detectionStatus, " +
			"a.default_sql as defaultSql, " +
			"a.custom_sql as customSql, " +
			"a.change_reason as changeReason, " +
			"a.channel_status as channelStatus, " +
			"f.channel_status as taskStatus, " +
			"e.name as editorUser, " +
			"a.update_time as updatedTime " +
			"from t_share_channel a " +
			"left join t_app_info b on a.app_handle_code = b.handle_code and b.is_deleted = 0 " +
			"left join t_ent_prefix c on b.ent_prefix = c.ent_prefix " +
			"left join t_data_channel d ON a.data_channel_id = d.data_channel_id and d.is_deleted = 0 " +
			"left join sys_user e ON a.update_by = e.user_id and del_flag = '0' " +
			"left join t_share_channel_applications f on a.share_channel_id = f.share_channel_id " +
			"and a.main_version = f.main_version and a.minor_version = f.minor_version and f.is_deleted = 0 " +
			"where a.share_channel_id = :shareChannelId " +
			"and a.is_deleted = 0 " +
			"order by a.main_version desc, a.minor_version desc ")
	List<ShareChannelsVersionDTO> queryShareChannelsVersionDTO(@Param("shareChannelId") String shareChannelId);

	@Query(nativeQuery = true,
			value = "SELECT " +
					"t1.id, " +
					"t1.share_channel_id as shareChannelId, " +
					"t3.data_channel_id as dataChannelId, " +
					"t2.id as applicationId, " +
					"t3.data_channel_name as shareChannelName, " +
					"t1.object_handle as handle, " +
					"t3.data_type as dataType, " +
					//"t1.main_version as mainVersion, " +
					//"t1.minor_version as minorVersion, " +
					"CONCAT('V', t1.main_version, '.', t1.minor_version) as version, " +
					"t2.created_time as updatedTime, " +
					"t2.channel_status as channelStatus " +
					"FROM t_share_channel t1 " +
					"INNER JOIN t_share_channel_applications t2 ON t1.share_channel_id = t2.share_channel_id " +
					"AND t1.main_version = t2.main_version AND t1.minor_version = t2.minor_version " +
					"LEFT JOIN t_data_channel t3 ON t1.data_channel_id = t3.data_channel_id " +
					"WHERE t1.is_deleted = 0 and t2.is_deleted = 0 " +
					"AND IF(:channelName != '' AND :channelName is not null, t3.data_channel_name like CONCAT('%',:channelName,'%'), 1=1 ) " +
					"AND IF(:handle != '' AND :handle is not null, t1.object_handle like CONCAT('%',:handle,'%'), 1=1 ) " +
					"AND IF(:channelStatus != '' AND :channelStatus is not null, t2.channel_status = :channelStatus, 1=1 ) " +
					"AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, t2.created_time BETWEEN :startTime AND :endTime , 1=1) " +
					"AND IF(:appHandle != '' AND :appHandle is not null, t1.app_handle_code like CONCAT('%',:appHandle,'%'), 1=1 ) " +
					"ORDER BY t2.created_time DESC ",
			countQuery = "SELECT count(1) " +
					"FROM t_share_channel t1 " +
					"INNER JOIN t_share_channel_applications t2 ON t1.share_channel_id = t2.share_channel_id " +
					"AND t1.main_version = t2.main_version AND t1.minor_version = t2.minor_version " +
					"LEFT JOIN t_data_channel t3 ON t1.data_channel_id = t3.data_channel_id " +
					"WHERE t1.is_deleted = 0 and t2.is_deleted = 0 " +
					"AND IF(:channelName != '' AND :channelName is not null, t3.data_channel_name like CONCAT('%',:channelName,'%'), 1=1 ) " +
					"AND IF(:handle != '' AND :handle is not null, t1.object_handle like CONCAT('%',:handle,'%'), 1=1 ) " +
					"AND IF(:channelStatus != '' AND :channelStatus is not null, t2.channel_status = :channelStatus, 1=1 ) " +
					"AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, t2.created_time BETWEEN :startTime AND :endTime , 1=1) " +
					"AND IF(:appHandle != '' AND :appHandle is not null, t1.app_handle_code like CONCAT('%',:appHandle,'%'), 1=1 ) "
	)
	Page<ShareChannelsApplicationsView> listShareChannelsApplications(@Param("channelName") String channelName,
																	  @Param("handle") String handle,
																	  @Param("channelStatus") String channelStatus,
																	  @Param("startTime") LocalDateTime startTime,
																	  @Param("endTime") LocalDateTime endTime,
																	  @Param("appHandle") String appHandle,
																	  Pageable pageable);

	@Query(nativeQuery = true, value = "select distinct " +
			"a.id as id, " +
			"a.share_channel_id as shareChannelId, " +
			"d.data_channel_id as dataChannelId, " +
			"d.data_channel_name as shareChannelName, " +
			"a.change_reason as changeReason, " +
			"a.object_handle as handle, " +
			"d.data_type as dataType, " +
			"a.main_version as mainVersion, " +
			"a.minor_version as minorVersion, " +
			"b.app_name as appName, " +
			"c.org_name as entName, " +
			"a.detection_status as detectionStatus, " +
			"a.default_sql as defaultSql, " +
			"a.custom_sql as customSql, " +
			"f.name as applyUser, " +
			"e.channel_status as channelStatus, " +
			"e.created_time as updatedTime " +
			"from t_share_channel a " +
			"left join t_app_info b on a.app_handle_code = b.handle_code and b.is_deleted = 0 " +
			"left join t_ent_prefix c on b.ent_prefix = c.ent_prefix " +
			"left join t_data_channel d on a.data_channel_id = d.data_channel_id and d.is_deleted = 0 " +
			"inner join t_share_channel_applications e on a.share_channel_id = e.share_channel_id " +
			"and a.main_version = e.main_version and a.minor_version = e.minor_version " +
			"left join sys_user f on e.apply_user_id = f.user_id and f.del_flag = '0' " +
			"where e.id = :applicationId and e.is_deleted = 0 " +
			"and a.is_deleted = 0 ")
	ShareChannelsApplicationsDetailView queryShareChannelsApplicationDetails(@Param("applicationId") String applicationId);

	List<ShareChannelEntity> findByDataChannelId(Long dataChannelId);

	ShareChannelEntity findByShareChannelIdAndChannelStatus(Long shareChannelId, Integer i);
	ShareChannelEntity findByDataChannelIdAndChannelStatus(Long dataChannelId, Integer i);

	@Query(nativeQuery = true, value = "select * from t_share_channel  " +
			"where share_channel_id = :shareChannelId and is_deleted = 0 " +
			"order by main_version desc ,minor_version desc limit  1")
	ShareChannelEntity findMaxVersionByShareChannelId(@Param("shareChannelId") Long shareChannelId);

	@Query(nativeQuery = true, value = "select " +
			"CONCAT('V', main_version, '.', minor_version) as version " +
			"from t_share_channel " +
			"where share_channel_id = :shareChannelId and is_deleted = 0 " +
			"order by main_version desc, minor_version desc ")
	List<String> queryChannelVersions(@Param("shareChannelId") String shareChannelId);

	ShareChannelEntity findByShareChannelIdAndMainVersionAndMinorVersion(Long shareChannelId, Integer mainVersion, Integer minorVersion);

	@Query(nativeQuery = true, value = """
			SELECT *
			                       FROM t_share_channel t1
			                       WHERE t1.channel_status = 1
			                         AND t1.is_deleted = 0
			                         AND NOT EXISTS (
			                           SELECT 1
			                           FROM t_share_channel t2
			                           WHERE t2.share_channel_id = t1.share_channel_id
			                             AND t2.channel_status = 1
			                             AND t2.is_deleted = 0
			                             AND (
			                               t2.main_version > t1.main_version
			                                   OR (t2.main_version = t1.main_version AND t2.minor_version > t1.minor_version)
			                               )
			                       )
			                       UNION ALL
			                       SELECT *
			                       FROM t_share_channel t1
			                       WHERE t1.is_deleted = 0
			                         AND t1.channel_status <> 1
			                         AND NOT EXISTS (
			                           SELECT 1
			                           FROM t_share_channel t2
			                           WHERE t2.share_channel_id = t1.share_channel_id
			                             AND t2.is_deleted = 0
			                             AND t2.channel_status <> 1
			                             AND (
			                               t2.main_version > t1.main_version
			                                   OR (t2.main_version = t1.main_version AND t2.minor_version > t1.minor_version)
			                               )
			                       )
			                         AND NOT EXISTS (
			                           SELECT 1
			                           FROM t_share_channel t3
			                           WHERE t3.share_channel_id = t1.share_channel_id
			                             AND t3.channel_status = 1
			                             AND t3.is_deleted = 0
			                       );
			""")
	List<ShareChannelEntity> findDetectChannel();

	List<ShareChannelEntity> findAllByShareChannelIdAndDetectionStatus(Long shareChannelId, Integer detectionStatus);

	List<ShareChannelEntity> findAllByShareChannelId(Long shareChannelId);
}