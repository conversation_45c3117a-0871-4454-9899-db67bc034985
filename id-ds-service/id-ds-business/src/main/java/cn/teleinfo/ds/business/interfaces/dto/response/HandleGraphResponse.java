package cn.teleinfo.ds.business.interfaces.dto.response;


import cn.teleinfo.ds.business.domain.model.valueobject.Edge;
import cn.teleinfo.ds.business.domain.model.valueobject.Node;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class HandleGraphResponse {
	/**
	 * 连线数组
	 */
	private List<Edge> edges;
	/**
	 * 节点数组
	 */
	private List<Node> nodes;
	/**
	 * 根标识
	 */
	private String rootHandle;
}
