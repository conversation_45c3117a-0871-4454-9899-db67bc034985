package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceItems;
import cn.teleinfo.ds.business.domain.model.valueobject.AppHandleCode;
import lombok.Data;

/**
 * 目标源聚合根
 */
@Data
public class TargetSource {
	/**
	 * 目标源
	 */
	private TargetSourceDomainEntity targetSourceDomainEntity;

	/**
	 * app handle code
	 */
	private AppHandleCode appHandleCode;

	private AppInfoDomainEntity appInfo;

	public TargetSource() {
	}

	public TargetSource(TargetSourceDomainEntity targetSourceDomainEntity, AppHandleCode appHandleCode) {
		this.targetSourceDomainEntity = targetSourceDomainEntity;
		this.targetSourceDomainEntity.setTargetSourceItems(JSONUtil.toBean(targetSourceDomainEntity.getItems(), TargetSourceItems.class));
		this.appHandleCode = appHandleCode;
	}

	public TargetSource(TargetSourceDomainEntity targetSourceDomainEntity) {
		this.targetSourceDomainEntity = targetSourceDomainEntity;
		this.targetSourceDomainEntity.setTargetSourceItems(JSONUtil.toBean(targetSourceDomainEntity.getItems(), TargetSourceItems.class));
	}

	public void genLinkName(){
		String linkName = targetSourceDomainEntity.getTargetSourceItems().getConnType() + "_" + targetSourceDomainEntity.getId();
		targetSourceDomainEntity.getTargetSourceItems().setLinkName(linkName);
		targetSourceDomainEntity.setItems(JSONUtil.toJsonStr(targetSourceDomainEntity.getTargetSourceItems()));
	}
}
