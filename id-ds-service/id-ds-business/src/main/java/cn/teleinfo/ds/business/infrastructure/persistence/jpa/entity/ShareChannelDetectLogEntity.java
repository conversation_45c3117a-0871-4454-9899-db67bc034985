package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.Instant;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "t_share_channel_detect_log")
@SQLDelete(sql = "update t_share_channel_detect_log set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class ShareChannelDetectLogEntity {
	@Id
	@Column(name = "id")
	private Long id;

	@Column(name = "share_channel_id")
	private Long shareChannelId;

	@Column(name = "connect_test")
	private Integer connectTest;

	@Column(name = "grammar_check")
	private Integer grammarCheck;

	@Column(name = "exec_test")
	private Integer execTest;

	@Column(name = "log_path")
	private String logPath;

	@Column(name = "main_version")
	private Integer mainVersion;

	@Column(name = "minor_version")
	private Integer minorVersion;

	@Column(name = "create_time")
	@CreatedDate
	private LocalDateTime createTime;

	@Column(name = "update_time")
	@LastModifiedDate
	private LocalDateTime updateTime;

	@Column(name = "is_deleted")
	private Integer isDeleted = 0;

}