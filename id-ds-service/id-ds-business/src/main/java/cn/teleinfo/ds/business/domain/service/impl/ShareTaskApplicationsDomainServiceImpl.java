package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.*;
import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.model.valueobject.*;
import cn.teleinfo.ds.business.domain.repository.*;
import cn.teleinfo.ds.business.domain.service.HandleGraphDomainService;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsDetailsService;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsDomainService;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsService;
import cn.teleinfo.ds.business.domain.util.CronParseUtil;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsXqDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.*;
import cn.teleinfo.ds.business.interfaces.dto.request.CartRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.GraphRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleItemDetailResponse;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.cronutils.descriptor.CronDescriptor;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.parser.CronParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import cn.teleinfo.ds.common.core.constant.UserConstants;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class ShareTaskApplicationsDomainServiceImpl implements ShareTaskApplicationsDomainService {

	private final ShareTaskApplicationsRepository repository;

	private final ShareTaskApplicationsService service;

	private final ShareTaskApplicationsDetailsService detailsService;

	private final ShareTaskAuthRepository authRepository;

	private final ShareTaskApplicationsDetailRepository detailRepository;

	private final HandleGraphDomainService handleGraphDomainService;

	private final RoleService roleService;

	private final AppInfoRepository appInfoRepository;

	//标识属性信息
	private final HandlesRepository handlesRepository;
	private final HandleItemRepository handleItemRepository;

	/**
	 * 创建任务申请
	 *
	 * @param command 任务申请信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void createShareTaskApplications(ShareTaskApplicationsCommand command) {
		ShareTaskApplications shareTaskApplications = service.createShareTaskApplications(command);
		ShareTaskApplicationDomain valueObject = new ShareTaskApplicationDomain();
		BeanUtils.copyProperties(shareTaskApplications, valueObject);
		valueObject.setTaskNo(UUID.randomUUID().toString().toUpperCase());
		ShareTaskApplicationsEntity applications = repository.createShareTaskApplicationsEntity(valueObject);

		if (CollectionUtil.isNotEmpty(command.getCart())) {
			ArrayList<ShareTaskApplicationsDetailsValueObject> detailsEntities = new ArrayList<>();
			for (CartRequest cartRequest : command.getCart()) {
				Cart cart = detailsService.setCartObject(cartRequest);
				ShareTaskApplicationsDetailsValueObject detailsEntity = new ShareTaskApplicationsDetailsValueObject();
				BeanUtils.copyProperties(shareTaskApplications, detailsEntity);
				if (StrUtil.isNotBlank(detailsEntity.getEntPrefix())) {
					detailsEntity.setProvincePrefix(detailsEntity.getEntPrefix().substring(0, detailsEntity.getEntPrefix().lastIndexOf('.')));
				}
				detailsEntity.setHandle(cart.getHandle());
				detailsEntity.setFields(cart.getFields());
				detailsEntity.setShareTaskApplicationsId(applications.getId());
				detailsEntities.add(detailsEntity);
			}
			repository.createShareTaskApplicationsDetails(detailsEntities);
		}
	}

	/**
	 * 修改任务申请
	 *
	 * @param applicationId 申请id
	 * @param command       任务申请信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateShareTaskApplications(Long applicationId, ShareTaskApplicationsCommand command) {
		ShareTaskApplicationsDomainEntity domainEntity = repository.findById(applicationId);
		command.setUpdate(true);
		ShareTaskApplications shareTaskApplications = service.createShareTaskApplications(command);
		ShareTaskApplicationDomain valueObject = setShareTaskApplicationDomain(applicationId, shareTaskApplications, domainEntity);
		// 修改申请信息
		repository.createShareTaskApplicationsEntity(valueObject);
		// 删除申请信息
		repository.deleteShareTaskApplicationsDetailsEntityS(command);
		// 新增申请详情信息
		if (CollectionUtil.isNotEmpty(command.getCart())) {
			ArrayList<ShareTaskApplicationsDetailsValueObject> detailsEntities = new ArrayList<>();
			for (CartRequest cartRequest : command.getCart()) {
				Cart cart = detailsService.setCartObject(cartRequest);
				ShareTaskApplicationsDetailsValueObject detailsEntity = new ShareTaskApplicationsDetailsValueObject();
				BeanUtils.copyProperties(shareTaskApplications, detailsEntity);
				detailsEntity.setHandle(cart.getHandle());
				detailsEntity.setFields(cart.getFields());
				detailsEntity.setShareTaskApplicationsId(applicationId);
				detailsEntities.add(detailsEntity);
			}
			repository.createShareTaskApplicationsDetails(detailsEntities);
		}
	}

	@NotNull
	private static ShareTaskApplicationDomain setShareTaskApplicationDomain(Long applicationId, ShareTaskApplications shareTaskApplications, ShareTaskApplicationsDomainEntity domainEntity) {
		ShareTaskApplicationDomain valueObject = new ShareTaskApplicationDomain();
		BeanUtils.copyProperties(shareTaskApplications, valueObject);
		valueObject.setId(applicationId);
		valueObject.setCreateTime(domainEntity.getCreateTime());
		valueObject.setTaskNo(domainEntity.getTaskNo());
		valueObject.setCreateBy(domainEntity.getCreateBy());
		return valueObject;
	}

	/**
	 * 查询任务列表
	 *
	 * @return 分页查询结果
	 */
	@Override
	public PageResponse<ShareTaskApplicationsView> listShareTaskApplications(ShareTaskApplicationsQuery query) {

		Long id = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(id);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				//获取该用户关联的所有应用
				List<String> handleCodes = appInfoRepository.findHandleCodeByUserId(id);
				if (!handleCodes.isEmpty()) {
					query.setUserHandleCode(handleCodes);
				}
			}
		}
		return repository.listShareTaskApplications(query, query.getCurrent(), query.getSize());
	}

	@Override
	public ShareTaskApplicationDetails getShareTaskApplicationDetail(Long applicationId) {
		// 获取申请信息
		ShareTaskApplicationDetails details = new ShareTaskApplicationDetails();
		ShareTaskApplicationsDTOView dto = repository.findShareTaskApplicationsById(applicationId);
		if (ObjectUtil.isEmpty(dto)) {
			return details;
		}
		String graph = dto.getGraph();
		ShareTaskApplications applications = new ShareTaskApplications();
		GraphRequest graph1 = applications.conversionGraph(graph);
		ShareTaskApplicationValue applicationValue = new ShareTaskApplicationValue();
		BeanUtils.copyProperties(dto, applicationValue);

		//翻译cron表达式
		String cron = dto.getCronExpression();
		applicationValue.setCronExpression(cron);
		applicationValue.setCronDetail(CronParseUtil.parseToHumanReadable(cron));

		//applicationValue.setCronDetail();
		//获取数据库名称
		String items = dto.getItems();
		if (StringUtils.isNotBlank(items)) {
			TargetSourceItems targetSourceItems = JSONUtil.toBean(items, TargetSourceItems.class);
			applicationValue.setDatabaseName(targetSourceItems.getDatabaseName());
		}

		details.setBasicInfo(applicationValue);
		// 获取申请信息
		List<ShareTaskAuthView> auths = authRepository.findShareTaskAuthsByApplicationId(dto.getId());
		if (CollectionUtil.isNotEmpty(auths)) {
			ArrayList<ShareTaskAuthApplicationValue> values = new ArrayList<>();
			for (ShareTaskAuthView auth : auths) {
				ShareTaskAuthApplicationValue value = new ShareTaskAuthApplicationValue();
				value.setId(auth.getId());
				value.setUpdatedTime(auth.getUpdateTime());
				value.setAuditUser(auth.getAuditUserName());
				value.setAuthStatus(auth.getAuthStatus());
				value.setAuditRemark(auth.getAuditRemark());
				values.add(value);
			}
			details.setAuthorizeRecords(values);
		}

		List<ShareTaskApplicationsDetailDTO> detailDTOS = detailRepository.findShareTaskApplicationsDetailsByTaskId(applicationId);
		if (CollectionUtil.isEmpty(detailDTOS)) {
			return details;
		}
		// 获取共享数据
		ArrayList<ShareTaskApplicationDetailValue> detailValues = new ArrayList<>();
		ArrayList<CartRequest> list = new ArrayList<>();
		detailDTOS.forEach(detail -> {
			ShareTaskApplicationDetailValue value = new ShareTaskApplicationDetailValue();
			BeanUtils.copyProperties(detail, value);
			detailValues.add(value);
			CartRequest cartRequests = applications.conversionCart(detail.getFields(), detail.getHandle());
			list.add(cartRequests);
		});
		ApplicationInfo applicationInfo = new ApplicationInfo();
		applicationInfo.setCart(list);
		applicationInfo.setGraph(graph1);
		details.setApplicationInfo(applicationInfo);
		details.setShareData(detailValues);
		return details;
	}



	/**
	 * 查询一个共享任务申请的详情
	 *
	 * @param shareTaskApplicationsId 共享任务申请id
	 * @return 共享任务申请的详情
	 */
	@Override
	public ShareTaskApplicationsDomainEntity findShareTaskApplicationsDomainEntity(Long shareTaskApplicationsId) {
		ShareTaskApplicationsDomainEntity entity = repository.findShareTaskApplicationsDomainEntity(shareTaskApplicationsId);
		if (entity == null) {
			throw new CheckedException("共享任务申请不存在或已删除！");
		}
		List<ShareTaskApplicationsDetailsDomainEntity> details = detailRepository.findShareTaskApplicationsDetailsDomainEntities(shareTaskApplicationsId);
		entity.setDetailsDomainEntities(details);
		return entity;
	}

	/**
	 * 更新共享任务申请状态
	 *
	 * @param id         id
	 * @param authStatus 授权状态
	 */
	@Override
	public void updateApplicationsStatus(Long id, AuthStatus authStatus) {
		repository.updateStatusById(id, authStatus.code());
	}

	@Override
	public HandleApplicationResponse getHandleItemsByHandle(HandleItemQuery query) {
		ShareTaskApplicationsDetailsDomainEntity detailsDomainEntity = detailRepository.findShareTaskApplicationsDetailsById(query.getShareTaskApplicationsDetailId());

		ShareHandleView shareHandleByHandle = repository.findShareHandleByHandle(query.getShareTaskApplicationsDetailId());
		Handle handle = handleGraphDomainService.handleItems(detailsDomainEntity.getHandle());
		if (ObjectUtil.isEmpty(handle)) {
			return null;
		}
		List<HandleItemDomainEntity> modelEntities = handle.getHandleItemModelEntities();
		HandleApplicationResponse applicationResponse  = new HandleApplicationResponse();
		if (ObjectUtil.isEmpty(detailsDomainEntity)) {
			return null;
		}
		String fields = detailsDomainEntity.getFields();
		if (StrUtil.isNotBlank(fields) && StrUtil.equals(null, fields)) {
			ObjectMapper mapper = new ObjectMapper();
			String[] array;
			try {
				array = mapper.readValue(fields, String[].class);
			} catch (JsonProcessingException e) {
				throw new RuntimeException(e);
			}
			List<String> list = Arrays.asList(array);
			applicationResponse = filterEntitiesByFields(modelEntities, list);
		}
		applicationResponse.setHandleName(shareHandleByHandle.getHandleName());
		applicationResponse.setAppName(shareHandleByHandle.getAppName());
		applicationResponse.setProvinceName(shareHandleByHandle.getProvinceName());
		applicationResponse.setEntName(shareHandleByHandle.getEntName());
		applicationResponse.setHandle(detailsDomainEntity.getHandle());
		return applicationResponse;
	}

	@Override
	public void deleteShareTaskApplications(Long applicationId) {
		repository.deleteShareTaskApplications(applicationId);
	}

	@Override
	public ShareTaskApplicationsDetailsDomainEntity findShareTaskApplicationsDetails(Long id) {
		return detailsService.findById(id);
	}

	/**
	 * 根据字段过滤实体
	 *
	 * @param modelEntities 实体列表
	 * @param filterList    需要过滤的字段列表
	 * @return 过滤后的实体列表
	 */
	public HandleApplicationResponse filterEntitiesByFields(
			List<HandleItemDomainEntity> modelEntities,
			List<String> filterList) {
		List<HandleItemDomainEntity> entityList = modelEntities.stream()
				.filter(entity -> filterList.contains(entity.getField()))
				.toList();
		if (CollectionUtil.isEmpty(entityList)) {
			return null;
		}
		// 基础属性
		List<HandleItemDomainEntity> isFoundation = entityList.stream().filter(item -> item.getFieldSourceType() == 0).toList();
		// 扩展属性
		List<HandleItemDomainEntity> isExtend = entityList.stream().filter(item -> item.getFieldSourceType() == 1).toList();
		List<HandleItemDetailResponse> items = isFoundation.stream()
				.map(entity -> {
					HandleItemDetailResponse dto = new HandleItemDetailResponse();
					BeanUtils.copyProperties(entity, dto);
					return dto;
				}).toList();
		List<HandleItemDetailResponse> extendItems = isExtend.stream()
				.map(entity -> {
					HandleItemDetailResponse dto = new HandleItemDetailResponse();
					BeanUtils.copyProperties(entity, dto);
					return dto;
				}).toList();
		HandleApplicationResponse response = new HandleApplicationResponse();
		response.setItems(items);
		response.setExtendItems(extendItems);
		return response;
	}

	@Override
	public ShareDataDetails getHandleDetails(Long dataId) {
		ShareTaskApplicationsDetailsDomainEntity detailsDomainEntity = detailRepository
				.findShareTaskApplicationsDetailsById(dataId);
		if (detailsDomainEntity == null) {
			return new ShareDataDetails();
		}
		String fields = detailsDomainEntity.getFields();
		ObjectMapper mapper = new ObjectMapper();
		String[] array;
		try {
			array = mapper.readValue(fields, String[].class);
		}
		catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
		List<String> list = Arrays.asList(array);

		SharedTaskView sharedTaskView = handlesRepository.getHandleDetails(detailsDomainEntity.getHandle());
		if (sharedTaskView == null) {
			return new ShareDataDetails();
		}
		// 过滤并按属性来源类型分组
		Map<String, List<HandleItemView>> groupedByFieldSourceType = handleItemRepository
				.findAllByHandle(detailsDomainEntity.getHandle())
				.stream()
				.filter(item -> list.contains(item.getField()))
				.collect(Collectors.groupingBy(HandleItemView::getFieldSourceType));

		return new ShareDataDetails(sharedTaskView, groupedByFieldSourceType.get("0"),
				groupedByFieldSourceType.get("1"));
	}



}
