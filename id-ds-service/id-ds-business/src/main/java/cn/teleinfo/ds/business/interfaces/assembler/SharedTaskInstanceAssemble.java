package cn.teleinfo.ds.business.interfaces.assembler;

import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.interfaces.dto.request.ListSharedTaskInstancesRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
public class SharedTaskInstanceAssemble {

	public SharedTaskInstanceListQuery toListQuery(ListSharedTaskInstancesRequest request) {
		SharedTaskInstanceListQuery query = new SharedTaskInstanceListQuery();
		BeanUtils.copyProperties(request, query);
		return query;
	}
}
