package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDetailDomainEntity;
import cn.teleinfo.ds.business.domain.repository.SharedTaskDetailsRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskShareDataValueDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskDetailEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.SharedTaskDetailJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class SharedTaskDetailsRepositoryImpl implements SharedTaskDetailsRepository {
	private final SharedTaskDetailJpaRepository sharedTaskDetailJpaRepository;

	@Override
	public void save(SharedTaskDetailDomainEntity sharedTaskDetailDomainEntity) {
		var entity = BeanUtil.copyProperties(sharedTaskDetailDomainEntity, SharedTaskDetailEntity.class);
		sharedTaskDetailJpaRepository.save(entity);
	}

	@Override
	public void deleteBySharedTaskId(Long id) {
		sharedTaskDetailJpaRepository.deleteById(id);
	}

	@Override
	public SharedTaskView findSharedTaskViewById(Long id) {
		return sharedTaskDetailJpaRepository.findSharedTaskViewById(id);
	}

	@Override
	public List<SharedTaskShareDataValueDTO> getSharedTaskShareDataValueDTO(Long id) {
		return sharedTaskDetailJpaRepository.getSharedTaskShareDataValueDTO(id);
	}
}
