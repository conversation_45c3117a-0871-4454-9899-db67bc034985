package cn.teleinfo.ds.business.infrastructure.persistence.dto.sync;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SyncUserDTO {


	/**
	 * 主键ID
	 */
	private Long id;
	/**
	 * 地址
	 */
	private String address;
	/**
	 * 应用前缀
	 */
	private String appHandleCode;
	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;
	/**
	 * 邮箱
	 */
	private String email;
	/**
	 * 企业租户
	 */
	private String entPrefix;

	/**
	 * 是否删除;是否删除 0：未删除，1：已删除；默认为0
	 */
	private Long isDeleted;
	/**
	 * 昵称
	 */
	private String nickName;
	/**
	 * 密码
	 */
	private String password;
	/**
	 * 电话
	 */
	private String phone;
	/**
	 * 省级租户
	 */
	private String provincePrefix;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 1-企业应用用户2-省级管理员
	 */
	private Long roleCode;
	/**
	 * 用户原始id
	 */
	private long sourceId;
	/**
	 * 1-省；2-企业
	 */
	private Long sourceType;
	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;
	/**
	 * 用户名
	 */
	private String username;
	/**
	 * 中台id
	 */
	private List<String> socialIds;

}
