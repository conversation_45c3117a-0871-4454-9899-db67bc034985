package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.aggregate.ShareDataDetails;
import cn.teleinfo.ds.business.domain.repository.HandleItemRepository;
import cn.teleinfo.ds.business.domain.repository.SharedTaskDetailsRepository;
import cn.teleinfo.ds.business.domain.service.SharedTaskDetailDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleItemView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskView;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class SharedTaskDetailDomainServiceImpl implements SharedTaskDetailDomainService {

	private final SharedTaskDetailsRepository sharedTaskDetailsRepository;

	private final HandleItemRepository handleItemRepository;

	@Override
	public void deleteBySharedTaskId(Long id) {
		sharedTaskDetailsRepository.deleteBySharedTaskId(id);
	}

	@Override
	public ShareDataDetails getSharedDataDetails(Long dataId) {
		SharedTaskView sharedTaskView = sharedTaskDetailsRepository.findSharedTaskViewById(dataId);
		if (sharedTaskView == null) {
			return new ShareDataDetails();
		}
		String fields = sharedTaskView.getFields();
		ObjectMapper mapper = new ObjectMapper();
		String[] array;
		try {
			array = mapper.readValue(fields, String[].class);
		}
		catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
		List<String> list = Arrays.asList(array);

		// 过滤并按属性来源类型分组
		Map<String, List<HandleItemView>> groupedByFieldSourceType = handleItemRepository
			.findAllByHandle(sharedTaskView.getHandle())
			.stream()
			.filter(item -> list.contains(item.getField()))
				.collect(Collectors.groupingBy(HandleItemView::getFieldSourceType));

		return new ShareDataDetails(sharedTaskView, groupedByFieldSourceType.get("0"),
				groupedByFieldSourceType.get("1"));
	}

}
