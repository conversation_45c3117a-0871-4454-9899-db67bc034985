package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ShareTaskApplicationResponse {

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务类型(1：手动任务 2：定时任务)
	 */
	private Integer taskType;

	/**
	 * 任务状态( 1:申请中 2:已驳回 3:已授权)
	 */
	private Integer applicationsStatus;

	/**
	 * 目标源
	 */
	private Integer targetSourceId;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 应用身份编码
	 */
	private String appHandleCode;

	/**
	 * 操作时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 操作人
	 */
	private String updateByName;

	/**
	 * 数据库
	 */
	private String databaseName;

	/**
	 * 定时周期
	 */
	private String cronDetail;

	/**
	 * 企业
	 */
	private String entName;

	/**
	 * 应用
	 */
	private String appName;

	/**
	 * 目标源
	 */
	private String targetSource;

	/**
	 * 审核备注
	 */
	private String auditRemark;

	/**
	 * 申请人
	 */
	private String committedUser;

	private String cronExpression;
}
