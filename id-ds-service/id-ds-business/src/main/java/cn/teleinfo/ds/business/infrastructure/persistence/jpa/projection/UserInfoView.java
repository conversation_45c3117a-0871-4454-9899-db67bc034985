package cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection;

import java.time.LocalDateTime;
import java.util.List;

public interface UserInfoView {
	Long getUserId();

	String getUsername();

	String getPassword();

	String getUcOpenId();

	String getSalt();

	String getPhone();

	String getAvatar();

	String getNickname();

	String getName();

	String getEmail();

	Long getDeptId();

	String getCreateBy();

	String getUpdateBy();

	LocalDateTime getCreateTime();

	LocalDateTime getUpdateTime();

	String getLockFlag();

	String getDelFlag();

	String getWxOpenid();

	String getMiniOpenid();

	String getQqOpenid();

	String getGiteeLogin();

	String getOscId();

	Long getAppId();
}
