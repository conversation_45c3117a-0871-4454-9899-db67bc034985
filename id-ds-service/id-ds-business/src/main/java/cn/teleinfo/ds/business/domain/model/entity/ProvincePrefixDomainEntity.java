package cn.teleinfo.ds.business.domain.model.entity;


import lombok.Data;

import java.util.Date;

@Data
public class ProvincePrefixDomainEntity {

	/**
	 *  主键ID
	 */
	private Long id;

	/**
	 *  创建时间
	 */
	private Date createdTime;

	/**
	 *  更新时间
	 */
	private Date updatedTime;

	/**
	 *  省级前缀
	 */
	private String provincePrefix;

	/**
	 *  组织机构名称
	 */
	private String orgName;

	/**
	 *  组织机构编码
	 */
	private String orgCode;

	/**
	 *  上级单位名称
	 */
	private String parentOrgName;

	/**
	 *  所属省
	 */
	private String orgAddrProvince;

	/**
	 *  所属市
	 */
	private String orgAddrCity;

	/**
	 *  所属区
	 */
	private String orgAddrDistrict;

	/**
	 *  地址
	 */
	private String orgAddress;

}
