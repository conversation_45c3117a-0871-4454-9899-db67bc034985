package cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto;

import lombok.Data;

@Data
public class SharedTaskInstanceListDTO {

	private Long id;

	private Long sharedTaskId;
	/**
	 * 任务执行编号
	 */
	private String taskInstanceNo;

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务类型 1-手动任务，2-定时任务
	 */
	private Integer taskType;

	/**
	 * 执行类型 1-正式执行，2-测试执行
	 */
	private Integer executionType;

	/**
	 * 任务状态 1-运行中，2-成功，3-失败，0-未运行
	 */
	private Integer runStatus;

	/**
	 * 运行时间
	 */
	private String runTime;
}
