package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDetailsDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsDetailsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShareTaskApplicationsDetailsJpaRepository extends BaseRepository<ShareTaskApplicationsDetailsEntity, Long> {


	List<ShareTaskApplicationsDetailsEntity> findAllByShareTaskApplicationsId(Long shareTaskApplicationsId);

	@Query(nativeQuery = true,
			value = """
						select
							s.id,
							t.name AS handleName,
							s.handle AS handle,
							pp.org_name AS provinceName,
							ep.org_name AS entName,
							ai.app_name AS appName,
							s.fields,
							s.province_prefix as provincePrefix,
							s.ent_prefix as entPrefix,
							s.app_handle_code as appHandleCode
						FROM t_share_task_applications_details s
						LEFT JOIN t_handle t on  s.handle = t.handle and t.is_deleted = 0
						left join t_app_info ai on ai.handle_code = s.app_handle_code
						left join t_ent_prefix ep on ep.ent_prefix = s.ent_prefix\s
						left join t_province_prefix pp on pp.province_prefix = ep.province_prefix\s
						WHERE s.share_task_applications_id = :taskId
						  AND s.is_deleted = 0
					"""
	)
	List<ShareTaskApplicationsDetailDTO> findShareTaskApplicationsDetailsByTaskId(Long taskId);


	List<ShareTaskApplicationsDetailsDomainEntity> findByShareTaskApplicationsId(Long shareTaskApplicationsId);

	ShareTaskApplicationsDetailsEntity findShareTaskApplicationsDetailsById(Long id);

} 