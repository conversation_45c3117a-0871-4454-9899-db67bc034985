package cn.teleinfo.ds.business.domain.model.entity;

//
//BOOTING：启动中。
//
//FAILURE_ON_SUBMIT：提交失败。
//
//RUNNING：运行中。
//
//SUCCEEDED：成功。
//
//FAILED：失败。
//
//UNKNOWN：未知。
//
//NEVER_EXECUTED：未被执行
public enum HcsCmdRunStatus {
	BOOTING("BOOTING", "启动中"),
	FAILURE_ON_SUBMIT("FAILURE_ON_SUBMIT", "提交失败"),
	RUNNING("RUNNING", "运行中"),
	SUCCEEDED("SUCCEEDED", "成功"),
	FAILED("FAILED", "失败"),
	UNKNOWN("UNKNOWN", "未知"),
	;

	private final String code;
	private final String message;

	HcsCmdRunStatus(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String code() {
		return code;
	}

	public String message() {
		return message;
	}
}
