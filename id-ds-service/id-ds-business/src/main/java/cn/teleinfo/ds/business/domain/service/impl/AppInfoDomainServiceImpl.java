package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.EntPrefixDomainEntity;
import cn.teleinfo.ds.business.domain.repository.AppInfoRepository;
import cn.teleinfo.ds.business.domain.repository.EntPrefixRepository;
import cn.teleinfo.ds.business.domain.repository.UserRepository;
import cn.teleinfo.ds.business.domain.service.AppInfoDomainService;
import cn.teleinfo.ds.business.infrastructure.config.AppConfig;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.AppInfoEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.UserAppView;
import cn.teleinfo.ds.business.interfaces.dto.response.AppInfoTreeResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ProvincePrefixResponse;
import cn.teleinfo.ds.common.core.constant.UserConstants;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service
public class AppInfoDomainServiceImpl implements AppInfoDomainService {

	private final AppInfoRepository appInfoRepository;

	private final RoleService roleService;

	private final EntPrefixRepository entPrefixRepository;

	private final AppConfig appConfig;

	private final UserRepository userRepository;

	@Override
	public PageResponse<HandleSignAppInfoDTO> listHandleSignAppInfo(ListAppInfoQuery query) {
		//当前登录人
		Long userId = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(userId);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				query.setUserId(userId);
			}

		}
		return appInfoRepository.listHandleSignAppInfo(query, query.getCurrent(), query.getSize());
	}

	@Override
	public HandleSignAppInfoDTO queryHandleSignAppInfoDetail(String id) {
		return appInfoRepository.queryHandleSignAppInfoDetail(id);
	}

	@Override
	public AppInfoDomainEntity findByHandleCode(String appHandleCode) {
		return appInfoRepository.findByHandleCode(appHandleCode);
	}

	@Override
	public List<AppInfoTreeResponse> queryAppInfoTree() {
		//当前登录人
		Long userId = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(userId);
		List<AppInfoTreeResponse> resultTree = new ArrayList<>();
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			//如果是管理员 不过滤
			if (StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				List<EntPrefixDomainEntity> provincePrefixList = entPrefixRepository.findAllByProvincePrefix(appConfig.getProvincePrefix());
				if (CollectionUtil.isEmpty(provincePrefixList)) {
					return null;
				}
				List<AppInfoEntity> allAppInfos = appInfoRepository.findAll();

				//  先遍历provincePrefix作为第一层
				for (EntPrefixDomainEntity prefixEntity : provincePrefixList) {
					// 创建新的节点对象（避免修改原实体）
					AppInfoTreeResponse treeNode = new AppInfoTreeResponse();
					BeanUtils.copyProperties(prefixEntity, treeNode);  // 复制属性
					treeNode.setEntName(prefixEntity.getOrgName());
					treeNode.setAppInfo(new ArrayList<>());  // 初始化children

					// 遍历所有AppInfo，匹配的放入children
					for (AppInfoEntity appInfo : allAppInfos) {
						if (StrUtil.isNotBlank(appInfo.getEntPrefix()) && StrUtil.isNotBlank(prefixEntity.getEntPrefix()) && appInfo.getEntPrefix().equals(prefixEntity.getEntPrefix())) {
							treeNode.getAppInfo().add(appInfo);
						}
					}
					resultTree.add(treeNode);
				}
			} else {
				List<UserAppView> userAppByUserId = userRepository.findUserAppByUserId(userId);
				if (CollectionUtil.isEmpty(userAppByUserId)) {
					return null;
				}

				// 获取所有应用的 appId 并转为 Set 以提高查找效率
				Set<Long> appIdSet = userAppByUserId.stream()
						.map(UserAppView::getAppId)
						.collect(Collectors.toSet());

				List<AppInfoEntity> allAppInfos = appInfoRepository.findAll();
				if (CollectionUtil.isEmpty(allAppInfos)) {
					return null;
				}

				// 筛选出 appId 在 appIdSet 中的应用信息
				List<AppInfoEntity> appInfoEntities = allAppInfos.stream()
						.filter(appInfoEntity -> appIdSet.contains(appInfoEntity.getId()))
						.collect(Collectors.toList());
				if (CollectionUtil.isEmpty(appInfoEntities)) {
					return null;
				}

				// 获取所有唯一的 entPrefix
				Set<String> entPrefixSet = appInfoEntities.stream()
						.map(AppInfoEntity::getEntPrefix)
						.collect(Collectors.toSet());
				if (CollectionUtil.isEmpty(entPrefixSet)) {
					return null;
				}

				// 根据 entPrefix 集合查询 EntPrefixDomainEntity
				List<EntPrefixDomainEntity> entitieList = entPrefixRepository.findAllByListEntPrefix(new ArrayList<>(entPrefixSet));
				if (CollectionUtil.isEmpty(entitieList)) {
					return null;
				}

				// 使用 Map 来映射 entPrefix 到对应的 AppInfoEntity 列表
				Map<String, List<AppInfoEntity>> entPrefixToAppInfoMap = appInfoEntities.stream()
						.filter(app -> app.getEntPrefix() != null)
						.collect(Collectors.groupingBy(AppInfoEntity::getEntPrefix));

				// 构建最终结果树
				resultTree = entitieList.stream()
						.map(entPrefixDomainEntity -> {
							AppInfoTreeResponse appInfoTreeResponse = new AppInfoTreeResponse();
							BeanUtils.copyProperties(entPrefixDomainEntity, appInfoTreeResponse);
							appInfoTreeResponse.setEntName(entPrefixDomainEntity.getOrgName());
							// 获取该 entPrefix 下的所有应用
							List<AppInfoEntity> appsForPrefix = entPrefixToAppInfoMap.getOrDefault(entPrefixDomainEntity.getEntPrefix(), Collections.emptyList());
							appInfoTreeResponse.setAppInfo(appsForPrefix);
							return appInfoTreeResponse;
						})
						.collect(Collectors.toList());

				return resultTree;
			}
			return resultTree;
		}
		return Collections.emptyList();
	}

	@Override
	public AppInfoDomainEntity findById(Long id) {
		return appInfoRepository.findById(id);
	}

	@Override
	public ProvincePrefixResponse getGlobalConfig() {
		ProvincePrefixResponse response = new ProvincePrefixResponse();
		response.setProvincePrefix(appConfig.getProvincePrefix());
		response.setProvinceOrgName(appConfig.getProvinceOrgName());
		return response;
	}
}
