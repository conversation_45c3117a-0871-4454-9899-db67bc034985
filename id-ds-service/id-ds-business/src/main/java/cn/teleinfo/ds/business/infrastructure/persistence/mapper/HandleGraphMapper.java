package cn.teleinfo.ds.business.infrastructure.persistence.mapper;

import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.model.valueobject.Edge;
import cn.teleinfo.ds.business.domain.model.valueobject.Graph;
import cn.teleinfo.ds.business.domain.model.valueobject.Node;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.graph.EdgeDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.graph.GraphDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.graph.NodeDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleItemEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleReferenceEntity;

import java.util.ArrayList;
import java.util.List;

@Component
public class HandleGraphMapper {
    public Graph toGraph(GraphDTO graphDTO) {
        Graph graph = new Graph();
        List<Edge> edges = new ArrayList<>();
        List<Node> nodes = new ArrayList<>();
        for (NodeDTO nodeDTO : graphDTO.getNodes()) {
            Node node = new Node();
            BeanUtils.copyProperties(nodeDTO, node);
            nodes.add(node);
        }
        for (EdgeDTO edgeDTO : graphDTO.getEdges()) {
            Edge edge = new Edge();
            BeanUtils.copyProperties(edgeDTO, edge);
            edges.add(edge);
        }
        graph.setEdges(edges);
        graph.setNodes(nodes);
        return graph;
    }

    public Handle toHandle(List<HandleItemDomainEntity> items, HandleDomainEntity handleDomainEntity) {

        Handle handle = new Handle();
        handle.setHandleDomainEntity(handleDomainEntity);
        handle.setHandleItemModelEntities(items);
        return handle;

    }

    public HandleDomainEntity toHandleDomainEntity(HandleEntity entity) {
        if (entity == null)
            return null;
        HandleDomainEntity domain = new HandleDomainEntity();
        BeanUtils.copyProperties(entity, domain);
        return domain;
    }

    public List<HandleItemDomainEntity> toHandleItemDomainEntities(List<HandleItemEntity> items) {
        List<HandleItemDomainEntity> list = new ArrayList<>();
        if (items != null) {
            for (HandleItemEntity item : items) {
                HandleItemDomainEntity domain = new HandleItemDomainEntity();
               BeanUtils.copyProperties(item, domain);
                list.add(domain);
            }
        }
        return list;
    }

    public List<HandleReferenceDomainEntity> toHandleReferenceDomainEntities(List<HandleReferenceEntity> refs) {
        List<HandleReferenceDomainEntity> list = new ArrayList<>();
        if (refs != null) {
            for (HandleReferenceEntity ref : refs) {
                HandleReferenceDomainEntity domain = new HandleReferenceDomainEntity();
                BeanUtils.copyProperties(ref, domain);
                list.add(domain);
            }
        }
        return list;
    }

}
