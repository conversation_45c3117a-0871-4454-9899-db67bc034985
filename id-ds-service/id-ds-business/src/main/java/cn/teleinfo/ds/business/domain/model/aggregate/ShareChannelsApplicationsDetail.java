package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareChannelAuthValue;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsDetailView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareChannelsApplicationsDetail {

	/**
	 * id
	 */
	private String id;

	/**
	 * 共享通道id
	 */
	private String shareChannelId;

	/**
	 * 数据通道id
	 */
	private String dataChannelId;

	/**
	 * 共享通道名称
	 */
	private String shareChannelName;

	/**
	 * 所属标识
	 */
	private String handle;

	/**
	 * 数据类型
	 */
	private String dataType;

	/**
	 * 版本
	 */
	private String version;

	/**
	 * 应用名
	 */
	private String appName;

	/**
	 * 企业名
	 */
	private String entName;

	/**
	 * 探测状态
	 */
	private String detectionStatus;

	/**
	 * 自动sql
	 */
	private String defaultSql;

	/**
	 * 手动sql
	 */
	private String customSql;

	/**
	 * 申请人
	 */
	private String applyUser;

	/**
	 * 申请时间
	 */
	private LocalDateTime updatedTime;

	/**
	 * 变更原因
	 */
	private String changeReason;

	/**
	 * 申请状态
	 */
	private String channelStatus;

	/**
	 * 审核记录
	 */
	private List<ShareChannelAuthValue> items;

	public ShareChannelsApplicationsDetail(ShareChannelsApplicationsDetailView shareChannelsVersionDTO,
										   List<ShareChannelsVersionAuthDTO> shareChannelsVersionAuthDTOList) {
		this.id = Convert.toStr(shareChannelsVersionDTO.getId(), "");
		this.shareChannelId = Convert.toStr(shareChannelsVersionDTO.getShareChannelId(), "");
		this.dataChannelId = Convert.toStr(shareChannelsVersionDTO.getDataChannelId(), "");
		this.shareChannelName = shareChannelsVersionDTO.getShareChannelName();
		this.handle = shareChannelsVersionDTO.getHandle();
		this.dataType = Convert.toStr(shareChannelsVersionDTO.getDataType(), "");
		this.version = "V" + shareChannelsVersionDTO.getMainVersion() + "." + shareChannelsVersionDTO.getMinorVersion();
		this.appName = shareChannelsVersionDTO.getAppName();
		this.entName = shareChannelsVersionDTO.getEntName();
		this.detectionStatus = Convert.toStr(shareChannelsVersionDTO.getDetectionStatus(), "");
		this.defaultSql = shareChannelsVersionDTO.getDefaultSql();
		this.customSql = shareChannelsVersionDTO.getCustomSql();
		this.applyUser = shareChannelsVersionDTO.getApplyUser();
		this.updatedTime = shareChannelsVersionDTO.getUpdatedTime();
		this.changeReason = shareChannelsVersionDTO.getChangeReason();
		this.channelStatus = shareChannelsVersionDTO.getChannelStatus();
		this.items = BeanUtil.copyToList(shareChannelsVersionAuthDTOList, ShareChannelAuthValue.class);
	}

}
