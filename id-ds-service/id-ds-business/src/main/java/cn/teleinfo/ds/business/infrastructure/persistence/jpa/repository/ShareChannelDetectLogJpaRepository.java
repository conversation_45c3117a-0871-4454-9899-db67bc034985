package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelDetectLogEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShareChannelDetectLogJpaRepository extends BaseRepository<ShareChannelDetectLogEntity, Long> {

	List<ShareChannelDetectLogEntity> findByShareChannelIdOrderByUpdateTimeDesc(Long id);

	List<ShareChannelDetectLogEntity> findByShareChannelIdAndMainVersionAndMinorVersionOrderByUpdateTimeDesc(Long id, Integer mainVersion, Integer minorVersion);
}