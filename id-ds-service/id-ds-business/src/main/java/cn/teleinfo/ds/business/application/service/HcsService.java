package cn.teleinfo.ds.business.application.service;

import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigCommodityOrder;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigDataSourceView;
import com.huaweicloud.sdk.dataartsstudio.v1.model.DatabasesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.Workspacebody;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;

import java.util.List;

public interface HcsService {

	/**
	 * 数据集成-规范层连接名称
	 */
	List<Links> findCdmConnections(Integer platformType, String projectId, String clusterId);


	/**
	 * 数据开发-规范层连接名称
	 */
	List<ApigDataSourceView> findDayuConnections(Integer platformType, String projectId,
												 String workspace, Integer offset, Integer limit) ;

	/**
	 * 数据开发-规范层数据库名称
	 */
	List<DatabasesList> findDayuConnectionsDatabases(Integer platformType, String projectId,
													 String workspace, String connectionId, Integer offset, Integer limit);

	/**
	 * 获取资源空间列表
	 */
	List<AuthProjectResult> findProjects(Integer platformType);

	/**
	 * 获取数据治理中心实例列表
	 */
	List<ApigCommodityOrder> findDasInstances(Integer platformType, String projectId, Integer current, Integer size);

	/**
	 * 获取工作空间列表
	 */
	List<Workspacebody> findDasWorkspaces(Integer platformType, String projectId, String instanceId, Integer current, Integer size);

	/**
	 * 获取CDM集群名称列表
	 */
	List<Clusters> findCdmClusters(Integer platformType, String projectId);

}
