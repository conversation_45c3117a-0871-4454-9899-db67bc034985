package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.ShareChannelAuthDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionAuthDTO;

import java.util.List;

public interface ShareChannelAuthRepository {

	List<ShareChannelsVersionAuthDTO> queryShareChannelsVersionAuthDTO(String shareChannelId);

	List<ShareChannelsVersionAuthDTO> queryShareChannelsVersionAuthDTO(String shareChannelId, String main_version, String minor_version);

	void save(ShareChannelAuthDomainEntity shareChannelAuthDomainEntity);
}
