package cn.teleinfo.ds.business.interfaces.dto.response;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.AppInfoEntDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.AppInfoEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AppInfoTreeResponse {

	private Long id;

	private String entName;

	private String entPrefix;

	private String provincePrefix;

	private List<AppInfoEntity> appInfo = new ArrayList<>();
}
