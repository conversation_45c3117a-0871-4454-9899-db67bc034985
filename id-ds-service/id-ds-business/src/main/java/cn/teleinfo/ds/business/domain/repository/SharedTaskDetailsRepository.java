package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDetailDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskShareDataValueDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskView;

import java.util.List;

public interface SharedTaskDetailsRepository {
	void save(SharedTaskDetailDomainEntity sharedTaskDetailDomainEntity);

    void deleteBySharedTaskId(Long id);

	SharedTaskView findSharedTaskViewById(Long id);

    List<SharedTaskShareDataValueDTO> getSharedTaskShareDataValueDTO(Long id);
}
