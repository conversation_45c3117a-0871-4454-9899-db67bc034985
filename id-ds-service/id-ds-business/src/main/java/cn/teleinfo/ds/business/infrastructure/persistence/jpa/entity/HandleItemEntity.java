package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Getter
@Setter
@Entity
@Table(name = "t_handle_item")
@SQLDelete(sql = "update t_handle_item set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class HandleItemEntity extends BaseEntity {
	/**
	 * 属性字段
	 */
	@Column(name = "field")
	private String field;

	/**
	 * 索引
	 */
	@Column(name = "field_index")
	private Integer fieldIndex;

	/**
	 * 描述
	 */
	@Column(name = "description")
	private String description;

	/**
	 * 属性类型（1固定值 2标识解析数据源 3标识值 4标识-属性）
	 */
	@Column(name = "field_type")
	private Integer fieldType;

	/**
	 * 标识ID（关联t_handle.id）
	 */
	@Column(name = "handle_id")
	private Long handleId;

	/**
	 * 属性值（大文本）
	 */
	@Lob
	@Column(name = "field_value")
	private String fieldValue;

	/**
	 * 数据通道ID
	 */
	@Column(name = "data_channel_id")
	private Long dataChannelId;

	/**
	 * 属性来源类型（0基础属性 1扩展属性）
	 */
	@Column(name = "field_source_type")
	private Integer fieldSourceType;

	/**
	 * 应用标识编码
	 */
	@Column(name = "app_handle_code")
	private String appHandleCode;

	/**
	 * 备注
	 */
	@Column(name = "remark")
	private String remark;

	/**
	 * 省级前缀
	 */
	@Column(name = "province_prefix")
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	@Column(name = "ent_prefix")
	private String entPrefix;

	/**
	 * 数据通道类型
	 */
	@Column(name = "data_channel_type")
	private Integer dataChannelType;

	/**
	 * 数据库名称
	 */
	@Column(name = "database_name")
	private String databaseName;

	/**
	 * 数据库IP
	 */
	@Column(name = "database_ip")
	private String databaseIp;

	/**
	 * 所属表
	 */
	@Column(name = "table_name")
	private String tableName;

	/**
	 * 所属字段
	 */
	@Column(name = "column_name")
	private String columnName;
}