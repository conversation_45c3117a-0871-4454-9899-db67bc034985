package cn.teleinfo.ds.business.interfaces.assembler;

import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.interfaces.dto.request.HandleItemDetailRequest;
import org.springframework.stereotype.Component;

@Component
public class HandleItemDetailAssembler {


	public HandleItemQuery toQuery(Long applicationId) {
		HandleItemQuery query = new HandleItemQuery();
		query.setShareTaskApplicationsDetailId(applicationId);
		return query;
	}

}
