package cn.teleinfo.ds.business.application.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.teleinfo.ds.business.application.query.ListHandlesQuery;
import cn.teleinfo.ds.business.application.service.HandlesApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.aggregate.HandleDirectory;
import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.service.*;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleListView;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleDetailResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
@AllArgsConstructor
public class HandlesApplicationServiceImpl implements HandlesApplicationService {
	private final HandlesDomainService handlesDomainService;
	private final HandleDirectoryDomainService handleDirectoryDomainService;
	private final HandleGraphDomainService handleGraphDomainService;
	private final EntPrefixDomainService entPrefixDomainService;
	private final ProvincePrefixDomainService provincePrefixDomainService;
	private final AppInfoDomainService appInfoDomainService;

	@Override
	public PageResponse<HandleListView> listHandles(ListHandlesQuery query) {
		Handle handle = new Handle(new HandleDomainEntity(query.getName(), query.getAppHandleCode(), query.getHandle()));
		return handlesDomainService.listHandles(handle, query.getCurrent(), query.getSize(), query.getStartTime(), query.getEndTime(), query.getUserHandleFilter());
	}

	@Override
	public HandleDomainEntity findByHandle(String handle) {
		HandleDomainEntity h = handlesDomainService.findByHandle(handle);

		EntPrefixDomainEntity ent = entPrefixDomainService.findByEntPrefix(h.getEntPrefix());
		h.setEntName(ent.getOrgName());
		ProvincePrefixDomainEntity province = provincePrefixDomainService.findByProvincePrefix(h.getProvincePrefix());
		h.setProvinceName(province.getOrgName());
		AppInfoDomainEntity app = appInfoDomainService.findByHandleCode(h.getAppHandleCode());
		h.setAppName(app.getAppName());
		return h;
	}

	@Override
	public List<HandleDirectory> directory() {
		//return handleDirectoryDomainService.findHandleDirectory();
		return handleDirectoryDomainService.findAllHandleDirectory();
	}

	@Override
	public Handle getHandleItemsByHandle(String handle) {
		return handleGraphDomainService.handleItems(handle);
	}

	@Override
	public HandleDetailResponse handleDetail(Long id) {
		return handlesDomainService.handleDetail(id);
	}
}
