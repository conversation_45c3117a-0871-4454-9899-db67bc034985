package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTaskInstance;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskInstanceDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskStatusView;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceDetailResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.util.List;

public interface SharedTaskInstanceDomainService {

	PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query);

	SharedTaskInstanceDetailResponse getShareTaskInstanceDetail(Long instanceId);

	void deleteBySharedTaskId(Long id);

	void save(SharedTaskInstanceDomainEntity sharedTaskInstance);

	/**
	 * 更新任务实例信息
	 * 任务执行时间
	 * 状态
	 * 共享数据总量等等
	 */
	void updateRuntimeInfo(SharedTaskInstanceDomainEntity sharedTaskInstance);

	List<SharedTaskStatusView> getSharedTaskStatus(Long id, String type);


	/**
	 * 共享任务执行
	 *
	 * @param sharedTaskInstance 执行实例聚合根；包含需要执行的子任务
	 * @return 本次执行实例的执行结果
	 */
	SharedTaskInstanceDomainEntity exec(SharedTaskInstance sharedTaskInstance);
}
