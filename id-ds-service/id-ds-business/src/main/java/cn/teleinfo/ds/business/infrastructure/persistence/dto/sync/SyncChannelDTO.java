package cn.teleinfo.ds.business.infrastructure.persistence.dto.sync;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通道dto
 */
@Data
public class SyncChannelDTO {

	/**
	 * 应用前缀
	 */
	private String appHandleCode;
	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;
	/**
	 * 数据通道ID
	 */
	private Long dataChannelId;
	/**
	 * 数据通道名称
	 */
	private String dataChannelName;
	/**
	 * 实例数据类型
	 */
	private Integer dataType;
	/**
	 * 企业前缀
	 */
	private String entPrefix;
	/**
	 * 主键id
	 */
	private Long id;
	/**
	 * 是否已删除
	 */
	private Integer isDeleted;
	/**
	 * 是否共享 1-共享 2-未共享
	 */
	private Integer isShare;
	/**
	 * 所属对象标识id
	 */
	private String objectHandle;
	/**
	 * 所属对象标识类型
	 */
	private Integer objectHandleType;
	/**
	 * 省级前缀
	 */
	private String provincePrefix;
	/**
	 * 查询sql
	 */
	private String querySql;
	/**
	 * 解析sql
	 */
	private String resolveSql;
	/**
	 * 通道原始id
	 */
	private Long sourceId;
	/**
	 * 修改时间
	 */
	private LocalDateTime updatedTime;

}
