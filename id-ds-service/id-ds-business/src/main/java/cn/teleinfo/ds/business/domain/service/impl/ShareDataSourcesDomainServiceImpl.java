package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesItem;
import cn.teleinfo.ds.business.domain.repository.AppInfoRepository;
import cn.teleinfo.ds.business.domain.repository.ShareDataSourcesRepository;
import cn.teleinfo.ds.business.domain.service.ShareDataSourcesDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareDataSourcesDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareSourceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareDataSourcesView;
import cn.teleinfo.ds.common.core.constant.UserConstants;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@AllArgsConstructor
@Service
public class ShareDataSourcesDomainServiceImpl implements ShareDataSourcesDomainService {

	private ShareDataSourcesRepository shareDataSourcesRepository;

	private final AppInfoRepository appInfoRepository;

	private final RoleService roleService;

	@Override
	public void createShareDataSources(Integer platformType, String appHandleCode, String items) {
		// 同一应用下只能创建一个共享源！
		List<ShareDataSourcesDomainEntity> shareDataSourcesDomainEntityList = shareDataSourcesRepository.findAllByAppHandleCodeAndPlatformType(appHandleCode, platformType);
		if (!shareDataSourcesDomainEntityList.isEmpty()) {
			throw new CheckedException("同一应用下只能创建一个共享源！");
		}

		ShareSourceEntity entity = new ShareSourceEntity();
		entity.setAppHandleCode(appHandleCode);
		entity.setPlatformType(platformType);
		entity.setItems(items);
		shareDataSourcesRepository.createShareDataSources(entity);
	}

	@Override
	public PageResponse<ShareSourceDTO> listShareDataSources(ShareDataSourcesDomainEntity entity, LocalDateTime start,
															 LocalDateTime end, Integer current, Integer size) {
		List<String> userHandleCodes = null;
		Long id = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(id);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				//获取该用户关联的所有应用
				List<String> handleCodes = appInfoRepository.findHandleCodeByUserId(id);
				if (!handleCodes.isEmpty()) {
					userHandleCodes = handleCodes;
				}
			}
		}
		return shareDataSourcesRepository.listShareDataSources(entity, userHandleCodes, start, end, current, size);
	}

	@Override
	public ShareDataSourcesDomainEntity findByAppHandleCode(String appHandleCode) {
		ShareDataSourcesDomainEntity shareDataSources = shareDataSourcesRepository.findByAppHandleCode(appHandleCode);
		if(shareDataSources == null){
			throw new CheckedException("共享源不存在或已删除！");
		}
		return shareDataSources;
	}

	@Override
	public ShareDataSourcesDomainEntity queryShareDataSourcesDetail(String id) {
		ShareDataSourcesView shareDataSources = shareDataSourcesRepository.queryShareDataSourcesDetail(id);

		ShareDataSourcesDomainEntity shareDataSourcesDomain = new ShareDataSourcesDomainEntity();
		shareDataSourcesDomain.setId(shareDataSources.getId());
		shareDataSourcesDomain.setAppHandleCode(shareDataSources.getAppCode());
		shareDataSourcesDomain.setAppName(shareDataSources.getAppName());
		shareDataSourcesDomain.setPlatformType(shareDataSources.getPlatformType());
		shareDataSourcesDomain.setItems(shareDataSources.getItems());

		shareDataSourcesDomain.setShareDataSourcesItem(JSONUtil.toBean(shareDataSources.getItems(), ShareDataSourcesItem.class));
		return shareDataSourcesDomain;
	}

	@Override
	public void updateShareDataSources(Integer platformType, String appHandleCode, String items, Long id) {
		List<ShareDataSourcesDomainEntity> shareDataSourcesDomainEntityList = shareDataSourcesRepository.findAllByAppHandleCodeAndPlatformType(appHandleCode, platformType);
		for (ShareDataSourcesDomainEntity shareDatasource : shareDataSourcesDomainEntityList) {
			if (!Objects.equals(shareDatasource.getId(), id)) {
				throw new CheckedException("同一应用下只能创建一个共享源！");
			}
		}

		ShareDataSourcesDomainEntity entity = shareDataSourcesRepository.findById(id);
		entity.setId(id);
		entity.setPlatformType(platformType);
		entity.setAppHandleCode(appHandleCode);
		entity.setItems(items);
		shareDataSourcesRepository.updateShareDataSources(entity);

	}

	@Override
	public void deleteShareDataSources(Long id) {
		ShareDataSourcesView shareDataSources = shareDataSourcesRepository.queryShareDataSourcesDetail(id.toString());

		// 应用至少需要一个共享源
		List<ShareDataSourcesDomainEntity> shareDatasource = shareDataSourcesRepository
				.findAllByAppHandleCode(shareDataSources.getAppCode()).stream()
				.filter(entity -> !id.equals(entity.getId())).toList();

		if (shareDatasource.isEmpty()) {
			throw new CheckedException("该应用仅有一个共享源，请勿删除！");
		}

		shareDataSourcesRepository.deleteShareDataSources(id);
	}

}
