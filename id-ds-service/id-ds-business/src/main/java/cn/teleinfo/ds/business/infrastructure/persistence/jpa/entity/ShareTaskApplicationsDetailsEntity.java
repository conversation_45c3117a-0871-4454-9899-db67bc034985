package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

/**
 * 共享任务-关联标识表
 */
@Getter
@Setter
@Entity
@Table(name = "t_share_task_applications_details")
@SQLDelete(sql = "update t_share_task_applications_details set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class ShareTaskApplicationsDetailsEntity extends AuditableEntity {

    /**
     * 省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * 应用身份编码
     */
    @Column(name = "app_handle_code")
    private String appHandleCode;

    /**
     * 标识编码
     */
    @Column(name = "handle")
    private String handle;

    /**
     * 标识属性
     */
    @Column(name = "fields", columnDefinition = "text")
    private String fields;

    /**
     * 共享任务编号
     */
    @Column(name = "task_no")
    private String taskNo;

	/**
	 * 共享任务表id
	 */
	@Column(name = "share_task_applications_id")
	private Long shareTaskApplicationsId;
} 