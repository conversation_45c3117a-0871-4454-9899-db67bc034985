package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.TargetSourceDetailDTO;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.time.LocalDateTime;
import java.util.List;

public interface TargetSourceDomainService {

	/**
	 * 保存目标源
	 */
	Long save(TargetSource targetSource);

	PageResponse<TargetSourceDomainEntity> listTargetSource(TargetSource targetSource, LocalDateTime start, LocalDateTime end, Integer page, Integer size);


	TargetSourceDomainEntity findById(Long id);


	void updateTargetSource(TargetSourceDomainEntity entity);

	void delTargetSource(Long id);

	TargetSourceDetailDTO queryTargetSourceDetail(String id);


	List<TargetSourceDomainEntity> findByAppHandleCode(String appHandleCode);
}
