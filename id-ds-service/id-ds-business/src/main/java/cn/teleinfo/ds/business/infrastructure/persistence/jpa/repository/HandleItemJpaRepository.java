package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleItemEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleItemView;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HandleItemJpaRepository extends BaseRepository<HandleItemEntity, Long> {
	List<Long> findIdBy();


	List<HandleItemEntity> findByHandleId(Long handleId);

	@Query(nativeQuery = true,
			value = "select " +
					"a.field as field, " +
					"a.description as description, " +
					"a.field_type as fieldType, " +
					"a.field_value as fieldValue, " +
					"a.field_source_type as fieldSourceType, " +
					"a.remark as remark " +
					"from t_handle_item a " +
					"left join t_handle b on a.handle_id = b.id and b.is_deleted = 0 " +
					"where b.handle = :handle and a.is_deleted = 0 "
	)
	List<HandleItemView> findAllByHandle(@Param("handle") String handle);

	@Query(nativeQuery = true, value = """
			select * from t_handle_item where id in :ids;
			""")
	List<HandleItemEntity> findAllByIds(@Param("ids") List<Long> ids);
}