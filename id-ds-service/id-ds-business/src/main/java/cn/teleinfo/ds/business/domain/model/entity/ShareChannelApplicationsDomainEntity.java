package cn.teleinfo.ds.business.domain.model.entity;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class ShareChannelApplicationsDomainEntity {

	/**
	 * id
	 */
	private Long id;

	/**
	 * 共享通道ID
	 */
	private String shareChannelId;

	/**
	 * 任务状态(1:申请中 2:已驳回 3:已授权)
	 */
	private Integer channelStatus;

	/**
	 * 审核人ID
	 */
	private Long auditUserId;

	/**
	 * 审核备注
	 */
	private String auditRemark;

	/**
	 * 0:否, NULL:是
	 */
	private Integer isDeleted;

	/**
	 * 主版本号
	 */
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	private Integer minorVersion;

	/**
	 * 申请人ID
	 */
	private Long applyUserId;

	/**
	 * 创建时间
	 */
	private Timestamp createdTime;

	/**
	 * 更新时间
	 */
	private Timestamp updatedTime;

}
