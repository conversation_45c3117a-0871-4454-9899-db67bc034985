package cn.teleinfo.ds.business.domain.model.entity;
// LAUNCHING :提交中
// RUNNING : 运行中
// FINISHED:执行成功
// FAILED:执行失败

import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.util.StrUtil;

// 华为脚本执行状态枚举
public enum HcsScriptRunStatus {
	LAUNCHING("LAUNCHING", "提交中"),
	RUNNING("RUNNING", "运行中"),
	FINISHED("FINISHED", "执行成功"),
	FAILED("FAILED", "执行失败"),
	;

	private final String code;
	private final String message;

	HcsScriptRunStatus(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String code() {
		return code;
	}

	public String message() {
		return message;
	}
}
