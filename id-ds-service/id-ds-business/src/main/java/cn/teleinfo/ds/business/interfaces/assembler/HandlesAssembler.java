package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.query.ListHandlesQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.aggregate.HandleDirectory;
import cn.teleinfo.ds.business.domain.model.entity.FieldSourceType;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.interfaces.dto.request.ListHandlesRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.HandInfoResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleDirectoryResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleItemResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ListHandlesResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class HandlesAssembler {

	public PageResponse<ListHandlesResponse> toListHandles(PageResponse<Handle> response) {
		var records = response.getRecords().stream().map(t -> BeanUtil.copyProperties(t.getHandleDomainEntity(), ListHandlesResponse.class)).toList();
		return new PageResponse<>(records, response.getTotal(), response.getSize(), response.getCurrent(), response.getPages());
	}


	public HandInfoResponse toHandInfoResponse(HandleDomainEntity h) {
		HandInfoResponse r = BeanUtil.copyProperties(h, HandInfoResponse.class);

		if (h.getHandleItems() != null && !h.getHandleItems().isEmpty()) {
			r.setItems(new ArrayList<>());
			r.setExtendItems(new ArrayList<>());

			for (HandleItemDomainEntity handleItem : h.getHandleItems()) {
				HandleItemResponse item = BeanUtil.copyProperties(handleItem, HandleItemResponse.class);
				if (FieldSourceType.BASIC.code() == handleItem.getFieldSourceType()) {
					r.getItems().add(item);
				}

				if (FieldSourceType.EXTEND.code() == handleItem.getFieldSourceType()) {
					r.getExtendItems().add(item);
				}
			}
		}
		return r;
	}

	public ListHandlesQuery toListHandlesQuery(ListHandlesRequest request) {
		return BeanUtil.copyProperties(request, ListHandlesQuery.class);
	}

	public List<HandleDirectoryResponse> toHandleDirectoryResponse(List<HandleDirectory> directory) {
		List<HandleDirectoryResponse> result = new ArrayList<>();
		for (HandleDirectory node : directory) {
			result.add(copyNode(node));
		}
		return result;
	}

	private static HandleDirectoryResponse copyNode(HandleDirectory source) {
		if (source == null) return null;

		HandleDirectoryResponse target = new HandleDirectoryResponse();
		BeanUtils.copyProperties(source, target);

		if (source.getChild() != null && !source.getChild().isEmpty()) {
			List<HandleDirectoryResponse> childList = new ArrayList<>();
			for (HandleDirectory child : source.getChild()) {
				childList.add(copyNode(child));
			}
			target.setChild(childList);
		}
		return target;
	}
}
