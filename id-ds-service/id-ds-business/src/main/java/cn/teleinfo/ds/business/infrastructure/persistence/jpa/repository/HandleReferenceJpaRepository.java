package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleReferenceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HandleReferenceJpaRepository extends BaseRepository<HandleReferenceEntity, Long> {
	List<Long> findIdBy();

	List<HandleReferenceEntity> findByHandleItemId(Long id);

	@Query(nativeQuery = true, value = """
			select * from t_handle_reference where id in :ids;
			""")
	List<HandleReferenceEntity> findAllByIds(@Param("ids") List<Long> ids);
}