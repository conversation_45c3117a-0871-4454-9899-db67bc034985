package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.EntPrefixDomainEntity;
import cn.teleinfo.ds.business.domain.repository.EntPrefixRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.EntPrefixEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.EntPrefixJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class EntPrefixRepositoryImpl implements EntPrefixRepository {
	private final EntPrefixJpaRepository entPrefixJpaRepository;
	/**
	 * 查询省级下的企业前缀
	 *
	 * @param provincePrefix 省级前缀
	 * @return
	 */
	@Override
	public List<EntPrefixDomainEntity> findAllByProvincePrefix(String provincePrefix) {
		List<EntPrefixEntity> all = entPrefixJpaRepository.findByProvincePrefix(provincePrefix);
		return BeanUtil.copyToList(all, EntPrefixDomainEntity.class);
	}

	@Override
	public EntPrefixDomainEntity findByEntPrefix(String entPrefix) {
		EntPrefixEntity ent = entPrefixJpaRepository.findByEntPrefix(entPrefix);
		return BeanUtil.copyProperties(ent,EntPrefixDomainEntity.class);
	}

	@Override
	public List<EntPrefixDomainEntity> findAllByListEntPrefix(List<String> entPrefix) {
		List<EntPrefixEntity> entPrefix1 = entPrefixJpaRepository.findByEntPrefixIn(entPrefix);
		return entPrefix1.stream().map(ent -> BeanUtil.copyProperties(ent,EntPrefixDomainEntity.class)).toList();
	}

	@Override
	public List<EntPrefixDomainEntity> findAll() {
		List<EntPrefixEntity> all = entPrefixJpaRepository.findAll();
		return BeanUtil.copyToList(all, EntPrefixDomainEntity.class);
	}
}
