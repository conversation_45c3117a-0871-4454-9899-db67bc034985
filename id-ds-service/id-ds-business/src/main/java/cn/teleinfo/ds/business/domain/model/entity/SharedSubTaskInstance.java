package cn.teleinfo.ds.business.domain.model.entity;

import lombok.Data;

import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

@Data
public class SharedSubTaskInstance {
	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 关联共享任务实例ID
	 */
	private Long sharedTaskInstanceId;

	/**
	 * 任务执行编号，如RW202506001
	 */
	private String taskInstanceNo;

	/**
	 * 对象标识
	 */
	private String handle;

	/**
	 * 子任务状态：0-未开始，1-进行中，2-成功，3-失败
	 */
	private Integer subTaskStatus;

	/**
	 * 当前执行步骤（失败时记录在哪一步失败）
	 * 1 分析湖仓映射关系
	 * 2. 查询批量共享通道 SQL
	 * 3. 数据写入对象标识
	 * 4. 创建离线管道任务
	 * 5. 执行任务写入目标源';
	 */
	private Integer currentStep;

	/**
	 * 运行时间
	 */
	private LocalDateTime runTime;

	/**
	 * 运行时长(秒)
	 */
	private Integer runDuration;

	/**
	 * 共享数据总量
	 */
	private Integer sharedDataCount;

	/**
	 * 任务日志路径
	 */
	private String logPath;

	/**
	 * 错误信息（失败时记录具体错误）
	 */
	private String errorMessage;


	private HandleDomainEntity handleEntity;
	private TargetSourceDomainEntity targetSource;
	private PlatformConnectionDomainEntity connection;


	// 任务执行的中间产物

	/**
	 * 根据对象标识生成输出表 SQL 内容
	 */
	private String outputTablesSQLContent;

	/**
	 * 表名：建表语句
	 */
	private HashMap<String, String> outputTables;

	/**
	 * 提取数据  SQL 内容
	 */
	private String extractSQLContent;

	private List<String> jobNames;

	private Path loggerPath;

	private Integer executionType;

}
