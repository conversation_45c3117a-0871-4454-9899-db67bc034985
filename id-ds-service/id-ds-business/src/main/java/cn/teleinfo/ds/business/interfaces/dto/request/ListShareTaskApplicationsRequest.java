package cn.teleinfo.ds.business.interfaces.dto.request;

import cn.teleinfo.ds.common.core.util.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ListShareTaskApplicationsRequest extends PageRequest {

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 任务类型
	 */
	private Integer taskType;

	/**
	 * 任务状态
	 */
	private Integer applicationsStatus;

	/**
	 * 开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	private LocalDateTime endTime;

	private String appHandleCode;

}
