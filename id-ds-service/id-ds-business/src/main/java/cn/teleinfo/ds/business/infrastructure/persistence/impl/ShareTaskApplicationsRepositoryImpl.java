package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareHandle;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplications;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationDomain;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationsDetailsValueObject;
import cn.teleinfo.ds.business.domain.repository.ShareTaskApplicationsRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsXqDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsDetailsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareHandleView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsDTOView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareTaskApplicationsDetailsJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareTaskApplicationsJpaRepository;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Component
@AllArgsConstructor
public class ShareTaskApplicationsRepositoryImpl implements ShareTaskApplicationsRepository {

	private final ShareTaskApplicationsJpaRepository repository;

	private final ShareTaskApplicationsDetailsJpaRepository detailsJpaRepository;

	/**
	 * 新增任务申请主体
	 *
	 * @param valueObject 任
	 * @return 任务申请
	 */
	@Override
	public ShareTaskApplicationsEntity createShareTaskApplicationsEntity(ShareTaskApplicationDomain valueObject) {
		ShareTaskApplicationsEntity entity = new ShareTaskApplicationsEntity();
		BeanUtil.copyProperties(valueObject, entity);
		if (StrUtil.isNotBlank(entity.getEntPrefix())) {
			entity.setProvincePrefix(entity.getEntPrefix().substring(0, entity.getEntPrefix().lastIndexOf('.')));
		}
		return repository.save(entity);
	}

	/**
	 * 创建任务申请
	 */
	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void createShareTaskApplicationsDetailsEntityS(List<ShareTaskApplicationsDetailsEntity> entityList) {
		detailsJpaRepository.saveAll(entityList);
	}

	@Override
	public void createShareTaskApplicationsDetails(List<ShareTaskApplicationsDetailsValueObject> list) {
		List<ShareTaskApplicationsDetailsEntity> entityList = list.stream().map(vo -> {
			ShareTaskApplicationsDetailsEntity entity = new ShareTaskApplicationsDetailsEntity();
			BeanUtil.copyProperties(vo, entity); // 支持忽略大小写、下划线等
			return entity;
		}).collect(Collectors.toList());
		detailsJpaRepository.saveAll(entityList);

	}

	@Override
	public void deleteShareTaskApplicationsDetailsEntityS(ShareTaskApplicationsCommand request) {
		List<ShareTaskApplicationsDetailsEntity> allDetails = detailsJpaRepository
				.findAllByShareTaskApplicationsId(request.getId());
		detailsJpaRepository.deleteAll(allDetails);
	}

	@Override
	public PageResponse<ShareTaskApplicationsView> listShareTaskApplications(ShareTaskApplicationsQuery request,
																			 Integer page, Integer size) {

		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);

		Page<ShareTaskApplicationsView> resultPage = repository.findShareTaskApplications(request.getTaskName(),
				request.getTaskNo(), request.getTaskType(), request.getApplicationsStatus(), request.getStartTime(),
				request.getEndTime(), request.getAppHandleCode(), request.getUserHandleCode(), pageable);

		return new PageResponse<>(resultPage.getContent(), resultPage.getTotalElements(), (long) size, (long) page,
				(long) resultPage.getTotalPages());
	}

	@Override
	public ShareTaskApplicationsDTOView findShareTaskApplicationsById(Long id) {
		return repository.findShareTaskApplicationsById(id);

	}

	@Override
	public ShareTaskApplicationsDomainEntity findShareTaskApplicationsDomainEntity(Long id) {
		Optional<ShareTaskApplicationsEntity> option = repository.findById(id);
		return option.map(e -> BeanUtil.copyProperties(e, ShareTaskApplicationsDomainEntity.class)).orElse(null);
	}

	@Override
	public void updateStatusById(Long id, Integer applicationsStatus) {
		Optional<ShareTaskApplicationsEntity> optional = repository.findById(id);
		if (optional.isPresent()) {
			ShareTaskApplicationsEntity shareTaskApplicationsEntity = optional.get();
			shareTaskApplicationsEntity.setApplicationsStatus(applicationsStatus);
			repository.save(shareTaskApplicationsEntity);
		}
	}

	@Override
	public ShareTaskApplicationsDomainEntity findById(Long id) {
		Optional<ShareTaskApplicationsEntity> entity = repository.findById(id);
		return entity
				.map(shareTaskApplicationsEntity -> BeanUtil.copyProperties(shareTaskApplicationsEntity,
						ShareTaskApplicationsDomainEntity.class))
				.orElse(null);
	}

	@Override
	public void deleteShareTaskApplications(Long id) {
		repository.deleteById(id);
	}

	@Override
	public List<ShareTaskApplications> findShareTaskApplicationsByTargetSourceIdAndStatus(Long id, Integer status) {
		var entityList = repository.findAllByTargetSourceIdAndApplicationsStatus(id, status);
		return BeanUtil.copyToList(entityList, ShareTaskApplications.class);
	}

	@Override
	public ShareHandleView findShareHandleByHandle(Long id) {
		return repository.findShareHandleByHandle(id);
	}

}
