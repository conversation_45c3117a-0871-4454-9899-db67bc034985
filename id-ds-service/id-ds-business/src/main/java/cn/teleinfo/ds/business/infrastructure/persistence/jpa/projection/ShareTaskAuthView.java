package cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection;

import java.time.LocalDateTime;

public interface ShareTaskAuthView {
    Long getId();
	Long getShareTaskApplicationsId();
	String getTaskName();
	String getTaskNo();
	Integer getTaskType();
	Integer getAuthStatus();
	Long getCommittedBy();
	String getCommittedByName();
	String getSourceType();
	String getProvincePrefix();
	String getEntPrefix();
	String getAppHandleCode();
	String getAppName();
    Long getAuditUserId();
    String getAuditUserName();
    String getAuditRemark();
	Long getTargetId();
	String getCronExpression();
	LocalDateTime getCreateTime();
	LocalDateTime getUpdateTime();
	Long getCreateBy();
	Long getUpdateBy();
	String getCreateByName();
	String getUpdateByName();
}