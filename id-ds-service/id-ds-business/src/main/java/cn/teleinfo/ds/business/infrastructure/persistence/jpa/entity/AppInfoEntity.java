package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.sql.Timestamp;

/**
 * 应用表
 */
@Getter
@Setter
@Entity
@Table(name = "t_app_info")
@SQLDelete(sql = "update t_app_info set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class AppInfoEntity extends BaseEntity {

    /**
     * 应用名称
     */
    @Column(name = "app_name")
    private String appName;

    /**
     * 标识编码
     */
    @Column(name = "handle_code")
    private String handleCode;

    /**
     * 部署地址
     */
    @Column(name = "deploy_address")
    private String deployAddress;

    /**
     * 系统版本
     */
    @Column(name = "sys_version")
    private String sysVersion;

    /**
     * 所属企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * 所属省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;

    /**
     * 类型: 1-中台应用,2-非中台应用
     */
    @Column(name = "app_type")
    private Integer appType;
} 