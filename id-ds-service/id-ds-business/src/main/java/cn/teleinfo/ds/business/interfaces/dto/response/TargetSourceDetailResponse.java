package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class TargetSourceDetailResponse {

	private String id;

	/**
	 * 目标源名称
	 */
	private String targetSourceName;

	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 应用标识
	 */
	private String appCode;

	/**
	 * 目标源类型
	 */
	private String platformType;

	/**
	 * 连接器类型
	 */
	private String connType;

	/**
	 * 数据库服务器
	 */
	private String databaseUrl;

	/**
	 * 端口
	 */
	private String port;

	/**
	 * 数据库名称
	 */
	private String databaseName;

	/**
	 * 数据库用户名
	 */
	private String databaseUsername;

	/**
	 * 数据库密码
	 */
	private String databasePassword;

	/**
	 * 操作时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 操作人
	 */
	private String updateUser;


}
