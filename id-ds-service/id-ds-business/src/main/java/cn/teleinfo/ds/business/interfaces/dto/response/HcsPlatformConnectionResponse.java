package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

@Data
public class HcsPlatformConnectionResponse {
	/**
	 * IAM_ENDPOINT
	 */
	private String iamEndpoint;

	/**
	 * CDM_ENDPOINT
	 */
	private String cdmEndpoint;

	/**
	 * DGC_ENDPOINT
	 */
	private String dgcEndpoint;

	/**
	 * DataArtsStudio_ENDPOINT
	 */
	private String dataArtsStudioEndpoint;
	/**
	 * Ak
	 */
	private String ak;
	/**
	 * SK
	 */
	private String sk;
	/**
	 * 资源空间
	 */
	private String projectId;

	/**
	 * 实例 Id
	 */
	private String instanceId;

	/**
	 * 工作空间
	 */
	private String workspace;

	/**
	 * 集群 Id
	 */
	private String clusterId;

	/**
	 * 数据集成-规范层连接名称
	 */
	private String cdmConnection;

	/**
	 * 数据集成-规范层数据库名称
	 */
	private String cdmConnectionDatabase;

	/**
	 * 数据开发-规范层连接id
	 */
	private String dayuConnectionId;

	/**
	 * 数据开发-规范层连接名称
	 */
	private String dayuConnection;
	/**
	 * 数据开发-规范层数据库名称
	 */
	private String dayuConnectionDatabase;
}
