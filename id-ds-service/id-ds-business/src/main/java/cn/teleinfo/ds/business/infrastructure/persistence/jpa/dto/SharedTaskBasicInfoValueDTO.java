package cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
public class SharedTaskBasicInfoValueDTO {

	private Long id;

	private Integer taskType;

	private Integer taskStatus;

	private Integer testStatus;

	private Integer runStatus;

	private Long lastRunDuration;

	private Integer lastSharedDataCount;

	private String taskNo;

	private String taskName;

	private String databaseName;

	private String appName;

	private String entName;

	private String lastExecutionNo;

	private String operator;

	private String cronExpression;

	private String cronDetail;

	private String targetName;

	private String appHandleName;

	private Timestamp updateTime;

	private Timestamp lastRunTime;


}
