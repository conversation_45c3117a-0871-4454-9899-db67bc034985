package cn.teleinfo.ds.business.interfaces.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ShareDataSourcesItemsRequest {

	/**
	 * app
	 */
	@NotNull(message = "应用信息不能为空")
	private ShareDataSourcesItemsDetailsRequest app;

	/**
	 * 资源空间
	 */
	@NotNull(message = "资源空间不能为空")
	private ShareDataSourcesItemsDetailsRequest project;

	/**
	 * 数据治理中心实例
	 */
	@NotNull(message = "数据治理中心实例不能为空")
	private ShareDataSourcesItemsDetailsRequest instance;

	/**
	 * 工作空间
	 */
	@NotNull(message = "工作空间不能为空")
	private ShareDataSourcesItemsDetailsRequest workspace;

	/**
	 * 数据集成-集群名称
	 */
	@NotNull(message = "数据集成-集群名称不能为空")
	private ShareDataSourcesItemsDetailsRequest cluster;

	/**
	 * 数据集成-贴源层连接名称
	 */
	@NotNull(message = "数据集成-贴源层连接名称不能为空")
	private ShareDataSourcesItemsDetailsRequest stgConn;

	/**
	 * 数据集成-贴源层数据库名称
	 */
	@NotNull(message = "数据集成-贴源层数据库名称不能为空")
	private ShareDataSourcesItemsDetailsRequest stgDatabase;

	/**
	 * 数据集成-规范层连接名称
	 */
	@NotNull(message = "数据集成-规范层连接名称不能为空")
	private ShareDataSourcesItemsDetailsRequest stdDataConn;

	/**
	 * 数据集成-规范层数据库名称
	 */
	@NotNull(message = "数据集成-规范层数据库名称不能为空")
	private ShareDataSourcesItemsDetailsRequest stdDataDatabase;

	/**
	 * 数据开发-规范层连接名称
	 */
	@NotNull(message = "数据开发-规范层连接名称不能为空")
	private ShareDataSourcesItemsDetailsRequest stdDataDayuConn;

	/**
	 * 数据开发-规范层数据库名称
	 */
	@NotNull(message = "数据开发-规范层数据库名称不能为空")
	private ShareDataSourcesItemsDetailsRequest stdDataDayuDatabase;
}
