package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ShareChannelsVersionSqlResponse {

	/**
	 * id
	 */
	private String id;

	/**
	 * 共享通道id
	 */
	private String shareChannelId;

	/**
	 * 数据通道id
	 */
	private String dataChannelId;

	/**
	 * 主版本号
	 */
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	private Integer minorVersion;

	/**
	 * 探测状态
	 */
	private String status;

	/**
	 * 启用状态
	 */
	private String enable;

	/**
	 * 自动sql
	 */
	private String autoSql;

	/**
	 * 手动sql
	 */
	private String editSql;

}
