package cn.teleinfo.ds.business.domain.model.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SharedTaskDomainEntity {

	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除: 0 未删除 null 已删除
	 */
	private Integer isDeleted = 0;

	/**
	 * 任务编号，如RW202506001
	 */
	private String taskNo;

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务类型：1-手动任务，2-定时任务
	 */
	private Integer taskType;

	/**
	 * 任务状态：1-启用，0-禁用
	 */
	private Integer taskStatus;

	/**
	 * 测试状态：1-成功，2-失败，0-未测试ƒ
	 */
	private Integer testStatus;

	/**
	 * 最后一次测试执行的任务实例ID
	 */
	private Long lastTestInstanceId;

	/**
	 * 最后一次正常执行的任务实例ID
	 */
	private Long lastExecutionInstanceId;

	/**
	 * 运行状态：1-运行中，2-成功，3-失败，0-未运行
	 */
	private Integer runStatus;

	/**
	 * 目标源ID
	 */
	private Long targetSourceId;

	/**
	 * 数据库名
	 */
	private String databaseName;

	/**
	 * 应用身份编码
	 */
	private String appHandleCode;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * CRON表达式，如0 0 12 * * ?（仅定时任务使用）
	 */
	private String cronExpression;

	/**
	 * 最后一次任务执行编号，如REDO202506011001
	 */
	private String lastExecutionNo;

	/**
	 * 最后一次运行时间
	 */
	private LocalDateTime lastRunTime;

	/**
	 * 最后一次运行时长(秒)
	 */
	private Integer lastRunDuration;

	/**
	 * 最后一次共享数据总量
	 */
	private Integer lastSharedDataCount;

	/**
	 * 操作人
	 */
	private String operator;


	private List<SharedTaskDetail> details;

	/**
	 * 最后一次测试执行的任务实例ID
	 */
	private String last_test_instance_id;

	/**
	 * 最后一次正常执行的任务实例ID
	 */
	private String last_execution_instance_id;

	/**
	 * 应用系统
	 */
	private String appHandleName;
}
