package cn.teleinfo.ds.business.application.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.query.ListSharedTaskQuery;
import cn.teleinfo.ds.business.application.service.SharedTaskApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareDataDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTaskDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTaskInstance;
import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.service.*;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskStatusView;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.NoSuchElementException;

@Slf4j
@Service
@AllArgsConstructor
public class SharedTaskApplicationServiceImpl implements SharedTaskApplicationService {

	private final SharedTaskDomainService sharedTaskDomainService;

	private final SharedTaskDetailDomainService sharedTaskDetailDomainService;

	private final SharedTaskInstanceDomainService sharedTaskInstanceDomainService;


	@Override
	public PageResponse<SharedTaskDomainEntity> listSharedTask(ListSharedTaskQuery query) {
		SharedTaskDomainEntity entity = BeanUtil.copyProperties(query, SharedTaskDomainEntity.class);
		return sharedTaskDomainService.listSharedTask(entity, query.getStart(), query.getEnd(), query.getCurrent(),
				query.getSize(), query.getTaskCode(), query.getAppHandleCode(), query.getEditStart(), query.getEditEnd());
	}

	@Override
	public SharedTaskDomainEntity getSharedTask(Long id) {
		return sharedTaskDomainService.getSharedTask(id);
	}

	/**
	 * 执行共享任务
	 *
	 * @param id 任务 id
	 */
	@Override
	public void execute(Long id, Integer executionType) {
		ExecutionType execType = ExecutionType.findByCode(executionType);

		SharedTaskDomainEntity sharedTask = this.sharedTaskDomainService.getSharedTask(id);

		if (TaskStatus.ENABLE.code() != sharedTask.getTaskStatus()) {
			throw new NoSuchElementException("共享任务未启用！");
		}

		if (sharedTask.getDetails() == null || sharedTask.getDetails().isEmpty()) {
			throw new NoSuchElementException("共享任务无共享内容！");
		}

		// 更新为运行中
		sharedTaskDomainService.updateLastInstance(sharedTask, null, execType);

		// 共享任务执行
		SharedTaskInstanceDomainEntity instance = sharedTaskInstanceDomainService.exec(new SharedTaskInstance(sharedTask, execType));

		// 更新共享任务信息。记录任务最后执行 id 等。
		sharedTaskDomainService.updateLastInstance(sharedTask, instance, execType);


//		SharedTask sharedTask = sharedTaskDomainService.sharedTask(id, execType);
//
//		sharedTask.start(); // 共享任务正式开始
//
//		// 保存执行实例
//		sharedTaskInstanceDomainService.save(sharedTask.getSharedTaskInstance());
//
//		// 更新上次执行实例 Id
//		sharedTaskDomainService.updateLastInstance(sharedTask);
//
//		// 分析湖仓映射关系
//		sharedTask.updateAllSubRuntime(SubTaskCurrentStep.MAPPING, 0, RunStatus.RUN, null);
//		sharedTaskDomainService.updateLastInstance(sharedTask);
//
//		sharedTaskDomainService.shareChannelConnected(sharedTask);
//		if (sharedTask.getSharedTaskInstance().getRunStatus() == RunStatus.FAIL.code()) {
//			sharedTaskInstanceDomainService.updateRuntimeInfo(sharedTask.getSharedTaskInstance());
//			sharedTaskDomainService.updateLastInstance(sharedTask);
//			sharedTask.destroyLogger();
//			return;
//		}
//
//		// 生成对象标识建表 SQL
//		sharedTask.updateAllSubRuntime(SubTaskCurrentStep.SHARE_CHANNEL_SQL, 0, RunStatus.RUN, null);
//		sharedTaskDomainService.updateLastInstance(sharedTask);
//
//		String SQL = sharedTaskDomainService.genOutputTablesSQL(sharedTask);
//		if (sharedTask.getSharedTaskInstance().getRunStatus() == RunStatus.FAIL.code()) {
//			sharedTaskInstanceDomainService.updateRuntimeInfo(sharedTask.getSharedTaskInstance());
//			sharedTaskDomainService.updateLastInstance(sharedTask);
//			sharedTask.destroyLogger();
//			return;
//		}
//
//		// 创建输出表
//		sharedTask.updateAllSubRuntime(SubTaskCurrentStep.WRITE_HANDLE, 0, RunStatus.RUN, null);
//		sharedTaskDomainService.updateLastInstance(sharedTask);
//
//		sharedTaskDomainService.createOutputTables(sharedTask, SQL);
//		if (sharedTask.getSharedTaskInstance().getRunStatus() == RunStatus.FAIL.code()) {
//			sharedTaskInstanceDomainService.updateRuntimeInfo(sharedTask.getSharedTaskInstance());
//			sharedTaskDomainService.updateLastInstance(sharedTask);
//			sharedTask.destroyLogger();
//			return;
//		}
//
//		// 生成用于数据抽取的 SQL
//		String extractSQL = sharedTaskDomainService.genExtractSQL(sharedTask);
//		if (sharedTask.getSharedTaskInstance().getRunStatus() == RunStatus.FAIL.code()) {
//			sharedTaskInstanceDomainService.updateRuntimeInfo(sharedTask.getSharedTaskInstance());
//			sharedTaskDomainService.updateLastInstance(sharedTask);
//			sharedTask.destroyLogger();
//			return;
//		}
//
//		// 写入输出表
//		sharedTask.updateAllSubRuntime(SubTaskCurrentStep.OFFLINE_TASK, 0, RunStatus.RUN, null);
//		sharedTaskDomainService.updateLastInstance(sharedTask);
//
//		sharedTaskDomainService.writeOutputTables(sharedTask, extractSQL);
//		if (sharedTask.getSharedTaskInstance().getRunStatus() == RunStatus.FAIL.code()) {
//			sharedTaskInstanceDomainService.updateRuntimeInfo(sharedTask.getSharedTaskInstance());
//			sharedTaskDomainService.updateLastInstance(sharedTask);
//			sharedTask.destroyLogger();
//			return;
//		}
//
//		if (ExecutionType.FORMAL.equals(execType)) {
//			// 迁移到目标源
//			sharedTask.updateAllSubRuntime(SubTaskCurrentStep.WRITE_TARGET_SOURCE, 0, RunStatus.RUN, null);
//			sharedTaskDomainService.updateLastInstance(sharedTask);
//
//			sharedTaskDomainService.outputToTargetDataSource(sharedTask);
//		}
//
//		// 最后更新所有执行实例
//		sharedTaskInstanceDomainService.updateRuntimeInfo(sharedTask.getSharedTaskInstance());
//
//		// 更新最终执行状态
//		sharedTaskDomainService.updateLastInstance(sharedTask);
//
//		sharedTask.destroyLogger();
	}

	@Override
	public void updateSharedTaskStatus(Long id) {
		SharedTaskDomainEntity task = sharedTaskDomainService.getSharedTask(id);

		if (TaskStatus.ENABLE.code() == task.getTaskStatus()) {
			task.setTaskStatus(TaskStatus.DISABLE.code());
		} else if (TaskStatus.DISABLE.code() == task.getTaskStatus()) {
			task.setTaskStatus(TaskStatus.ENABLE.code());
		}

		sharedTaskDomainService.updateSharedTaskStatus(task);

	}

	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void deleteSharedTaskId(Long id) {
		sharedTaskDomainService.deleteSharedTask(id);
		sharedTaskDetailDomainService.deleteBySharedTaskId(id);
		sharedTaskInstanceDomainService.deleteBySharedTaskId(id);
	}

	@Override
	public List<SharedTaskStatusView> getSharedTaskStatus(Long id, String type) {
		return sharedTaskInstanceDomainService.getSharedTaskStatus(id, type);
	}

	@Override
	public ShareDataDetails getSharedDataDetails(Long dataId) {
		return sharedTaskDetailDomainService.getSharedDataDetails(dataId);
	}

	@Override
	public SharedTaskDetails getSharedTaskDetail(Long id) {
		return sharedTaskDomainService.getSharedTaskDetail(id);
	}

}
