package cn.teleinfo.ds.business.infrastructure.persistence.jpa;

import cn.hutool.core.lang.Snowflake;
import jakarta.persistence.PrePersist;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

// 实体类监听器
// 用于生成id
@Slf4j
@Component
@AllArgsConstructor
public class BaseEntityListeners {
	private final Snowflake snowflake;

	@PrePersist
	public void generateId(Object entity) {
		if (entity instanceof BaseEntity e) {
			if (e.getId() == null){
				e.setId(snowflake.nextId());
			}
		}else {
			Class<?> clazz = entity.getClass();
			try {
				Method method = clazz.getDeclaredMethod("setId", Long.class);
				method.invoke(entity, snowflake.nextId());
			} catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
				log.info("设置实体Id失败", e);
				throw new RuntimeException(e);
			}
		}
	}
}
