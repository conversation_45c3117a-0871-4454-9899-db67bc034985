package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskListDetail;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskBasicInfoValueDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface SharedTaskJpaRepository extends BaseRepository<SharedTaskEntity, Long> {

	List<SharedTaskEntity> findAllByTargetSourceId(Long targetSourceId);

	@Query(nativeQuery = true,
			value = "SELECT " +
					"a.id, " +
					"a.task_no as taskNo, " +
					"a.task_name as taskName, " +
					"a.task_type as taskType, " +
					"a.task_status as taskStatus, " +
					"a.test_status as testStatus, " +
					"a.last_test_instance_id as last_test_instance_id, " +
					"a.run_status as runStatus, " +
					"a.last_execution_instance_id as last_execution_instance_id, " +
					"a.last_run_time as lastRunTime, " +
					"c.name as operator, " +
					"a.create_time as createTime, " +
					"a.update_time as updateTime, " +
					"b.app_name as appHandleName " +
					"FROM t_shared_task a " +
					"LEFT JOIN t_app_info b ON a.app_handle_code = b.handle_code and b.is_deleted = 0 " +
					"LEFT JOIN sys_user c ON a.operator = c.user_id and c.del_flag = '0' " +
					"WHERE a.is_deleted = 0 " +
					"AND IF(:taskName != '' AND :taskName is not null, a.task_name like CONCAT('%',:taskName,'%'), 1=1 ) " +
					"AND IF(:taskType != '' AND :taskType is not null, a.task_type = :taskType, 1=1 ) " +
					"AND IF(:runStatus != '' AND :runStatus is not null, a.run_status  = :runStatus, 1=1 ) " +
					"AND IF(:start IS NOT NULL AND :end IS NOT NULL, a.last_run_time BETWEEN :start AND :end , 1=1) " +
					"AND IF(:taskCode != '' AND :taskCode is not null, a.task_no like CONCAT('%',:taskCode,'%'), 1=1 ) " +
					"AND IF(:appHandleCode != '' AND :appHandleCode is not null, a.app_handle_code like CONCAT('%',:appHandleCode,'%'), 1=1 ) " +
					"AND IF(COALESCE(:userHandleCode) IS NOT NULL, a.app_handle_code in (:userHandleCode), 1=1) " +
					"AND IF(:editStart IS NOT NULL AND :editEnd IS NOT NULL, a.update_time BETWEEN :editStart AND :editEnd , 1=1) " +
					"ORDER BY a.update_time DESC ",
			countQuery = "SELECT count(1) " +
					"FROM t_shared_task a " +
					"LEFT JOIN t_app_info b ON a.app_handle_code = b.handle_code and b.is_deleted = 0 " +
					"LEFT JOIN sys_user c ON a.operator = c.user_id and c.del_flag = '0' " +
					"WHERE a.is_deleted = 0 " +
					"AND IF(:taskName != '' AND :taskName is not null, a.task_name like CONCAT('%',:taskName,'%'), 1=1 ) " +
					"AND IF(:taskType != '' AND :taskType is not null, a.task_type = :taskType, 1=1 ) " +
					"AND IF(:runStatus != '' AND :runStatus is not null, a.run_status  = :runStatus, 1=1 ) " +
					"AND IF(:start IS NOT NULL AND :end IS NOT NULL, a.last_run_time BETWEEN :start AND :end , 1=1) " +
					"AND IF(:taskCode != '' AND :taskCode is not null, a.task_no like CONCAT('%',:taskCode,'%'), 1=1 ) " +
					"AND IF(:appHandleCode != '' AND :appHandleCode is not null, a.app_handle_code like CONCAT('%',:appHandleCode,'%'), 1=1 ) " +
					"AND IF(COALESCE(:userHandleCode) IS NOT NULL, a.app_handle_code in (:userHandleCode), 1=1) " +
					"AND IF(:editStart IS NOT NULL AND :editEnd IS NOT NULL, a.update_time BETWEEN :editStart AND :editEnd , 1=1) "
	)
    Page<SharedTaskListDetail> findListSharedTask(@Param("taskName") String taskName,
												  @Param("taskType") Integer taskType,
												  @Param("runStatus") Integer runStatus,
												  @Param("start") LocalDateTime start,
												  @Param("end") LocalDateTime end,
												  @Param("taskCode") String taskCode,
												  @Param("appHandleCode") String appHandleCode,
												  @Param("userHandleCode") List<String> userHandleCodes,
												  @Param("editStart") LocalDateTime editStart,
												  @Param("editEnd") LocalDateTime editEnd,
												  Pageable pageable);

	@Query(nativeQuery = true,
			value = "SELECT " +
					"a.id, " +
					"a.task_type as taskType, " +
					"a.task_status as taskStatus, " +
					"a.test_status as testStatus, " +
					"a.run_status as runStatus, " +
					"IF(a.run_status = 1, TIMESTAMPDIFF(SECOND, a.last_run_time, NOW()), a.last_run_duration) as lastRunDuration, " +
					"a.last_shared_data_count as lastSharedDataCount, " +
					"a.task_no as taskNo, " +
					"a.task_name as taskName, " +
					"a.database_name as databaseName, " +
					"a.app_handle_code as appName, " +
					"d.org_name as entName, " +
					"a.last_execution_no as lastExecutionNo, " +
					"c.name as operator, " +
					"a.cron_expression as cronExpression, " +
					"'' as cronDetail, " +
					"e.target_source_name as targetName, " +
					"b.app_name as appHandleName, " +
					"a.update_time as updateTime, " +
					"a.last_run_time as lastRunTime " +
					"FROM t_shared_task a " +
					"LEFT JOIN t_app_info b ON a.app_handle_code = b.handle_code and b.is_deleted = 0 " +
					"LEFT JOIN sys_user c ON a.operator = c.user_id and c.del_flag = '0' " +
					"LEFT JOIN t_ent_prefix d ON a.ent_prefix = d.ent_prefix " +
					"LEFT JOIN t_target_source e ON a.target_source_id = e.id and e.is_deleted = 0 " +
					"WHERE a.is_deleted = 0 AND a.id = :id "
	)
	SharedTaskBasicInfoValueDTO getSharedTaskBasicInfoValueDTO(@Param("id") Long id);
}