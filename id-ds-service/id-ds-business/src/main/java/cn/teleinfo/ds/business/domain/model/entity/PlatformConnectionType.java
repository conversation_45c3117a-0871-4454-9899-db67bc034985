package cn.teleinfo.ds.business.domain.model.entity;

/**
 * 系统设置
 * 连接设置
 * 平台类型枚举
 */
public enum PlatformConnectionType {
	/**
	 * 华为云
	 */
	HCS((byte) 0),
	/**
	 * 阿里云
	 */
	ALI((byte) 1),
	/**
	 * 自建
	 */
	CUSTOM((byte) 2);

	private final int code;

	PlatformConnectionType(int code) {
		this.code = code;
	}

	public int code() {
		return code;
	}

	public static PlatformConnectionType findByCode(int code) {
		for (PlatformConnectionType value : PlatformConnectionType.values()) {
			if (value.code() == code) {
				return value;
			}
		}

		throw new IllegalArgumentException("无效的平台类型: " + code);
	}
}
