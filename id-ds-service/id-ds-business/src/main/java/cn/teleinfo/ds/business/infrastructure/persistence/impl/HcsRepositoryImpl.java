package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.teleinfo.ds.business.domain.repository.HcsRepository;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmJobResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmJobStatusResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmLinkResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CreateJobTableDTO;
import cn.teleinfo.ds.business.infrastructure.external.hcs.DatabaseClient;
import cn.teleinfo.ds.business.infrastructure.external.hcs.HcsClient;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.HcsBaseResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ListScriptResultsResponseDTO;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ScriptInfoDTO;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.CreateJobResponse;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.cdm.v1.model.Submission;
import com.huaweicloud.sdk.dataartsstudio.v1.model.*;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Component
@Primary
public class HcsRepositoryImpl implements HcsRepository {
	private final HcsClient client;
	private final DatabaseClient databaseClient;

	/**
	 * 数据集成-规范层连接名称
	 */
	@Override
	public List<Links> findCdmConnections(String ak, String sk, String projectId, List<String> endpoints, String clusterId) {
		return client.listConnections(ak, sk, projectId, endpoints, clusterId);
	}

	/**
	 * 数据开发-规范层连接名称
	 */
	@Override
	public List<ApigDataSourceView> findDayuConnections(String ak, String sk, String projectId,
														List<String> endpoints, String workspace, Integer offset, Integer limit) {
		return client.listDataConnections(ak, sk, projectId, endpoints, workspace, offset, limit);
	}

	/**
	 * 数据开发-规范层数据库名称
	 */
	@Override
	public List<DatabasesList> findDayuConnectionsDatabases(String ak, String sk, String projectId,
															List<String> endpoints, String workspace, String connectionId, Integer offset, Integer limit) {
		return client.listDataDatabases(ak, sk, projectId, endpoints, workspace, connectionId, offset, limit);
	}

	/**
	 * 获取资源空间列表
	 */
	@Override
	public List<AuthProjectResult> findProjects(String ak, String sk, List<String> endpoints) {
		return client.keystoneListAuthProjects(ak, sk, endpoints);
	}

	/**
	 * 获取数据治理中心实例列表
	 */
	@Override
	public List<ApigCommodityOrder> findDasInstances(String ak, String sk, String projectId,
													 List<String> endpoints, Integer offset, Integer limit) {
		return client.listDataArtsStudioInstances(ak, sk, projectId, endpoints, offset, limit);
	}

	/**
	 * 获取工作空间列表
	 */
	@Override
	public List<Workspacebody> findDasWorkspaces(String ak, String sk, String projectId, String instanceId,
												 List<String> endpoints, Integer offset, Integer limit) {
		return client.listManagerWorkSpacesRequest(ak, sk, projectId, instanceId, endpoints, offset, limit);
	}

	/**
	 * 获取CDM集群名称列表
	 */
	@Override
	public List<Clusters> findCdmClusters(String ak, String sk, String projectId, List<String> endpoints) {
		return client.listClusters(ak, sk, projectId, endpoints);
	}

	@Override
	public List<Job> findJobs(String ak, String sk, String projectId, List<String> endpoints, String clusterId) {
		return client.listJobs(ak, sk, projectId, endpoints, clusterId);
	}

	@Override
	public void createScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName,
							 String scriptContent, String workspace, String dataBaseName, String connectionName) {
		client.createScript(ak, sk, projectId, endpoints, scriptName, scriptContent, workspace, dataBaseName, connectionName);
	}

	/**
	 * 查询脚本
	 */
	@Override
	public ScriptInfo findScript(String ak, String sk, String projectId, List<String> endpoints, String workspace, String scriptName) {
		return client.findScript(ak, sk, projectId, endpoints, workspace, scriptName);
	}

	/**
	 * 查询所有脚本
	 */
	@Override
	public ScriptInfoDTO findScriptList(String ak, String sk, String projectId, List<String> endpoints, String workspace, Integer limit, Integer offset) {
		return client.findScriptList(ak, sk, projectId, endpoints, workspace, limit, offset);
	}

	/**
	 * 删除脚本
	 */
	@Override
	public void deleteScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String workspace) {
		client.deleteScript(ak, sk, projectId, endpoints, scriptName, workspace);
	}

	@Override
	public String executeScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName,
								String workspace) {
		return client.executeScript(ak, sk, projectId, endpoints, scriptName, workspace);
	}

	@Override
	public ListScriptResultsResponseDTO listScriptResults(String ak, String sk, String projectId, List<String> endpoints,
														  String scriptName, String workspace, String instanceId) {
		return client.listScriptResults(ak, sk, projectId, endpoints, scriptName, workspace, instanceId);
	}

	@Override
	public List<TablesList> listTables(String ak, String sk, String projectId, List<String> endpoints, String connectionId, String databaseName, String tableName, String workSpace) {
		return client.listTables(ak, sk, projectId, endpoints, connectionId, databaseName, tableName, workSpace);
	}

	@Override
	public List<ColumnsList> listTableColumns(String ak, String sk, String projectId, List<String> endpoints, String connectionId, String workSpace, String tableId) {
		return client.listTableColumns(ak, sk, projectId, endpoints, connectionId, workSpace, tableId);
	}

	/**
	 * 创建作业
	 *
	 * @param ak           ak
	 * @param sk           sk
	 * @param projectId    projectId
	 * @param endpoints    endpoints
	 * @param clusterId    集群 Id
	 * @param table        迁移的表信息
	 * @param fromLinkName 源链接名称
	 * @param fromDatabase 源数据库
	 * @param toLinkName   目标链接名称
	 * @param toDatabase   目标数据库
	 */
	@Override
	public HcsBaseResponse createJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId, CreateJobTableDTO table, String fromLinkName, String fromDatabase, String toLinkName, String toDatabase) {
		return client.createJob(ak, sk, projectId, endpoints, clusterId, table, fromLinkName, fromDatabase, toLinkName, toDatabase);
	}

	@Override
	public HcsBaseResponse startJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName) {
		return client.startJob(ak, sk, projectId, endpoints, clusterId, jobName);
	}

	@Override
	public CdmJobStatusResponse showJobStatus(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName) {
		return client.showJobStatus(ak, sk, projectId, endpoints, clusterId, jobName);
	}

	@Override
	public HcsBaseResponse deleteJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName) {
		return client.deleteJob(ak, sk, projectId, endpoints, clusterId, jobName);
	}

	@Override
	public CdmJobResponse showJobs(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName) {
		return client.showJobs(ak, sk, projectId, endpoints, clusterId, jobName);
	}

	@Override
	public void createTable(String jdbcUrl, String username, String password, String sql) {
		databaseClient.exec(jdbcUrl, username, password, sql);
	}

	@Override
	public HcsBaseResponse createLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String host, Integer port, String database, String username, String password, String linkName) {
		return client.createLink(ak, sk, projectId, endpoints, clusterId, host, port, database, username, password, linkName);
	}

	@Override
	public CdmLinkResponse listLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String linkName) {
		return client.listConnections(ak, sk, projectId, endpoints, clusterId, linkName);
	}

	@Override
	public HcsBaseResponse updateLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String host, Integer port, String database, String username, String password, String linkName) {
		return client.updateLink(ak, sk, projectId, endpoints, clusterId, host, port, database, username, password, linkName);
	}

	@Override
	public HcsBaseResponse delLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String linkName) {
		return client.delLink(ak, sk, projectId, endpoints, clusterId, linkName);
	}
}
