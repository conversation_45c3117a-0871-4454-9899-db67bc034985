package cn.teleinfo.ds.business.domain.model.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.AuditableEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ShareTaskApplicationDomain extends AuditableEntity {

	private Long id;

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 任务类型(1：手动任务 2：定时任务)
	 */
	private Integer taskType;

	/**
	 * 任务状态( 1:申请中 2:已驳回 3:已授权)
	 */
	private Integer applicationsStatus;

	/**
	 * 提交人
	 */
	private Long committedBy;

	/**
	 * 共享源类型
	 */
	private String sourceType;

	/**
	 * 目标源ID
	 */
	private Long targetSourceId;

	/**
	 * 根节点
	 */
	private String rootHandle;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 应用身份编码
	 */
	private String appHandleCode;

	/**
	 * 图谱
	 */
	private String graph;

	/**
	 * 定时任务详情
	 */
	private String taskCron;

	private LocalDateTime createTime;

	/**
	 * 定时任务表达式
	 */
	private String cronExpression;
}
