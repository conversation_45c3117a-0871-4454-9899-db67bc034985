package cn.teleinfo.ds.business.application.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.application.service.ShareTaskApplicationsAppService;
import cn.teleinfo.ds.business.application.service.SharedTaskInstanceAppService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationDetailValue;
import cn.teleinfo.ds.business.domain.service.SharedTaskInstanceDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleItemDetailResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceDetailResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Service
public class SharedTaskInstanceAppServiceImpl implements SharedTaskInstanceAppService {

	private final SharedTaskInstanceDomainService service;

	private final ShareTaskApplicationsAppService shareTaskApplicationsAppService;

	@Override
	public PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query) {
		return service.listSharedTaskInstances(query);
	}

	@Override
	public SharedTaskInstanceDetailResponse getShareTaskInstanceDetail(Long instanceId) {
		return service.getShareTaskInstanceDetail(instanceId);
	}

	@Override
	public HandleApplicationResponse getHandleItemsByHandle(Long applicationId) {

		ShareTaskApplicationDetails details = shareTaskApplicationsAppService.getShareTaskApplicationDetail(applicationId);
		if (ObjectUtil.isNull(details)) {
			return null;
		}
		List<HandleItemDetailResponse> items = new ArrayList<>();
		List<HandleItemDetailResponse> extendItems = new ArrayList<>();
		List<ShareTaskApplicationDetailValue> data = details.getShareData();
		if (CollectionUtil.isNotEmpty(data)) {
			for (ShareTaskApplicationDetailValue detail : data) {
				HandleItemQuery query = new HandleItemQuery();
				query.setShareTaskApplicationsDetailId(detail.getId());
				HandleApplicationResponse handle = shareTaskApplicationsAppService.getHandleItemsByHandle(query);
				if (ObjectUtil.isNotNull(handle)) {
					items.addAll(handle.getItems());
					extendItems.addAll(handle.getExtendItems());
				}
			}
		}
		HandleApplicationResponse applicationResponse = new HandleApplicationResponse();
		applicationResponse.setItems(items);
		applicationResponse.setExtendItems(extendItems);
		return applicationResponse;
	}
}
