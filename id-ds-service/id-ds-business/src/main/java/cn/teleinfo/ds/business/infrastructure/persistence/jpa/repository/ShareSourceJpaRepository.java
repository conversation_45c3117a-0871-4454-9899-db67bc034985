package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareSourceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareDataSourcesView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareSourceView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShareSourceJpaRepository extends BaseRepository<ShareSourceEntity, Long> {

	@Query(nativeQuery = true,
			value = """
					SELECT
						s.id,
						s.platform_type AS platformType,
						s.conn_state AS connState,
						s.app_handle_code AS appHandleCode,
						a.app_name AS appName,
						s.update_by AS updateBy,
						s.create_time AS createTime,
						s.update_time AS updateTime,
						u.`name` AS updateByName
					FROM t_share_source s
						LEFT JOIN sys_user u ON s.update_by = u.user_id AND u.del_flag = 0
						JOIN t_app_info a ON s.app_handle_code = a.handle_code\s
							AND IF(:appName != '' AND :appName is not null, a.app_name like CONCAT('%',:appName,'%'), 1=1 )
							AND a.is_deleted = 0
					WHERE IF(:appHandleCode IS NOT NULL AND :appHandleCode != '', s.app_handle_code = :appHandleCode, 1=1 )
					AND IF(:platformType IS NOT NULL, s.platform_type = :platformType, 1=1 )
					AND IF(:connState IS NOT NULL, s.conn_state = :connState, 1=1 )
					AND IF(:startDate IS NOT NULL AND :endDate IS NOT NULL, s.update_time BETWEEN :startDate AND :endDate , 1=1)
					AND IF(COALESCE(:userHandleCode) IS NOT NULL, s.app_handle_code in (:userHandleCode), 1=1)\s
					AND s.is_deleted = 0 order by s.update_time desc
					""",
			countQuery =
					"""
							SELECT
								count(*)
							FROM t_share_source s
								LEFT JOIN sys_user u ON s.update_by = u.user_id AND u.del_flag = 0
								JOIN t_app_info a ON s.app_handle_code = a.handle_code\s
									AND IF(:appName != '' AND :appName is not null, a.app_name like CONCAT('%',:appName,'%'), 1=1 )
									AND a.is_deleted = 0
							WHERE IF(:appHandleCode IS NOT NULL AND :appHandleCode != '', s.app_handle_code = :appHandleCode, 1=1 )
							AND IF(:platformType IS NOT NULL, s.platform_type = :platformType, 1=1 )
							AND IF(:connState IS NOT NULL, s.conn_state = :connState, 1=1 )
							AND IF(:startDate IS NOT NULL AND :endDate IS NOT NULL, s.update_time BETWEEN :startDate AND :endDate , 1=1)
							AND IF(COALESCE(:userHandleCode) IS NOT NULL, s.app_handle_code in (:userHandleCode), 1=1)\s
							AND s.is_deleted = 0
							"""
	)
	Page<ShareSourceView> listShareDataSources(@Param("appHandleCode") String appHandleCode,
											   @Param("appName") String appName,
											   @Param("platformType") Integer platformType,
											   @Param("connState") Integer connState,
											   @Param("userHandleCode") List<String> userHandleCode,
											   @Param("startDate") LocalDateTime startDate,
											   @Param("endDate") LocalDateTime endDate,
											   Pageable pageable);

	ShareSourceEntity findFirstByAppHandleCode(String appHandleCode);

	@Query(nativeQuery = true,
			value = "SELECT " +
					"a.id, " +
					"a.platform_type AS platformType, " +
					"a.app_handle_code as appCode, " +
					"b.app_name AS appName, " +
					"a.items as items " +
					"from t_share_source a " +
					"left join t_app_info b on a.app_handle_code = b.handle_code and b.is_deleted = 0 " +
					"where a.id = :id and a.is_deleted = 0"
	)
	ShareDataSourcesView queryShareDataSourcesDetail(@Param("id") String id);

	List<ShareSourceEntity> findAllByAppHandleCode(String appHandleCode);

	List<ShareSourceEntity> findAllByAppHandleCodeAndPlatformType(String appHandleCode, Integer platformType);
}