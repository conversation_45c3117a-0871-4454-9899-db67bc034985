package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.application.service.AppInfoService;
import cn.teleinfo.ds.business.domain.service.AppInfoDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import cn.teleinfo.ds.business.interfaces.dto.response.AppInfoTreeResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ProvincePrefixResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class AppInfoServiceImpl implements AppInfoService {

	private final AppInfoDomainService appInfoDomainService;

	@Override
	public PageResponse<HandleSignAppInfoDTO> listHandleSignAppInfo(ListAppInfoQuery query) {
		return appInfoDomainService.listHandleSignAppInfo(query);
	}

	@Override
	public HandleSignAppInfoDTO queryHandleSignAppInfoDetail(String id) {
		return appInfoDomainService.queryHandleSignAppInfoDetail(id);
	}

	@Override
	public List<AppInfoTreeResponse> queryAppInfoTree() {
		return appInfoDomainService.queryAppInfoTree();
	}

	@Override
	public ProvincePrefixResponse getGlobalConfig() {
		return appInfoDomainService.getGlobalConfig();
	}

}
