package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.aggregate.HandleDirectory;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.EntPrefixDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleDirectoryType;
import cn.teleinfo.ds.business.domain.model.entity.ProvincePrefixDomainEntity;
import cn.teleinfo.ds.business.domain.repository.AppInfoRepository;
import cn.teleinfo.ds.business.domain.repository.EntPrefixRepository;
import cn.teleinfo.ds.business.domain.repository.ProvincePrefixRepository;
import cn.teleinfo.ds.business.domain.service.HandleDirectoryDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.AppInfoEntity;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class HandleDirectoryDomainServiceImpl implements HandleDirectoryDomainService {
	private final ProvincePrefixRepository provincePrefixRepository;
	private final EntPrefixRepository entPrefixRepository;
	private final AppInfoRepository appInfoRepository;

	/**
	 * 查询对象标识目录；全量
	 */
	@Override
	public List<HandleDirectory> findHandleDirectory() {
		List<ProvincePrefixDomainEntity> provinces = provincePrefixRepository.findAll();
		List<HandleDirectory> dirs = new ArrayList<>();
		if (provinces != null && !provinces.isEmpty()) {
			for (ProvincePrefixDomainEntity province : provinces) {
				HandleDirectory dir = new HandleDirectory();
				dir.setId(province.getId());
				dir.setName(province.getOrgName());
				dir.setCode(province.getOrgCode());
				dir.setType(HandleDirectoryType.PROVINCE.code());
				List<HandleDirectory> child = this.findEnt(province.getProvincePrefix());
				dir.setChild(child);

				dirs.add(dir);
			}
		}

		return dirs;
	}

	private List<HandleDirectory> findEnt(String provincePrefix){
		List<HandleDirectory> dirs = new ArrayList<>();
		List<EntPrefixDomainEntity> ents = entPrefixRepository.findAllByProvincePrefix(provincePrefix);
		if (ents != null && !ents.isEmpty()){
			for (EntPrefixDomainEntity ent : ents) {
				HandleDirectory dir = new HandleDirectory();
				dir.setId(ent.getId());
				dir.setName(ent.getOrgName());
				dir.setCode(ent.getOrgCode());
				dir.setType(HandleDirectoryType.ENT.code());
				List<HandleDirectory> child = this.findApp(ent.getEntPrefix());
				dir.setChild(child);

				dirs.add(dir);
			}
		}
		return dirs;
	}

	private List<HandleDirectory> findApp(String entPrefix){
		List<HandleDirectory> dirs = new ArrayList<>();
		List<AppInfoDomainEntity> apps = appInfoRepository.findAllByEntPrefix(entPrefix);
		if (apps != null && !apps.isEmpty()){
			for (AppInfoDomainEntity app : apps) {
				HandleDirectory dir = new HandleDirectory();
				dir.setId(app.getId());
				dir.setName(app.getAppName());
				dir.setCode(app.getHandleCode());
				dir.setType(HandleDirectoryType.APP.code());
				dirs.add(dir);
			}
		}
		return dirs;
	}


	@Override
	public List<HandleDirectory> findAllHandleDirectory() {
		List<ProvincePrefixDomainEntity> provinces = provincePrefixRepository.findAll();
		List<EntPrefixDomainEntity> ents = entPrefixRepository.findAll();
		List<AppInfoEntity> apps = appInfoRepository.findAll();

		// 构建内存索引提升查询效率
		Map<String, List<EntPrefixDomainEntity>> entMap = ents.stream()
				.filter(ent -> ent.getProvincePrefix() != null)
				.collect(Collectors.groupingBy(EntPrefixDomainEntity::getProvincePrefix));

		Map<String, List<AppInfoEntity>> appMap = apps.stream()
				.filter(app -> app.getEntPrefix() != null)
				.collect(Collectors.groupingBy(AppInfoEntity::getEntPrefix));

		return provinces.stream()
				.map(province -> {
					HandleDirectory dir = buildDirectoryNode(province, HandleDirectoryType.PROVINCE);
					List<HandleDirectory> entDirs = entMap.getOrDefault(province.getProvincePrefix(), Collections.emptyList())
							.stream()
							.map(ent -> {
								HandleDirectory entDir = buildDirectoryNode(ent, HandleDirectoryType.ENT);
								entDir.setChild(appMap.getOrDefault(ent.getEntPrefix(), Collections.emptyList())
										.stream()
										.map(app -> buildDirectoryNode(app, HandleDirectoryType.APP))
										.collect(Collectors.toList()));
								return entDir;
							})
							.collect(Collectors.toList());
					dir.setChild(entDirs);
					return dir;
				})
				.collect(Collectors.toList());
	}

	private HandleDirectory buildDirectoryNode(Object entity, HandleDirectoryType type) {
		HandleDirectory dir = new HandleDirectory();
		if (entity instanceof ProvincePrefixDomainEntity province) {
			dir.setId(province.getId());
			dir.setName(province.getOrgName());
			dir.setCode(province.getOrgCode());
		} else if (entity instanceof EntPrefixDomainEntity ent) {
			dir.setId(ent.getId());
			dir.setName(ent.getOrgName());
			dir.setCode(ent.getOrgCode());
		} else if (entity instanceof AppInfoEntity app) {
			dir.setId(app.getId());
			dir.setName(app.getAppName());
			dir.setCode(app.getHandleCode());
		}
		dir.setType(type.code());
		return dir;
	}
}
