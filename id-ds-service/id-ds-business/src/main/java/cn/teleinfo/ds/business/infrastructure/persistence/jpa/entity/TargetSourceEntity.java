package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Getter
@Setter
@Entity
@Table(name = "t_target_source")
@SQLDelete(sql = "update t_target_source set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class TargetSourceEntity extends AuditableEntity {

	/**
	 * 目标源名称
	 */
	@Column(name = "target_source_name")
	private String targetSourceName;

	/**
	 * 平台类型 0 华为 1 阿里 2 自建
	 */
	@Column(name = "platform_type")
	private Integer platformType;

	/**
	 * 应用标识
	 */
	@Column(name = "app_handle_code")
	private String appHandleCode;

	/**
	 * 配置内容
	 */
	@Column(name = "items")
	private String items;


}
