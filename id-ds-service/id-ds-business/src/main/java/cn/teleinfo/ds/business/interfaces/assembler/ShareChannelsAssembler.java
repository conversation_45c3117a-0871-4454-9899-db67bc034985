package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.command.CreateShareChannelVersionCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareChannelsApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ListShareChannelApplicationsQuery;
import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsVersion;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannelDetectLog;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareChannelsApplicationsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareChannelsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareChannelSaveRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareChannelsApplicationsReviewRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareChannelDetectLogResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareChannelsDetailsResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareChannelsResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareChannelsVersionResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ShareChannelsAssembler {

	public ListShareChannelsQuery toListShareChannelsQuery(ListShareChannelsRequest request) {
		return BeanUtil.copyProperties(request, ListShareChannelsQuery.class);
	}

	public PageResponse<ShareChannelsResponse> toShareChannelsResponse(
			PageResponse<ShareChannelsDTO> shareChannelsDTOPageResponse) {
		var records = shareChannelsDTOPageResponse.getRecords()
				.stream()
				.map(t -> BeanUtil.copyProperties(t, ShareChannelsResponse.class))
				.toList();
		return new PageResponse<>(records, shareChannelsDTOPageResponse.getTotal(),
				shareChannelsDTOPageResponse.getSize(), shareChannelsDTOPageResponse.getCurrent(),
				shareChannelsDTOPageResponse.getPages());
	}

	public ShareChannelsDetailsResponse toShareChannelsDetailsResponse(
			ShareChannelsDetailsDTO shareChannelsDetailsDTO) {
		return BeanUtil.copyProperties(shareChannelsDetailsDTO, ShareChannelsDetailsResponse.class);
	}

	public List<ShareChannelsDetailsResponse> toShareChannelsDetailsResponse(
			List<ShareChannelsDetailsDTO> shareChannelsDetailsDTOList) {
		return BeanUtil.copyToList(shareChannelsDetailsDTOList, ShareChannelsDetailsResponse.class);
	}

	public List<ShareChannelsVersionResponse> toShareChannelsVersionResponse(
			List<ShareChannelsVersion> shareChannelsDetailsDTOList) {
		return BeanUtil.copyToList(shareChannelsDetailsDTOList, ShareChannelsVersionResponse.class);
	}

	public ShareChannelDetectLogResponse toShareChannelLogResponse(
			ShareChannelDetectLog shareChannelDetectLog) {
		ShareChannelDetectLogResponse response = new ShareChannelDetectLogResponse();
		if (null == shareChannelDetectLog) {
			return response;
		}
		BeanUtil.copyProperties(shareChannelDetectLog, response);
		return response;
	}


	public UpdateShareChannelsApplicationsCommand toUpdateShareChannelsApplicationsCommand(
			ShareChannelsApplicationsReviewRequest request) {
		return BeanUtil.copyProperties(request, UpdateShareChannelsApplicationsCommand.class);

	}

	public CreateShareChannelVersionCommand toCreateShareChannelVersionCommand(ShareChannelSaveRequest request) {
		return BeanUtil.copyProperties(request, CreateShareChannelVersionCommand.class);
	}

	public ListShareChannelApplicationsQuery toListShareChannelApplicationsQuery(ListShareChannelsApplicationsRequest request) {
		return BeanUtil.copyProperties(request, ListShareChannelApplicationsQuery.class);
	}
}
