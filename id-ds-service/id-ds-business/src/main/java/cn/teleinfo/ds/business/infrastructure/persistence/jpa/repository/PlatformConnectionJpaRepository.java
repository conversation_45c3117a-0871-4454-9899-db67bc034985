package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.PlatformConnectionEntity;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PlatformConnectionJpaRepository extends BaseRepository<PlatformConnectionEntity, Long> {

	Optional<PlatformConnectionEntity> findByPlatformType(Integer platformType);


}
