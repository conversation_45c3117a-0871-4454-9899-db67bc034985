package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.SharedTask;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTaskDetails;
import cn.teleinfo.ds.business.domain.model.entity.ExecutionType;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskInstanceDomainEntity;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.time.LocalDateTime;

public interface SharedTaskDomainService {
	PageResponse<SharedTaskDomainEntity> listSharedTask(SharedTaskDomainEntity entity,
														LocalDateTime start, LocalDateTime end, Integer page, Integer size,
														String taskCode, String appHandleCode, LocalDateTime editStart, LocalDateTime editEnd);

	SharedTaskDomainEntity getSharedTask(Long id);

	// 组装聚合根
	SharedTask sharedTask(Long id, ExecutionType execType);

	String genOutputTablesSQL(SharedTask sharedTask);

	String genExtractSQL(SharedTask sharedTask);

	/**
	 * 创建输出表结构
	 */
	void createOutputTables(SharedTask sharedTask, String SQL);

	/**
	 * 写入输出表数据
	 */
	void writeOutputTables(SharedTask sharedTask, String extractSQL);

	/**
	 * 输出内容到指定数据源
	 */
	void outputToTargetDataSource(SharedTask sharedTask);

	void deleteSharedTask(Long id);

	void updateSharedTaskStatus(SharedTaskDomainEntity task);

	// 更新上一次执行实例
	void updateLastInstance(SharedTaskDomainEntity sharedTask, SharedTaskInstanceDomainEntity instance,ExecutionType execType);

	SharedTaskDetails getSharedTaskDetail(Long id);

	// 分析湖仓映射关系
	void shareChannelConnected(SharedTask sharedTask);


}
