package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannelApplicationsDomainEntity;
import cn.teleinfo.ds.business.domain.repository.ShareChannelApplicationsRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelApplicationsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareChannelApplicationsJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
public class ShareChannelApplicationsRepositoryImpl implements ShareChannelApplicationsRepository {

	private final ShareChannelApplicationsJpaRepository shareChannelApplicationsJpaRepository;

	@Override
	public ShareChannelApplicationsDomainEntity queryById(String applicationId) {
		return BeanUtil.copyProperties(shareChannelApplicationsJpaRepository.queryById(Long.parseLong(applicationId)), ShareChannelApplicationsDomainEntity.class);
	}

	@Override
	public void save(ShareChannelApplicationsDomainEntity shareChannelApplicationsDomainEntity) {
		var entity = BeanUtil.copyProperties(shareChannelApplicationsDomainEntity, ShareChannelApplicationsEntity.class);
		shareChannelApplicationsJpaRepository.save(entity);
	}

	@Override
	public ShareChannelApplicationsDomainEntity findByShareChannelIdAndVersion(String shareChannelId, Integer mainVersion, Integer minorVersion) {
		return BeanUtil.copyProperties(shareChannelApplicationsJpaRepository.findByShareChannelIdAndMainVersionAndMinorVersion(shareChannelId, mainVersion, minorVersion), ShareChannelApplicationsDomainEntity.class);
	}
}
