package cn.teleinfo.ds.business.domain.model.entity;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SharedTaskDetailDomainEntity {

	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除: 0 未删除 null 已删除
	 */
	private Integer isDeleted = 0;

	/**
	 * 关联共享任务ID
	 */
	private Long sharedTaskId;

	/**
	 * 对象标识编码
	 */
	private String handle;

	/**
	 * 属性清单（JSON数组格式）
	 */
	private String fields;

	/**
	 * 应用身份编码
	 */
	private String appHandleCode;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;
}
