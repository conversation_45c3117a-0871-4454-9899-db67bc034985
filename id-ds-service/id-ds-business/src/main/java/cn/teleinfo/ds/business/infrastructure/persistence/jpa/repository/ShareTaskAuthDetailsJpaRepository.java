package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskAuthDetailsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShareTaskAuthDetailsJpaRepository extends BaseRepository<ShareTaskAuthDetailsEntity, Long> {

    List<ShareTaskAuthDetailsEntity> findByShareTaskAuthId(@Param("shareTaskAuthId") Long shareTaskAuthId);
}