package cn.teleinfo.ds.business.domain.service.impl;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.command.CreateShareChannelVersionCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareChannelsApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ListShareChannelApplicationsQuery;
import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsApplicationsDetail;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsVersion;
import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.repository.*;
import cn.teleinfo.ds.business.domain.service.HcsDomainService;
import cn.teleinfo.ds.business.domain.service.PlatformConnectionDomainService;
import cn.teleinfo.ds.business.domain.service.ShareChannelsDomainService;
import cn.teleinfo.ds.business.domain.service.ShareDataSourcesDomainService;
import cn.teleinfo.ds.business.domain.util.SqlGenerator;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ListScriptResultsResponseDTO;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ScriptInfoDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import cn.teleinfo.ds.common.log.util.BusinessLoggerOption;
import cn.teleinfo.ds.common.log.util.BusinessLoggerUtils;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.huaweicloud.sdk.cdm.v1.model.Configs;
import com.huaweicloud.sdk.cdm.v1.model.Input;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigDataSourceView;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ColumnsList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.TablesList;
import cn.teleinfo.ds.common.core.constant.UserConstants;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.core.util.SqlUtils;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Path;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class ShareChannelsDomainServiceImpl implements ShareChannelsDomainService {

	private final ShareChannelRepository shareChannelRepository;

	private final ShareChannelAuthRepository shareChannelAuthRepository;

	private final DataIntegratedRepository dataIntegratedRepository;

	private final ShareChannelApplicationsRepository shareChannelApplicationsRepository;

	private final Snowflake snowflake;

	private final RoleService roleService;

	private final ShareDataSourcesRepository shareDataSourcesRepository;

	private final PlatformConnectionDomainService platformConnectionDomainService;

	private final HcsDomainService hcsDomainService;
	private final ShareDataSourcesDomainService shareDataSourcesDomainService;
	private final BusinessLoggerOption businessLoggerOption;

	@Override
	public PageResponse<ShareChannelsDTO> listShareChannels(ListShareChannelsQuery query) {
		//当前登录人
		Long userId = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(userId);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				query.setUserId(userId);
			}
		}

		/*//更新探测状态
		List<ShareChannel> allShareChannel = shareChannelRepository.findAllShareChannel();
		List<ShareChannel> updateShareChannel = new ArrayList<>();
		//给探测状态赋值
		for (ShareChannel shareChannels : allShareChannel) {
			Integer detectionStatus = getDetectionStatus(shareChannels.getShareChannelId());
			if (!Objects.equals(detectionStatus, shareChannels.getDetectionStatus())) {
				shareChannels.setDetectionStatus(detectionStatus);
				updateShareChannel.add(shareChannels);
			}
		}
		shareChannelRepository.saveAll(updateShareChannel);*/

		return shareChannelRepository.listShareChannels(query, query.getCurrent(), query.getSize());
	}

	@Override
	public ShareChannelsDetailsDTO queryDetailsById(String id) {
		ShareChannelsDetailsDTO shareChannelsDetailsDTO = shareChannelRepository.queryDetailsById(id);
		//给探测状态赋值
		shareChannelsDetailsDTO.setDetectionStatus(getDetectionStatus(shareChannelsDetailsDTO.getShareChannelId()));

		return shareChannelsDetailsDTO;
	}

	@Override
	public List<ShareChannelsDetailsDTO> listShareChannelsVersion(String shareChannelId) {
		return shareChannelRepository.listShareChannelsVersion(shareChannelId);
	}

	@Override
	public ShareChannelsVersionSqlView queryVersionSql(String shareChannelId, String version) {
		if (version.startsWith("V")) {
			version = version.substring(1);
		}
		String[] versions = version.split("\\.");
		return shareChannelRepository.queryVersionSql(shareChannelId, versions[0], versions[1]);
	}

	@Override
	public List<ShareChannelsVersion> shareChannelsVersion(String shareChannelId) {
		List<ShareChannelsVersionDTO> shareChannelsVersionDTOList = shareChannelRepository
				.queryShareChannelsVersionDTO(shareChannelId);
		List<ShareChannelsVersionAuthDTO> shareChannelsVersionDTOAuthList = shareChannelAuthRepository
				.queryShareChannelsVersionAuthDTO(shareChannelId);

		// 给审核记录分组
		Map<String, List<ShareChannelsVersionAuthDTO>> groupedByCompositeKey = shareChannelsVersionDTOAuthList.stream()
				.collect(Collectors
						.groupingBy(dto -> dto.getShareChannelId() + "_" + dto.getMainVersion() + "." + dto.getMinorVersion()));

		List<ShareChannelsVersion> shareChannelsVersions = new ArrayList<>();
		// 把共享通道和审核记录拼起来
		for (ShareChannelsVersionDTO shareChannelsVersionDTO : shareChannelsVersionDTOList) {
			// 此共享通道对应的审批记录
			List<ShareChannelsVersionAuthDTO> shareChannelsVersionAuthDTOList = groupedByCompositeKey
					.computeIfAbsent(shareChannelsVersionDTO.getShareChannelId() + "_" + shareChannelsVersionDTO.getMainVersion() + "."
							+ shareChannelsVersionDTO.getMinorVersion(), k -> new ArrayList<>());
			//给探测状态赋值
			shareChannelsVersionDTO.setDetectionStatus(getDetectionStatus(shareChannelsVersionDTO.getShareChannelId(), shareChannelsVersionDTO.getMainVersion(), shareChannelsVersionDTO.getMinorVersion()));

			// 排序
			shareChannelsVersionAuthDTOList.sort((o1, o2) -> o2.getUpdatedTime().compareTo(o1.getUpdatedTime()));
			ShareChannelsVersion shareChannelsVersion = new ShareChannelsVersion(shareChannelsVersionDTO,
					shareChannelsVersionAuthDTOList);
			shareChannelsVersions.add(shareChannelsVersion);
		}

		return shareChannelsVersions;
	}

	@Override
	public PageResponse<ShareChannelsApplicationsView> listShareChannelsApplications(ListShareChannelApplicationsQuery query) {
		return shareChannelRepository.listShareChannelsApplications(query, query.getCurrent(), query.getSize());
	}

	@Override
	public ShareChannelsApplicationsDetail queryShareChannelsApplicationDetails(String applicationId) {
		var shareChannelsApplicationDetails = shareChannelRepository
				.queryShareChannelsApplicationDetails(applicationId);

		List<ShareChannelsVersionAuthDTO> shareChannelsVersionAuthDTOList = shareChannelAuthRepository
				.queryShareChannelsVersionAuthDTO(shareChannelsApplicationDetails.getShareChannelId().toString(),
						shareChannelsApplicationDetails.getMainVersion().toString(),
						shareChannelsApplicationDetails.getMinorVersion().toString());
		shareChannelsVersionAuthDTOList.sort((o1, o2) -> o2.getUpdatedTime().compareTo(o1.getUpdatedTime()));

		ShareChannelsApplicationsDetail shareChannelsApplicationsDetail = new ShareChannelsApplicationsDetail(shareChannelsApplicationDetails, shareChannelsVersionAuthDTOList);
		//给探测状态赋值
		shareChannelsApplicationsDetail.setDetectionStatus(Convert.toStr(getDetectionStatus(shareChannelsApplicationDetails.getShareChannelId(), shareChannelsApplicationDetails.getMainVersion(), shareChannelsApplicationDetails.getMinorVersion())));
		return shareChannelsApplicationsDetail;
	}

	@Override
	@Transactional
	public void createShareChannels() {
		// 1. 查询所有数据通道
		List<DataChannelDomainEntity> dataChannels = dataIntegratedRepository.dataChannels();
		if (dataChannels == null || dataChannels.isEmpty()) {
			return;
		}
		for (DataChannelDomainEntity dataChannel : dataChannels) {
			try {
				Long dataChannelId = dataChannel.getDataChannelId() == null ? null : dataChannel.getDataChannelId();
				if (dataChannelId == null)
					continue;
				if (null == dataChannel.getItemType()) {
					continue;
				}
				// 2. 查询该数据通道下所有共享通道版本
				List<ShareChannel> shareChannels = shareChannelRepository.findShareChannelsByDataChannelId(dataChannelId);
				boolean needCreate = true;
				String resolveSql = dataChannel.getResolveSql();
				String newSql = "";
				if (shareChannels != null && !shareChannels.isEmpty()) {
					// 存在关联的ShareChannel且enable!=1
					for (ShareChannel shareChannel : shareChannels) {
						if (shareChannel.getChannelStatus() == 1) {
							needCreate = false;
							break;
						}
					}
					ShareChannel maxVersionChannel = shareChannelRepository.findMaxVersion(dataChannelId);
					String defaultSql = maxVersionChannel.getDefaultSql();

					if (dataChannel.getItemType() == 1) {
						newSql = SqlUtils.baseToSql(resolveSql);
					} else {
						newSql = SqlUtils.extendToSql(resolveSql);
					}
					if (newSql.equals(defaultSql)) {
						needCreate = false;

					}
				}
				if (needCreate) {
					if (newSql.isEmpty()) {
						if (dataChannel.getItemType() == 1) {
							newSql = SqlUtils.baseToSql(resolveSql);
						} else {
							newSql = SqlUtils.extendToSql(resolveSql);
						}
					}
					// 计算新版本号
					int mainVersion = 1;
					int minorVersion = 0;
					if (shareChannels != null && !shareChannels.isEmpty()) {
						String maxVersion = shareChannels.stream()
								.map(chanel -> chanel.getMainVersion() + "." + chanel.getMinorVersion())
								.max(String::compareTo)
								.orElse("1.0");
						String[] parts = maxVersion.split("\\.");
						try {
							mainVersion = Integer.parseInt(parts[0]);
							minorVersion = Integer.parseInt(parts[1]);
							if (minorVersion < 9) {
								minorVersion++;
							} else {
								mainVersion++;
								minorVersion = 0;
							}
						} catch (Exception e) {
							mainVersion = 1;
						}
					}
					ShareChannel shareChannel = new ShareChannel();
					BeanUtils.copyProperties(dataChannel, shareChannel);
					buildShareChannel(dataChannel, newSql, mainVersion, minorVersion, shareChannel);
				}

			} catch (Exception e) {
				log.error("共享通道生成失败{},失败原因：", dataChannel.getDataChannelId(), e);
			}

		}
	}

	private void buildShareChannel(DataChannelDomainEntity dataChannel, String newSql, int mainVersion, int minorVersion, ShareChannel shareChannel) {
		shareChannel.setId(snowflake.nextId());
		shareChannel.setShareChannelId(dataChannel.getDataChannelId());
		shareChannel.setShareChannelName(dataChannel.getDataChannelName());
		shareChannel.setDefaultSql(newSql);
		shareChannel.setMainVersion(mainVersion);
		shareChannel.setMinorVersion(minorVersion);
		shareChannel.setChannelStatus(0);
		shareChannel.setCreateTime(LocalDateTime.now());
		shareChannel.setUpdateTime(LocalDateTime.now());
		shareChannel.setChangeReason("企业节点数据通道发生变更自动同步!");
		shareChannelRepository.saveShareChannel(shareChannel);
	}

	@Override
	public void createShareChannel(Long shareChannelId) {
		ShareChannel enableChannel = shareChannelRepository.findByShareChannelIdAndChannelStatus(shareChannelId);
		if (Objects.isNull(enableChannel)) {
			throw new CheckedException("通道生效版本不存在");
		}
		DataChannelDomainEntity dataChannel = dataIntegratedRepository.dataChannel(shareChannelId);
		if (Objects.isNull(dataChannel)) {
			throw new CheckedException("数据通道不存在");
		}
		Long dataChannelId = dataChannel.getDataChannelId();
		if (null == dataChannel.getItemType()) {
			throw new CheckedException("通道类型错误");
		}
		String resolveSql = dataChannel.getResolveSql();
		String newSql;
		ShareChannel maxVersionChannel = shareChannelRepository.findMaxVersion(dataChannelId);
		String defaultSql = enableChannel.getDefaultSql();
		if (dataChannel.getItemType() == 1) {
			newSql = SqlUtils.baseToSql(resolveSql);
		} else {
			newSql = SqlUtils.extendToSql(resolveSql);
		}
		if (newSql.equals(defaultSql)) {
			throw new CheckedException("企业节点该通道暂未更新，无法生成最新版本通道！");
		}
		// 计算新版本号
		Integer mainVersion = maxVersionChannel.getMainVersion();
		Integer minorVersion = maxVersionChannel.getMinorVersion();
		if (minorVersion < 9) {
			minorVersion++;
		} else {
			mainVersion++;
			minorVersion = 0;
		}
		ShareChannel shareChannel = new ShareChannel();
		BeanUtils.copyProperties(dataChannel, shareChannel);
		buildShareChannel(dataChannel, newSql, mainVersion, minorVersion, shareChannel);

	}

	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void reviewShareChannels(String applicationId, UpdateShareChannelsApplicationsCommand command) {
		// 审核人
		Long userId = SecurityUtils.getUser().getId();
		// 当前时间
		Timestamp currentTime = new Timestamp(System.currentTimeMillis());

		ShareChannelApplicationsDomainEntity shareChannelApplicationsDomainEntity = shareChannelApplicationsRepository
				.queryById(applicationId);
		shareChannelApplicationsDomainEntity.setChannelStatus(command.getAction());
		shareChannelApplicationsDomainEntity.setAuditRemark(command.getComment());
		shareChannelApplicationsDomainEntity.setAuditUserId(userId);
		shareChannelApplicationsDomainEntity.setUpdatedTime(currentTime);
		shareChannelApplicationsRepository.save(shareChannelApplicationsDomainEntity);

		ShareChannelAuthDomainEntity shareChannelAuthDomainEntity = new ShareChannelAuthDomainEntity();
		shareChannelAuthDomainEntity.setId(snowflake.nextId());
		shareChannelAuthDomainEntity.setShareChannelId(shareChannelApplicationsDomainEntity.getShareChannelId());
		shareChannelAuthDomainEntity.setChannelStatus(shareChannelApplicationsDomainEntity.getChannelStatus());
		shareChannelAuthDomainEntity.setAuditRemark(shareChannelApplicationsDomainEntity.getAuditRemark());
		shareChannelAuthDomainEntity.setAuditUserId(userId);
		shareChannelAuthDomainEntity.setIsDeleted(0);
		shareChannelAuthDomainEntity.setCreatedTime(currentTime);
		shareChannelAuthDomainEntity.setUpdatedTime(currentTime);
		shareChannelAuthDomainEntity.setMainVersion(shareChannelApplicationsDomainEntity.getMainVersion());
		shareChannelAuthDomainEntity.setMinorVersion(shareChannelApplicationsDomainEntity.getMinorVersion());
		shareChannelAuthRepository.save(shareChannelAuthDomainEntity);

		// 如果审批通过 将当前版本生效
		if (shareChannelApplicationsDomainEntity.getChannelStatus() == 3) {
			//查询各个版本的共享通道
			List<ShareChannel> shareChannelList = shareChannelRepository.findAllByShareChannelId(Long.parseLong(shareChannelApplicationsDomainEntity.getShareChannelId()));
			List<ShareChannel> changeList = new ArrayList<>();
			for (ShareChannel shareChannel : shareChannelList) {
				if (shareChannel.getChannelStatus() == 1) {
					shareChannel.setChannelStatus(0);
					shareChannel.setUpdateBy(userId);
					shareChannel.setUpdateTime(LocalDateTime.now());
					changeList.add(shareChannel);
				} else if (Objects.equals(shareChannel.getMainVersion(), shareChannelApplicationsDomainEntity.getMainVersion())
						&& Objects.equals(shareChannel.getMinorVersion(), shareChannelApplicationsDomainEntity.getMinorVersion())) {
					shareChannel.setChannelStatus(1);
					shareChannel.setUpdateBy(userId);
					shareChannel.setUpdateTime(LocalDateTime.now());
					changeList.add(shareChannel);
				}
			}

			shareChannelRepository.saveAll(changeList);
		}
	}

	@Override
	public List<String> queryChannelVersions(String shareChannelId) {
		return shareChannelRepository.queryChannelVersions(shareChannelId);
	}

	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void shareChannelVersionSave(CreateShareChannelVersionCommand command) {
		Long usrId = SecurityUtils.getUser().getId();
		Long shareChannelId = Long.parseLong(command.getShareChannelId());
		String version = command.getVersion();
		if (version.startsWith("V")) {
			version = version.substring(1);
		}
		String[] versions = version.split("\\.");
		ShareChannel shareChannel = shareChannelRepository.findByShareChannelIdAndVersion(shareChannelId,
				Integer.parseInt(versions[0]), Integer.parseInt(versions[1]));

		ShareChannel maxVersion = shareChannelRepository.findMaxVersion(shareChannelId);
		Integer mainVersion = maxVersion.getMainVersion();
		Integer minorVersion = maxVersion.getMinorVersion();
		if (minorVersion < 99) {
			minorVersion++;
		} else {
			mainVersion++;
			minorVersion = 0;
		}

		ShareChannel newShareChannel = new ShareChannel();
		BeanUtils.copyProperties(shareChannel, newShareChannel);
		newShareChannel.setId(snowflake.nextId());
		newShareChannel.setCustomSql(command.getCustomSql());
		newShareChannel.setChangeReason(command.getChangeReason());
		newShareChannel.setMainVersion(mainVersion);
		newShareChannel.setMinorVersion(minorVersion);
		newShareChannel.setChannelStatus(0);
		newShareChannel.setCreateBy(usrId);
		newShareChannel.setCreateTime(LocalDateTime.now());
		newShareChannel.setUpdateTime(null);
		newShareChannel.setUpdateBy(null);
		shareChannelRepository.saveShareChannel(newShareChannel);
	}

	@Override
	public ShareChannel findEnableChannel(Long id) {
		return shareChannelRepository.findByShareChannelIdAndChannelStatus(id);
	}

	@Override
	public ShareChannel findEnableChannelByDataChannelId(Long dataChannelId) {
		return shareChannelRepository.findByDataChannelIdAndChannelStatus(dataChannelId);
	}

	@Override
	public ShareChannel checkShareChannelVersionChange(String shareChannelId, String version) {
		if (version.startsWith("V")) {
			version = version.substring(1);
		}
		String[] versions = version.split("\\.");

		//当前通道
		ShareChannel shareChannel = shareChannelRepository.findByShareChannelIdAndVersion(
				Long.parseLong(shareChannelId), Integer.parseInt(versions[0]), Integer.parseInt(versions[1]));

		ShareChannel enableChannel = shareChannelRepository.findByShareChannelIdAndChannelStatus(Long.parseLong(shareChannelId));

		//查询是否有正在探测的通道
		Integer detectionStatus = getDetectionStatus(shareChannel.getShareChannelId());
		if (detectionStatus != null && detectionStatus == 0) {
			throw new CheckedException("有版本正在探测中，请稍后再试！");
		}
		//探测
		String msg = detectShareChannel(shareChannel.getId());
		//探测结果
		if (!"success".equals(msg)) {
			throw new CheckedException("探测失败！");
		}
		return enableChannel;
	}

	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void shareChannelVersionChange(String shareChannelId, String version, ShareChannel enableChannel) {
		// 当前登录人
		Long usrId = SecurityUtils.getUser().getId();
		// 当前时间
		Timestamp currentTime = new Timestamp(System.currentTimeMillis());

		if (version.startsWith("V")) {
			version = version.substring(1);
		}
		String[] versions = version.split("\\.");

		//当前通道
		ShareChannel shareChannel = shareChannelRepository.findByShareChannelIdAndVersion(
				Long.parseLong(shareChannelId), Integer.parseInt(versions[0]), Integer.parseInt(versions[1]));

		//如果没有生效版本
		if (Objects.isNull(enableChannel)) {
			// 直接生效
			shareChannel.setChannelStatus(1);
			shareChannel.setDetectionStatus(1);
			shareChannel.setUpdateTime(LocalDateTime.now());
			shareChannel.setUpdateBy(usrId);
			shareChannelRepository.saveShareChannel(shareChannel);
		} else {
			shareChannel.setChannelStatus(0);
			shareChannel.setDetectionStatus(1);
			shareChannel.setUpdateTime(LocalDateTime.now());
			shareChannel.setUpdateBy(usrId);
			shareChannelRepository.saveShareChannel(shareChannel);

			// 发起审批
			ShareChannelApplicationsDomainEntity shareChannelApplicationsDomainEntity = shareChannelApplicationsRepository
					.findByShareChannelIdAndVersion(shareChannelId, Integer.parseInt(versions[0]),
							Integer.parseInt(versions[1]));
			if (Objects.isNull(shareChannelApplicationsDomainEntity)) {
				shareChannelApplicationsDomainEntity = new ShareChannelApplicationsDomainEntity();
				shareChannelApplicationsDomainEntity.setId(snowflake.nextId());
				shareChannelApplicationsDomainEntity.setShareChannelId(shareChannelId);
				shareChannelApplicationsDomainEntity.setIsDeleted(0);
				shareChannelApplicationsDomainEntity.setMainVersion(Integer.parseInt(versions[0]));
				shareChannelApplicationsDomainEntity.setMinorVersion(Integer.parseInt(versions[1]));
				shareChannelApplicationsDomainEntity.setCreatedTime(currentTime);
			}
			shareChannelApplicationsDomainEntity.setChannelStatus(1);
			shareChannelApplicationsDomainEntity.setUpdatedTime(currentTime);
			shareChannelApplicationsDomainEntity.setApplyUserId(usrId);
			shareChannelApplicationsDomainEntity.setAuditUserId(null);
			shareChannelApplicationsDomainEntity.setAuditRemark(null);
			shareChannelApplicationsRepository.save(shareChannelApplicationsDomainEntity);
		}
	}

	// 统一异常处理
	private <T> T callWithCatch(Supplier<T> supplier, String errMsg, Logger logger, ShareChannelDetectLog log) {
		try {
			return supplier.get();
		} catch (Exception e) {
			logger.error(errMsg, e);
			log.setExecTest(2);
			shareChannelRepository.updateDetectStatus(log);
			BusinessLoggerUtils.destroy(logger);
			throw new RuntimeException("流程中断", e);
		}
	}

	private void callWithCatch(Runnable runnable, String errMsg, Logger logger, ShareChannelDetectLog log) {
		try {
			runnable.run();
		} catch (Exception e) {
			logger.error(errMsg, e);
			log.setExecTest(2);
			shareChannelRepository.updateDetectStatus(log);
			BusinessLoggerUtils.destroy(logger);
			throw new RuntimeException("流程中断", e);
		}
	}

	// 拆分业务步骤
	private List<Job> getJobs(PlatformConnection connection, String projectId, String clusterId, Logger logger, ShareChannelDetectLog log) {
		return callWithCatch(() -> hcsDomainService.findJobs(connection, projectId, clusterId), "调用hcsDomainService.findJobs失败", logger, log);
	}

	private List<TablesList> getTables(PlatformConnection connection, String projectId, String connectionId, String dbName, String workspace, Logger logger, ShareChannelDetectLog log, String msg) {
		return callWithCatch(() -> hcsDomainService.listTables(connection, projectId, connectionId, dbName, "", workspace), msg, logger, log);
	}

	private ScriptInfoDTO getScriptList(PlatformConnection connection, String projectId, String workspace, Integer limit, Integer offset, Logger logger, ShareChannelDetectLog log, String msg) {
		return callWithCatch(() -> hcsDomainService.scriptList(connection, projectId, workspace, limit, offset), msg, logger, log);
	}

	private List<ColumnsList> getColumns(PlatformConnection connection, String projectId, String connectionId, String workspace, String tableId, Logger logger, ShareChannelDetectLog log, String msg) {
		return callWithCatch(() -> hcsDomainService.listTableColumns(connection, projectId, connectionId, workspace, tableId), msg, logger, log);
	}

	private ScriptInfo getScript(PlatformConnection connection, String projectId, String workspace, String scriptName, Logger logger, ShareChannelDetectLog log, String msg) {
		return callWithCatch(() -> hcsDomainService.findScript(connection, projectId, workspace, scriptName), msg, logger, log);
	}

	private List<ColumnsList> getColumnsStd(PlatformConnection connection, String projectId, String connectionId, String workspace, String tableId, Logger logger, ShareChannelDetectLog log, String msg) {
		return callWithCatch(() -> hcsDomainService.listTableColumns(connection, projectId, connectionId, workspace, tableId), msg, logger, log);
	}

	private void createScript(PlatformConnection connection, String projectId, String scriptName, String scriptContent, String workspace, String dbName, String connName, Logger logger, ShareChannelDetectLog log, String msg) {
		callWithCatch(() -> hcsDomainService.createScript(connection, projectId, scriptName, scriptContent, workspace, dbName, connName), msg, logger, log);
	}

	private void delScript(PlatformConnection connection, String projectId, String scriptName, String workspace, Logger logger, ShareChannelDetectLog log, String msg) {
		callWithCatch(() -> hcsDomainService.deleteScript(connection, projectId, scriptName, workspace), msg, logger, log);
	}


	private String executeScript(PlatformConnection connection, String projectId, String scriptName, String workspace, Logger logger, ShareChannelDetectLog log, String msg) {
		return callWithCatch(() -> hcsDomainService.executeScript(connection, projectId, scriptName, workspace), msg, logger, log);
	}

	private ListScriptResultsResponseDTO getScriptResults(PlatformConnection connection, String projectId, String scriptName, String workspace, String instanceId, Logger logger, ShareChannelDetectLog log, String msg) {
		return callWithCatch(() -> hcsDomainService.listScriptResults(connection, projectId, scriptName, workspace, instanceId), msg, logger, log);
	}

	/**
	 * 初始化时检查并清理已存在的脚本
	 * 确保每次创建新脚本前不存在同名脚本
	 */
	private void checkAndCleanupExistingScripts(PlatformConnection connection, String projectId, String workspace,
												Long shareChannelId, Logger logger, ShareChannelDetectLog log) {
		logger.info("检查并清理已存在的脚本");

		// 检查DDL脚本
		String ddlScriptName = "DDL-" + shareChannelId;
		ScriptInfo ddl = getScript(connection, projectId, workspace, ddlScriptName,
				logger, log, "调用hcsDomainService.findScript()失败");
		if (Objects.nonNull(ddl)) {
			logger.info("删除已存在的DDL脚本: {}", ddl.getName());
			delScript(connection, projectId, ddl.getName(), workspace,
					logger, log, "调用hcsDomainService.deleteScript()失败");
		}

		// 检查INSERT脚本
		String insertScriptName = "DATA-" + shareChannelId;
		ScriptInfo insert = getScript(connection, projectId, workspace, insertScriptName,
				logger, log, "调用hcsDomainService.findScript()失败");
		if (Objects.nonNull(insert)) {
			logger.info("删除已存在的数据脚本: {}", insert.getName());
			delScript(connection, projectId, insert.getName(), workspace,
					logger, log, "调用hcsDomainService.deleteScript()失败");
		}
	}

	/**
	 * 异常时清理创建的脚本
	 * 用于在处理失败时进行资源清理
	 */
	private void cleanupCreatedScripts(PlatformConnection connection, String projectId, String workspace,
									   List<String> scriptNames, Logger logger, ShareChannelDetectLog log) {
		if (!scriptNames.isEmpty()) {
			logger.info("探测失败，开始清理创建的脚本");
			for (String name : scriptNames) {
				logger.info("删除脚本: {}", name);
				delScript(connection, projectId, name, workspace, logger, log,
						"调用hcsDomainService.deleteScript()失败");
			}
		}
	}

	/**
	 * 创建并执行脚本，监控执行状态
	 *
	 * @param scriptName     脚本名称
	 * @param scriptContent  脚本内容
	 * @param connection     连接信息
	 * @param projectId      项目ID
	 * @param workspace      工作空间
	 * @param databaseName   数据库名称
	 * @param connectionName 连接名称
	 * @param scriptType     脚本类型（"DDL"或"INSERT"）
	 * @param logger         日志记录器
	 * @param log            探测日志
	 * @param scriptNames    脚本名称列表（用于失败时清理）
	 * @throws Exception 执行异常
	 */
	private void createExecuteAndMonitorScript(String scriptName, String scriptContent,
											   PlatformConnection connection, String projectId, String workspace,
											   String databaseName, String connectionName, String scriptType,
											   Logger logger, ShareChannelDetectLog log, List<String> scriptNames, String stdDataDayuDataBaseName) throws Exception {

		// 添加到脚本列表，用于失败时清理
		scriptNames.add(scriptName);

		// 创建脚本
		logger.info("创建脚本: {}", scriptName);
		createScript(connection, projectId, scriptName, scriptContent, workspace,
				stdDataDayuDataBaseName, connectionName, logger, log,
				"调用hcsDomainService.createScript(" + scriptName + ")失败");
		logger.info("创建{}脚本成功", scriptName);
		// 执行脚本
		logger.info("执行{}脚本", scriptName);
		String instanceId = executeScript(connection, projectId, scriptName, workspace,
				logger, log, "调用hcsDomainService.executeScript(" + scriptType + ")失败");

		// 轮询执行状态
		logger.info("开始轮询{}脚本执行状态···", scriptName);
		while (true) {
			Thread.sleep(8000);
			ListScriptResultsResponseDTO result = getScriptResults(connection, projectId,
					scriptName, workspace, instanceId, logger, log,
					"调用hcsDomainService.listScriptResults(" + scriptName + ")失败");

			String status = result.getStatus();
			if (status.equals(HcsScriptRunStatus.FINISHED.code())) {
				logger.info("{}脚本执行成功", scriptName);
				if ("INSERT".equals(scriptType)) {
					log.setExecTest(1);
				}
				break;
			} else if (status.equals(HcsScriptRunStatus.FAILED.code())) {
				logger.error("{}脚本执行失败: {}", scriptName, result.getMessage());
				log.setExecTest(2);
				shareChannelRepository.updateDetectStatus(log);
				throw new RuntimeException(scriptType + "脚本执行失败: " + result.getMessage());
			}
		}
	}

	/**
	 * 初始化探测日志
	 */
	private ShareChannelDetectLog initDetectLog(ShareChannel shareChannel, Path logPath) {
		ShareChannelDetectLog log = new ShareChannelDetectLog();
		log.setId(snowflake.nextId());
		log.setLogPath(logPath.toString());
		log.setShareChannelId(shareChannel.getShareChannelId());
		log.setMainVersion(shareChannel.getMainVersion());
		log.setMinorVersion(shareChannel.getMinorVersion());
		log.setConnectTest(1);
		log.setExecTest(0);
		return log;
	}

	/**
	 * 恢复表结构和数据
	 * 主要流程：
	 * 1. 解析SQL获取表名
	 * 2. 验证SQL语法
	 * 3. 获取贴源层表信息
	 * 4. 获取规范层表信息
	 * 5. 生成并执行建表DDL
	 * 6. 生成并执行数据INSERT
	 *
	 * @param channels 需要处理的通道列表
	 */
	@Override
	public String shareChannelConnected(List<ShareChannel> channels) {
		String msg = "";
		for (ShareChannel shareChannel : channels) {
			msg = shareChannelConnected(shareChannel, null);
		}
		return msg;
	}

	@Override
	public String shareChannelConnected4TaskExecute(List<ShareChannel> channels, List<Path> path) {
		String msg = "";
		for (ShareChannel channel : channels) {
			msg = shareChannelConnected(channel, path);
		}
		return msg;
	}


	private String shareChannelConnected(ShareChannel shareChannel, List<Path> paths) {
		// 1. 初始化日志记录器
		Path fullLogPath = BusinessLoggerUtils.fullLogPath(businessLoggerOption.getLogPath(),
				shareChannel.getShareChannelId().toString());
		if (null != paths) {
			paths.add(fullLogPath);
		}
		Logger logger = BusinessLoggerUtils.createLogger(fullLogPath, Level.INFO);
		logger.info("开始探测通道: {}", shareChannel.getShareChannelId());
		shareChannel.setDetectionStatus(0);
		shareChannelRepository.saveShareChannel(shareChannel);
		// 2. 初始化探测日志
		ShareChannelDetectLog shareChannelDetectLog = initDetectLog(shareChannel, fullLogPath);

		try {
			// 3. 处理通道的核心逻辑
			processChannel(shareChannel, logger, shareChannelDetectLog);

			// 4. 更新探测状态
			shareChannelDetectLog.setExecTest(1);
			shareChannelRepository.updateDetectStatus(shareChannelDetectLog);
			shareChannel.setDetectionStatus(1);
			shareChannel.setChannelStatus(1);
			shareChannelRepository.saveShareChannel(shareChannel);
			logger.info("探测成功");
			return "success";
		} catch (Exception e) {
			handleRestoreException(e, logger, shareChannelDetectLog);
			shareChannel.setDetectionStatus(2);
			shareChannelRepository.saveShareChannel(shareChannel);
			return "error";
		} finally {
			// 5. 确保日志资源释放
			BusinessLoggerUtils.destroy(logger);
		}
	}

	private void handleRestoreException(Exception e, Logger logger, ShareChannelDetectLog log) {
		logger.error("处理过程发生异常", e);
		log.setExecTest(2);
		shareChannelRepository.updateDetectStatus(log);
	}

	private void processChannel(ShareChannel shareChannel, Logger logger,
								ShareChannelDetectLog log) throws Exception {
		// 1. 获取基础信息
		String appHandleCode = shareChannel.getAppHandleCode();
		String sql = StringUtils.isEmpty(shareChannel.getCustomSql())
				? shareChannel.getDefaultSql()
				: shareChannel.getCustomSql();
		logger.info("通道SQL: {}", sql);
		logger.info("开始解析SQL语法");
		// 2. 解析SQL获取表名
		Set<String> tables = parseTableNames(sql, logger, log);
		// 3. 验证SQL语法
		validateSqlSyntax(sql, logger, log);
		logger.info("SQL语法验证通过");
		// 4. 获取数据源信息
		logger.info("获取共享源信息");
		ShareDataSourcesDomainEntity dataSourcesDomainEntity =
				shareDataSourcesRepository.findByAppHandleCode(appHandleCode);
		ShareDataSourcesItem dataSourcesItem =
				JSONUtil.toBean(dataSourcesDomainEntity.getItems(), ShareDataSourcesItem.class);

		// 5. 获取连接参数
		PlatformConnection connection = platformConnectionDomainService.findByPlatformType(
				dataSourcesDomainEntity.getPlatformType());
		String projectId = dataSourcesItem.getProjectId();
		logger.info("连接信息-projectId: {}", projectId);
		String clusterId = dataSourcesItem.getClusterId();
		logger.info("连接信息-clusterId: {}", clusterId);
		String connectionId = dataSourcesItem.getStdDataDayuConnId();
		logger.info("连接信息-connectionId: {}", connectionId);
		String workspace = dataSourcesItem.getWorkspace();
		logger.info("连接信息-workspace: {}", workspace);
		String stgDatabaseName = dataSourcesItem.getStgDatabaseName();
		logger.info("连接信息-stgDatabaseName: {}", stgDatabaseName);
		String stdDatabaseName = dataSourcesItem.getStdDataDatabaseName();
		logger.info("连接信息-stdDatabaseName: {}", stdDatabaseName);
		String connectionName = dataSourcesItem.getStdDataDayuConnName();
		logger.info("连接信息-connectionName: {}", connectionName);
		String stdDataDayuDataBaseName = dataSourcesItem.getStdDataDayuDatabaseName();
		logger.info("连接信息-stdDataDayuDataBaseName: {}", stdDataDayuDataBaseName);

		// 6. 初始化映射关系
		TableMappings mappings = new TableMappings();

		// 7. 处理表映射
		processTableMappings(connection, projectId, clusterId, workspace, connectionId, stdDatabaseName,
				tables, mappings, logger, log);

		// 8. 验证表匹配结果
		validateTableMappings(tables, mappings, logger, log);

		// 9. 获取贴源层表结构
		Map<String, List<ColumnsList>> stgColumnsType =
				fetchStgTableStructures(connection, projectId, connectionId,
						workspace, stgDatabaseName, mappings.stg.values(), logger, log);

		// 10. 生成并执行脚本
		executeScripts(connection, projectId, workspace, stdDatabaseName,
				connectionName, tables, mappings,
				stgColumnsType, sql, shareChannel.getShareChannelId(),
				logger, log, stdDataDayuDataBaseName);
	}

	// 辅助类：封装表映射关系
	private static class TableMappings {
		Map<String, String> stg = new HashMap<>(); // 业务表 -> 贴源表
		Map<String, String> std = new HashMap<>(); // 贴源表 -> 规范表
		Map<String, String> stgFromColumnList = new HashMap<>(); // 业务表字段
		Map<String, String> stgToColumnList = new HashMap<>(); // 贴源表字段
		Map<String, String> stdFromColumnList = new HashMap<>(); // 贴源表字段（规范层）
		Map<String, String> stdToColumnList = new HashMap<>(); // 规范表字段
	}

	private Set<String> parseTableNames(String sql, Logger logger, ShareChannelDetectLog log) {
		try {
			logger.info("开始解析SQL获取表名");
			Set<String> tables = SqlUtils.findTableName(sql);
			tables = tables.stream().map(table -> table.replaceAll("`", "")).collect(Collectors.toSet());
			logger.info("获取到的表名: {}", tables);
			return tables;
		} catch (JSQLParserException e) {
			logger.error("SQL解析失败", e);
			log.setGrammarCheck(2);
			shareChannelRepository.saveShareChannelLog(log);
			throw new RuntimeException("SQL解析失败", e);
		}
	}

	private void validateSqlSyntax(String sql, Logger logger, ShareChannelDetectLog log) {
		logger.info("开始验证SQL语法");
		if (!SqlUtils.validateSQL(sql)) {
			logger.error("SQL语法验证失败");
			log.setGrammarCheck(2);
			shareChannelRepository.saveShareChannelLog(log);
			throw new RuntimeException("SQL语法验证失败");
		}
		logger.info("SQL语法验证通过");
		log.setGrammarCheck(1);
		shareChannelRepository.saveShareChannelLog(log);
	}

	private void processTableMappings(PlatformConnection connection, String projectId, String clusterId,
									  String workspace, String connectionId, String stdDatabaseName, Set<String> tables, TableMappings mappings,
									  Logger logger, ShareChannelDetectLog log) {
		// 1. 获取作业
		logger.info("开始获取作业信息");
		List<Job> jobs = getJobs(connection, projectId, clusterId, logger, log);
		logger.info("获取到{}个作业", jobs.size());

		// 2. 解析贴源层表
		parseStgLayerMappings(jobs, tables, mappings, logger);

		// 3. 解析规范层表
		parseStdLayerMappings(jobs, mappings, logger);

		// 4. 处理未匹配的表
		handleUnmatchedTables(connection, projectId, workspace, connectionId, stdDatabaseName, mappings, logger, log);
	}

	private void parseStgLayerMappings(List<Job> jobs, Set<String> tables,
									   TableMappings mappings, Logger logger) {
		logger.info("开始从作业中获取贴源层表信息");
		Set<String> remainingTables = new HashSet<>(tables);
		for (Job job : jobs) {
			for (Configs config : job.getFromConfigValues().getConfigs()) {
				for (Input input : config.getInputs()) {
					if ("fromJobConfig.tableName".equals(input.getName())) {
						if(input.getValue() == null || "".equals(input.getValue())) {
							log.info("jobName={} inputName={} is null.", job.getName(), input.getName());
							continue;
						}

						String tableName = input.getValue().toString();
						if (remainingTables.contains(tableName)) {
							logger.info("获取业务库->贴源层表: {}", tableName);
							// 获取业务表字段
							String columnListValue = getInputValue(job.getFromConfigValues().getConfigs(),
									"fromJobConfig.columnList");
							if (columnListValue != null) {
								mappings.stgFromColumnList.put(tableName, columnListValue);
							}

							// 获取贴源表名和字段
							String stgTable = getInputValue(job.getToConfigValues().getConfigs(),
									"toJobConfig.table");
							if (stgTable != null) {
								logger.info("获取业务库->贴源层表: {}", stgTable);
								mappings.stg.put(tableName, stgTable);
								String stgColumnList = getInputValue(job.getToConfigValues().getConfigs(),
										"toJobConfig.columnList");
								if (stgColumnList != null) {
									mappings.stgToColumnList.put(tableName, stgColumnList);
								}
								remainingTables.remove(tableName);
							}
						}
					}
				}
			}
			if (remainingTables.isEmpty()) {
				break;
			}
		}
	}

	private void parseStdLayerMappings(List<Job> jobs, TableMappings mappings, Logger logger) {
		Set<String> stgTables = new HashSet<>(mappings.stg.values());
		logger.info("开始从规范层获取表信息");
		for (Job job : jobs) {
			for (Configs config : job.getFromConfigValues().getConfigs()) {
				for (Input input : config.getInputs()) {
					if ("fromJobConfig.table".equals(input.getName())) {
						if (input.getValue() == null) {
							log.info("jobName={} inputName={} is null", job.getName(), input.getName());
							continue;
						}

						String tableName = input.getValue().toString();
						if (stgTables.contains(tableName)) {
							logger.info("处理贴源层->范层表: {}", tableName);
							// 获取贴源表字段（规范层）
							String columnListValue = getInputValue(job.getFromConfigValues().getConfigs(),
									"fromJobConfig.columnList");
							if (columnListValue != null) {
								mappings.stdFromColumnList.put(tableName, columnListValue);
							}

							// 获取规范表名和字段
							String stdTable = getInputValue(job.getToConfigValues().getConfigs(),
									"toJobConfig.table");
							if (stdTable != null) {
								logger.info("处理贴源层->范层表: {}", stdTable);
								mappings.std.put(tableName, stdTable);
								String stdColumnList = getInputValue(job.getToConfigValues().getConfigs(),
										"toJobConfig.columnList");
								if (stdColumnList != null) {
									mappings.stdToColumnList.put(stdTable, stdColumnList);
								}
								stgTables.remove(tableName);
							}
						}
					}
				}
			}
			if (stgTables.isEmpty()) {
				break;
			}
		}
	}

	private String getInputValue(List<Configs> configsList, String inputName) {
		for (Configs configs : configsList) {
			for (Input input : configs.getInputs()) {
				if (inputName.equals(input.getName())) {
					return input.getValue().toString();
				}
			}
		}
		return null;
	}

	private void handleUnmatchedTables(PlatformConnection connection, String projectId,
									   String workspace, String connectionId, String stdDatabaseName, TableMappings mappings,
									   Logger logger, ShareChannelDetectLog log) {
		// 查找未匹配的贴源表
		List<String> unmatchedStgTables = new ArrayList<>();
		for (String stgTable : mappings.stg.values()) {
			if (!mappings.std.containsKey(stgTable)) {
				unmatchedStgTables.add(stgTable);
			}
		}

		if (!unmatchedStgTables.isEmpty()) {
			logger.info("开始处理脚本信息");
			resolveUnmatchedTablesFromScripts(connection, projectId, workspace, connectionId, stdDatabaseName,
					unmatchedStgTables, mappings, logger, log);
		}
	}

	private void resolveUnmatchedTablesFromScripts(PlatformConnection connection, String projectId,
												   String workspace, String connectionId, String stdDatabaseName, List<String> unmatchedStgTables,
												   TableMappings mappings, Logger logger,
												   ShareChannelDetectLog log) {
		int limit = 100;
		int offset = 0;
		Set<String> remainingTables = new HashSet<>(unmatchedStgTables);

		while (!remainingTables.isEmpty()) {
			ScriptInfoDTO scriptList = getScriptList(connection, projectId, workspace,
					limit, offset, logger, log,
					"调用hcsDomainService.scriptList()失败");
			if (null == scriptList) {
				logger.info("没有更多脚本可以查询");
				break;
			}
			List<ScriptInfo> scripts = scriptList.getScriptInfo();
			for (ScriptInfo script : scripts) {
				String directory = script.getDirectory();
				// 只探测贴源层转规范层脚本目录
				String scriptPath = "/ODS/scyx/std";
				if (!scriptPath.equals(directory)) {
					continue;
				}
				String hiveSql = script.getContent();
				Iterator<String> it = remainingTables.iterator();
				while (it.hasNext()) {
					String tableName = it.next();
					String stdTable = SqlUtils.findStdTable(tableName, hiveSql);
					if (stdTable != null) {
						logger.debug("找到表{}的对应规范层表{}", tableName, stdTable);
						mappings.std.put(tableName, stdTable);
						String columns = SqlUtils.findStdTableColumns(hiveSql);
						mappings.stdFromColumnList.put(tableName, columns);
						it.remove();
					}
				}
				if (remainingTables.isEmpty()) {
					break;
				}
			}

			offset++;
			if (scriptList.getTotal() != null && offset >= scriptList.getTotal()) {
				logger.info("已遍历所有脚本");
				break;
			}
		}

		// 获取规范层表结构
		if (!remainingTables.isEmpty()) {
			logger.warn("仍有贴源层表{}未匹配的规范层表", remainingTables);
			throw new RuntimeException("无法找到规范层对应表关系");
		}

		// 获取规范层表结构
		List<TablesList> stdTableList = getTables(connection, projectId, connectionId, stdDatabaseName, workspace, logger, log, "调用hcsDomainService.listTables(规范层)失败");
		List<String> stds = new ArrayList<>(mappings.std.values());
		logger.info("获取规范层表结构");
		for (TablesList tablesList : stdTableList) {
			String tableName = tablesList.getTableName();
			Iterator<String> it = stds.iterator();
			while (it.hasNext()) {
				String value = it.next();
				if (value.equals(tableName)) {
					String tableId = tablesList.getTableId();
					List<ColumnsList> columnsLists = getColumns(connection, projectId, connectionId, workspace, tableId, logger, log, "调用hcsDomainService.listTableColumns(规范层)失败");
					String columns = SqlGenerator.getStdColumns(columnsLists);
					mappings.stdToColumnList.put(tableName, columns);
					it.remove();
					break;
				}
			}
			if (stds.isEmpty()) {
				break;
			}
		}
	}

	private void validateTableMappings(Set<String> tables, TableMappings mappings,
									   Logger logger, ShareChannelDetectLog log) {
		if (mappings.stg.size() != tables.size()) {
			logger.error("贴源层表数量不匹配，期望: {}，实际: {}", tables.size(), mappings.stg.size());
			log.setExecTest(2);
			shareChannelRepository.updateDetectStatus(log);
			throw new RuntimeException("贴源层表数量不匹配");
		}

		if (mappings.std.size() != mappings.stg.size()) {
			logger.error("规范层表数量不匹配，贴源层表数: {}，规范层表数: {}",
					mappings.stg.size(), mappings.std.size());
			log.setExecTest(2);
			shareChannelRepository.updateDetectStatus(log);
			throw new RuntimeException("规范层表数量不匹配");
		}
	}

	private Map<String, List<ColumnsList>> fetchStgTableStructures(PlatformConnection connection,
																   String projectId, String connectionId,
																   String workspace, String databaseName,
																   Collection<String> tableNames,
																   Logger logger, ShareChannelDetectLog log)
			throws Exception {
		Map<String, List<ColumnsList>> stgColumnsType = new HashMap<>();
		List<TablesList> stgTableList = getTables(connection, projectId, connectionId,
				databaseName, workspace, logger, log,
				"调用hcsDomainService.listTables(贴源层)失败");

		for (TablesList table : stgTableList) {
			String tableName = table.getTableName();
			if (tableNames.contains(tableName)) {
				logger.debug("获取贴源层表{}的结构", tableName);
				List<ColumnsList> columns = getColumns(connection, projectId, connectionId,
						workspace, table.getTableId(),
						logger, log,
						"调用hcsDomainService.listTableColumns(贴源层)失败");
				stgColumnsType.put(tableName, columns);
			}
		}
		return stgColumnsType;
	}

	private void executeScripts(PlatformConnection connection, String projectId,
								String workspace, String stdDatabaseName,
								String connectionName,
								Set<String> tables,
								TableMappings mappings,
								Map<String, List<ColumnsList>> stgColumnsType,
								String sql, Long shareChannelId,
								Logger logger, ShareChannelDetectLog log, String stdDataDayuDataBaseName) throws Exception {
		var conn = connection.getHcsConnContent();

		// 标识解析
		var connProjectId = conn.getProjectId();
		var connWorkspace = conn.getWorkspace();
		var connStdDatabaseName = conn.getDayuConnectionDatabase();


		// XXX 查询链接名称。应该存储在库中。
		String connConnectionName = null;
		List<ApigDataSourceView> dayuConnections = hcsDomainService.findDayuConnections(connection, connProjectId, connWorkspace, 1, 100);
		if (dayuConnections != null) {
			for (ApigDataSourceView dayuConnection : dayuConnections) {
				if (StrUtil.equals(dayuConnection.getDwId(), conn.getDayuConnection())) {
					connConnectionName = dayuConnection.getDwName();
				}
			}
		}

		if (connConnectionName == null) {
			connConnectionName = "HIVE";
		}
		// XXX


		// 1. 初始化清理已存在的脚本
		checkAndCleanupExistingScripts(connection, connProjectId, connWorkspace,
				shareChannelId, logger, log);

		List<String> scriptNames = new ArrayList<>();


		try {
			// 2. 生成并执行建表DDL
			String ddlScriptName = "DDL-" + shareChannelId;
			logger.info("生成DDL脚本: {}", ddlScriptName);
			String ddlSql = generateDdlSql(tables, mappings, stgColumnsType);
			logger.info("脚本内容: {}", ddlSql);
			createExecuteAndMonitorScript(ddlScriptName, ddlSql, connection, connProjectId,
					connWorkspace, connStdDatabaseName, connConnectionName,
					"DDL", logger, log, scriptNames, connStdDatabaseName);
			// 3. 生成并执行数据插入SQL
			String insertScriptName = "DATA-" + shareChannelId;
			logger.info("生成数据脚本: {}", ddlScriptName);
			String insertSql = generateInsertSql(stdDatabaseName, tables, mappings, sql, connStdDatabaseName);
			logger.info("脚本内容: {}", insertSql);
			createExecuteAndMonitorScript(insertScriptName, insertSql, connection, connProjectId,
					connWorkspace, connStdDatabaseName, connConnectionName,
					"INSERT", logger, log, scriptNames, connStdDatabaseName);
		} catch (Exception e) {
			// 清理脚本
			cleanupCreatedScripts(connection, connProjectId, connWorkspace, scriptNames, logger, log);
			throw e;
		}
	}

	private String generateDdlSql(Set<String> tables, TableMappings mappings,
								  Map<String, List<ColumnsList>> stgColumnsType) {
		StringBuilder ddlBuilder = new StringBuilder();
		for (String table : tables) {
			String stgTable = mappings.stg.get(table);
			String createTableSql = SqlGenerator.generateCreateTableSql(table, stgColumnsType.get(stgTable));
			ddlBuilder.append(createTableSql).append(";");
		}
		return ddlBuilder.toString();
	}

	private String generateInsertSql(String stdDatabaseName, Set<String> tables,
									 TableMappings mappings, String sql, String stdDataDayuDataBaseName) {
		StringBuilder insertBuilder = new StringBuilder();
		for (String table : tables) {
			String stgTable = mappings.stg.get(table);
			String stdTable = mappings.std.get(stgTable);
			String insertSql = SqlGenerator.generateInsertSql(stdDatabaseName, stdTable, table,
					mappings.stgFromColumnList.get(table), mappings.stgToColumnList.get(table),
					mappings.stdFromColumnList.get(stgTable), mappings.stdToColumnList.get(stdTable), stdDataDayuDataBaseName);
			insertBuilder.append(System.lineSeparator()).append(insertSql).append(";");
		}
		insertBuilder.append(System.lineSeparator()).append(System.lineSeparator());
		insertBuilder.append(sql).append(";");
		return insertBuilder.toString();
	}

	@Override
	public ShareChannelDetectLog getChannelLog(Long shareChannelId) {
		ShareChannelDetectLog log = shareChannelRepository.findShareChannelLog(shareChannelId);
		if (null == log) {
			return null;
		}
		String logPath = log.getLogPath();
		String logString = readLog(logPath);
		log.setLog(logString);
		return log;

	}

	@Override
	public String detectShareChannel(Long id) {
		ShareChannel shareChannel = shareChannelRepository.findById(id);
		if (null == shareChannel) {
			return "error";
		}
		List<ShareChannel> shareChannelList = new ArrayList<>();
		shareChannelList.add(shareChannel);
		return this.shareChannelConnected(shareChannelList);

	}

	@Override
	public void detectShareChannels() {
		List<ShareChannel> allShareChannel = shareChannelRepository.findAllShareChannel();
		this.shareChannelConnected(allShareChannel);
	}

	/**
	 * 读取日志文件内容
	 *
	 * @param logPath 日志文件路径
	 * @return 日志文件内容
	 */
	private String readLog(String logPath) {
		if (StringUtils.isBlank(logPath)) {
			throw new RuntimeException("日志路径为空!");
		}

		String normalizedPath;
		try {
			normalizedPath = new File(logPath).getCanonicalPath();
		} catch (IOException e) {
			throw new RuntimeException("无法解析日志路径: " + logPath, e);
		}

		StringBuilder sb = new StringBuilder();
		try (BufferedReader br = new BufferedReader(new FileReader(normalizedPath))) {
			String line;
			while ((line = br.readLine()) != null) {
				sb.append(line).append('\n');
			}
		} catch (IOException e) {
			throw new RuntimeException("读取日志文件失败: " + normalizedPath, e);
		}

		return sb.toString();
	}

	private Integer getDetectionStatus(Long shareChannelId) {
		ShareChannelDetectLog shareChannelDetectLog = shareChannelRepository.findShareChannelLog(shareChannelId);
		if (null == shareChannelDetectLog) {
			return null;
		}
		//如果三个标识都是成功
		if (shareChannelDetectLog.getConnectTest() != null && shareChannelDetectLog.getConnectTest() == 1
				&& shareChannelDetectLog.getGrammarCheck() != null && shareChannelDetectLog.getGrammarCheck() == 1
				&& shareChannelDetectLog.getExecTest() != null && shareChannelDetectLog.getExecTest() == 1) {
			return 1;
		} else if (shareChannelDetectLog.getConnectTest() != null && shareChannelDetectLog.getConnectTest() == 2
				|| shareChannelDetectLog.getGrammarCheck() != null && shareChannelDetectLog.getGrammarCheck() == 2
				|| shareChannelDetectLog.getExecTest() != null && shareChannelDetectLog.getExecTest() == 2) {
			return 2;
		} else {
			return 0;
		}
	}

	private Integer getDetectionStatus(Long shareChannelId, Integer mainVersion, Integer minorVersion) {
		ShareChannelDetectLog shareChannelDetectLog = shareChannelRepository.findShareChannelLog(shareChannelId, mainVersion, minorVersion);
		if (null == shareChannelDetectLog) {
			return null;
		}

		//如果三个标识都是成功
		if (shareChannelDetectLog.getConnectTest() != null && shareChannelDetectLog.getConnectTest() == 1
				&& shareChannelDetectLog.getGrammarCheck() != null && shareChannelDetectLog.getGrammarCheck() == 1
				&& shareChannelDetectLog.getExecTest() != null && shareChannelDetectLog.getExecTest() == 1) {
			return 1;
		} else if (shareChannelDetectLog.getConnectTest() != null && shareChannelDetectLog.getConnectTest() == 2
				|| shareChannelDetectLog.getGrammarCheck() != null && shareChannelDetectLog.getGrammarCheck() == 2
				|| shareChannelDetectLog.getExecTest() != null && shareChannelDetectLog.getExecTest() == 2) {
			return 2;
		} else {
			return 0;
		}
	}

}
