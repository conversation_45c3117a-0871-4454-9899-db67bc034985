package cn.teleinfo.ds.business.interfaces.dto.request;

import cn.teleinfo.ds.common.core.util.PageRequest;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ListShareTaskAuthsRequest extends PageRequest {


	private String taskNo;

	private String taskName;

	private String appHandleCode;

	/**
	 * 授权状态
	 */
	private Integer authStatus;

	/**
	 * 授权人
	 */
	private String auditUserName;

	/**
	 * 开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	private LocalDateTime endTime;

}
