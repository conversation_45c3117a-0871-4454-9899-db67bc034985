package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskInstanceDomainEntity;
import cn.teleinfo.ds.business.domain.repository.SharedTaskInstanceRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskInstanceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskStatusView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.SharedTaskInstanceJpaRepository;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
@AllArgsConstructor
public class SharedTaskInstanceRepositoryImpl implements SharedTaskInstanceRepository {

	private final SharedTaskInstanceJpaRepository repository;

	@Override
	public PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query, Integer page, Integer size) {

		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		Page<SharedTaskInstanceListView> resultPage = repository.listSharedTaskInstances(
				query.getSharedTaskId(),
				query.getTaskInstanceNo(),
				query.getTaskInstanceName(),
				query.getExecutionType(),
				query.getRunStatus(),
				query.getTaskNo(),
				query.getTaskName(),
				query.getAppHandleCode(),
				query.getUserHandleCode(),
				query.getStartTime(),
				query.getEndTime(),
				pageable);
		return new PageResponse<>(
				resultPage.getContent(),
				resultPage.getTotalElements(),
				(long) size,
				(long) page,
				(long) resultPage.getTotalPages());
	}

	@Override
	public SharedTaskInstanceView getSharedTaskInstanceById(Long instanceId) {
		return repository.getSharedTaskInstanceById(instanceId);
	}

	@Override
	public void deleteById(Long id) {
		repository.deleteById(id);
	}

	@Override
	public Long save(SharedTaskInstanceDomainEntity sharedTaskInstance) {
		SharedTaskInstanceEntity sharedTaskInstanceEntity;
		if (sharedTaskInstance.getId() != null) {
			sharedTaskInstanceEntity = repository.findById(sharedTaskInstance.getId()).orElse(new SharedTaskInstanceEntity());
		}else{
			sharedTaskInstanceEntity = new SharedTaskInstanceEntity();
		}

		BeanUtils.copyProperties(sharedTaskInstance, sharedTaskInstanceEntity);
		repository.save(sharedTaskInstanceEntity);
		return sharedTaskInstanceEntity.getId();
	}

	/**
	 * 更新任务实例信息
	 * 运行时长 共享数据量总量
	 *
	 * @param sharedTaskInstance
	 */
	@Override
	public void updateRuntimeInfo(SharedTaskInstanceDomainEntity sharedTaskInstance) {
		Optional<SharedTaskInstanceEntity> optional = repository.findById(sharedTaskInstance.getId());
		if (optional.isPresent()) {
			SharedTaskInstanceEntity sharedTaskInstanceEntity = optional.get();
			sharedTaskInstanceEntity.setRunStatus(sharedTaskInstance.getRunStatus());
			sharedTaskInstanceEntity.setRunDuration(sharedTaskInstance.getRunDuration());
			sharedTaskInstanceEntity.setSharedDataCount(sharedTaskInstance.getSharedDataCount());
			repository.save(sharedTaskInstanceEntity);
		}
	}

	@Override
	public List<SharedTaskStatusView> getSharedTaskStatus(Long id, String type) {
		if ("test".equals(type)) {
			return repository.getSharedTaskTestStatus(id);
		} else if ("execute".equals(type)) {
			return repository.getSharedTaskExecuteStatus(id);
		} else {
			return new ArrayList<>();
		}
	}
}
