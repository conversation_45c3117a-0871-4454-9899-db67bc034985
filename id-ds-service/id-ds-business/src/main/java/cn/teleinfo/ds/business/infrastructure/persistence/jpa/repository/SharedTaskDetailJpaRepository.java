package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskShareDataValueDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskDetailEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskView;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SharedTaskDetailJpaRepository extends BaseRepository<SharedTaskDetailEntity, Long> {
	List<SharedTaskDetailEntity> findBySharedTaskId(Long id);

	@Query(nativeQuery = true,
			value = "select distinct " +
					"b.id as id, " +
					"b.name as name, " +
					"b.handle as handle, " +
					"c.org_name as provinceName, " +
					"d.org_name as entName, " +
					"e.app_name as appName, " +
					"a.fields as fields " +
					"from t_shared_task_detail a " +
					"left join t_handle b on a.handle = b.handle and b.is_deleted = 0 " +
					"left join t_province_prefix c on b.province_prefix = c.province_prefix " +
					"left join t_ent_prefix d on b.ent_prefix = d.ent_prefix " +
					"left join t_app_info e on b.app_handle_code = e.handle_code and e.is_deleted = 0 " +
					"where a.id = :id and a.is_deleted = 0 "
	)
	SharedTaskView findSharedTaskViewById(@Param("id") Long id);

	@Query(nativeQuery = true,
			value = "select distinct " +
					"a.id as id, " +
					"b.name as handleName, " +
					"b.handle as handle, " +
					"c.org_name as provinceName, " +
					"d.org_name as entName, " +
					"e.app_name as appName " +
					"from t_shared_task_detail a " +
					"left join t_handle b on a.handle = b.handle and b.is_deleted = 0 " +
					"left join t_province_prefix c on b.province_prefix = c.province_prefix " +
					"left join t_ent_prefix d on b.ent_prefix = d.ent_prefix " +
					"left join t_app_info e on b.app_handle_code = e.handle_code and e.is_deleted = 0 " +
					"where a.shared_task_id = :id and a.is_deleted = 0 "
	)
	List<SharedTaskShareDataValueDTO> getSharedTaskShareDataValueDTO(@Param("id") Long id);
}