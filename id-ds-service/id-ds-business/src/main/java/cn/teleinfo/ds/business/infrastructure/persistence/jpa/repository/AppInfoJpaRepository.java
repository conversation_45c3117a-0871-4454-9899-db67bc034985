package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.AppInfoEntDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.AppInfoEntity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.AppInfoEntView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface AppInfoJpaRepository extends BaseRepository<AppInfoEntity, Long> {


	List<AppInfoEntity> findByEntPrefix(String entPrefix);

	@Query(nativeQuery = true,
			value = "SELECT \n" +
					"ep.id,\n" +
					"    ai.ent_prefix as entPrefix, \n" +
					"    ep.org_name as entName,\n" +
					"    ep.province_prefix as provincePrefix\n" +
					"FROM t_app_info ai\n" +
					"left join t_ent_prefix ep on ep.ent_prefix = ai.ent_prefix \n" +
					"left join sys_user_app ua on ua.user_id = :userId " +
					"WHERE ai.ent_prefix IS NOT null\n" +
					"and is_deleted = 0\n" +
					"and IF(:userId is not null, ai.id = ua.app_id, 1=1 ) " +
					"GROUP BY ai.ent_prefix"
	)
	List<AppInfoEntDTO> findEntPrefix(@Param("userId") Long userId);

	AppInfoEntity findByHandleCode(String appHandleCode);

	@Query(nativeQuery = true,
			value = "SELECT " +
					"a.id as id, " +
					"a.app_name as appName, " +
					"b.org_name as entName, " +
					"SUBSTRING_INDEX(a.handle_code, '/', 1) as handlePrefix, " +
					"a.handle_code as handleCode, " +
					"a.deploy_address as deployAddress, " +
					"a.sys_version as sysVersion, " +
					"a.update_time as updatedTime " +
					"FROM t_app_info a " +
					"LEFT JOIN t_ent_prefix b ON a.ent_prefix = b.ent_prefix " +
					"LEFT JOIN sys_user_app c ON c.user_id = :userId " +
					"WHERE a.is_deleted = 0 " +
					"AND IF(:appName != '' AND :appName is not null, a.app_name like CONCAT('%',:appName,'%'), 1=1 ) " +
					"AND IF(:handleCode != '' AND :handleCode is not null, a.handle_code like CONCAT('%',:handleCode,'%'), 1=1 ) " +
					"AND IF(:userId != '' AND :userId is not null, c.app_id = a.id, 1=1 )" +
					"order by a.update_time desc ",
			countQuery = "SELECT count(1) " +
					"FROM t_app_info a " +
					"LEFT JOIN t_ent_prefix b ON a.ent_prefix = b.ent_prefix " +
					"LEFT JOIN sys_user_app c ON c.user_id = :userId " +
					"WHERE a.is_deleted = 0 " +
					"AND IF(:appName != '' AND :appName is not null, a.app_name like CONCAT('%',:appName,'%'), 1=1 ) " +
					"AND IF(:handleCode != '' AND :handleCode is not null, a.handle_code like CONCAT('%',:handleCode,'%'), 1=1 ) " +
					"AND IF(:userId != '' AND :userId is not null, c.app_id = a.id, 1=1 )"
	)
	Page<HandleSignAppInfoDTO> listHandleSignAppInfo(@Param("appName") String appName,
													 @Param("handleCode") String handleCode,
													 @Param("userId") Long userId,
													 Pageable pageable);

	@Query(nativeQuery = true,
			value = "SELECT " +
					"a.id as id, " +
					"a.app_name as appName, " +
					"b.org_name as entName, " +
					"SUBSTRING_INDEX(a.handle_code, '/', 1) as handlePrefix, " +
					"a.handle_code as handleCode, " +
					"a.deploy_address as deployAddress, " +
					"a.sys_version as sysVersion, " +
					"a.update_time as updatedTime " +
					"FROM t_app_info a " +
					"LEFT JOIN t_ent_prefix b ON a.ent_prefix = b.ent_prefix " +
					"WHERE a.is_deleted = 0 AND a.id = :id ")
	HandleSignAppInfoDTO queryHandleSignAppInfoDetail(@Param("id") String id);

	@Query(nativeQuery = true,value = """
			select * from t_app_info
			""")
	List<AppInfoEntity> findAllApp();

	@Query(nativeQuery = true,
			value = "SELECT " +
					"a.handle_code as handleCode " +
					"FROM t_app_info a " +
					"LEFT JOIN sys_user_app b ON a.id = b.app_id " +
					"WHERE a.is_deleted = 0 AND b.user_id = :id ")
	List<String> findHandleCodeByUserId(Long id);
}