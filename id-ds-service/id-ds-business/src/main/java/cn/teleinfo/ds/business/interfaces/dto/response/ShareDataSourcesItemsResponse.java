package cn.teleinfo.ds.business.interfaces.dto.response;

import cn.teleinfo.ds.business.interfaces.dto.request.ShareDataSourcesItemsDetailsRequest;
import lombok.Data;

@Data
public class ShareDataSourcesItemsResponse {

	/**
	 * app
	 */
	private ShareDataSourcesItemsDetailsRequest app;

	/**
	 * 资源空间
	 */
	private ShareDataSourcesItemsDetailsRequest project;

	/**
	 * 数据治理中心实例
	 */
	private ShareDataSourcesItemsDetailsRequest instance;

	/**
	 * 工作空间
	 */
	private ShareDataSourcesItemsDetailsRequest workspace;

	/**
	 * 数据集成-集群名称
	 */
	private ShareDataSourcesItemsDetailsRequest cluster;

	/**
	 * 数据集成-贴源层连接名称
	 */
	private ShareDataSourcesItemsDetailsRequest stgConn;

	/**
	 * 数据集成-贴源层数据库名称
	 */
	private ShareDataSourcesItemsDetailsRequest stgDatabase;

	/**
	 * 数据集成-规范层连接名称
	 */
	private ShareDataSourcesItemsDetailsRequest stdDataConn;

	/**
	 * 数据集成-规范层数据库名称
	 */
	private ShareDataSourcesItemsDetailsRequest stdDataDatabase;

	/**
	 * 数据开发-规范层连接名称
	 */
	private ShareDataSourcesItemsDetailsRequest stdDataDayuConn;

	/**
	 * 数据开发-规范层数据库名称
	 */
	private ShareDataSourcesItemsDetailsRequest stdDataDayuDatabase;
}
