package cn.teleinfo.ds.business.interfaces.dto.response;

import jakarta.persistence.Column;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ListHandlesResponse {
	private Long id;

	/**
	 * 标识名称
	 */
	private String name;


	/**
	 * 标识
	 */
	private String handle;


	/**
	 * 实体类型 1业务实体 2资源实体
	 */
	private Integer entityType;

	/**
	 * 应用标识身份
	 */
	private String appHandleCode;

	private LocalDateTime createTime;
	private LocalDateTime updateTime;
}
