package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.repository.DataIntegratedRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.to.DataChannelTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.*;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.*;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class DataIntegratedRepositoryImpl implements DataIntegratedRepository {

	private final AppInfoJpaRepository appInfoJpaRepository;
	private final DataChannelJpaRepository dataChannelJpaRepository;
	private final UserJpaRepository userJpaRepository;
	private final HandleJpaRepository handleJpaRepository;
	private final HandleItemJpaRepository handleItemJpaRepository;
	private final HandleReferenceJpaRepository handleReferenceJpaRepository;

//	@Override
//	public List<UserDomainEntity> userFindAll() {
//		List<UserDomainEntity> userDomainEntityList = new ArrayList<>();
//		List<UserEntity> users = userJpaRepository.findAll();
//		for (UserEntity user : users) {
//			UserDomainEntity userDomainEntity = new UserDomainEntity();
//			BeanUtils.copyProperties(user, userDomainEntity);
//			userDomainEntityList.add(userDomainEntity);
//		}
//		return userDomainEntityList;
//	}

//	@Override
//	public void userSaveAll(List<UserDomainEntity> users) {
//		List<UserEntity> userEntities = users.stream()
//				.map(user -> {
//					UserEntity entity = new UserEntity();
//					BeanUtils.copyProperties(user, entity);
//					return entity;
//				})
//				.collect(Collectors.toList());
//		userJpaRepository.saveAll(userEntities);
//	}

//	@Override
//	public void userDeleteAll(List<UserDomainEntity> users) {
//		List<UserEntity> userEntities = users.stream()
//				.map(user -> {
//					UserEntity entity = new UserEntity();
//					BeanUtils.copyProperties(user, entity);
//					return entity;
//				})
//				.collect(Collectors.toList());
//		userJpaRepository.deleteAll(userEntities);
//	}

	@Override
	public List<DataChannelDomainEntity> dataChannelFindAll() {
		List<DataChannelDomainEntity> channelDomainEntities = new ArrayList<>();
		List<DataChannelEntity> channels = dataChannelJpaRepository.findDataChannelEntity();
		for (DataChannelEntity channel : channels) {
			DataChannelDomainEntity channelDomainEntity = new DataChannelDomainEntity();
			BeanUtils.copyProperties(channel, channelDomainEntity);
			channelDomainEntities.add(channelDomainEntity);
		}
		return channelDomainEntities;
	}

	@Override
	public void dataChannelSaveAll(List<DataChannelDomainEntity> channels) {
		List<DataChannelEntity> channelEntities = channels.stream()
				.map(channel -> {
					DataChannelEntity entity = new DataChannelEntity();
					BeanUtils.copyProperties(channel, entity);
					return entity;
				})
				.collect(Collectors.toList());
		dataChannelJpaRepository.saveAll(channelEntities);
	}

	@Override
	public void dataChannelDeleteAll(List<DataChannelDomainEntity> channels) {
		List<DataChannelEntity> channelEntities = channels.stream()
				.map(channel -> {
					DataChannelEntity entity = new DataChannelEntity();
					BeanUtils.copyProperties(channel, entity);
					return entity;
				})
				.collect(Collectors.toList());
		dataChannelJpaRepository.deleteAll(channelEntities);
	}

	@Override
	public List<AppInfoDomainEntity> appInfoFindAll() {
		List<AppInfoDomainEntity> appInfoDomainEntities = new ArrayList<>();
		List<AppInfoEntity> appInfos = appInfoJpaRepository.findAllApp();
		for (AppInfoEntity appInfo : appInfos) {
			AppInfoDomainEntity appInfoDomainEntity = new AppInfoDomainEntity();
			BeanUtils.copyProperties(appInfo, appInfoDomainEntity);
			appInfoDomainEntities.add(appInfoDomainEntity);
		}
		return appInfoDomainEntities;
	}

	@Override
	public void appInfoDeleteAll(List<AppInfoDomainEntity> apps) {
		List<AppInfoEntity> appEntities = apps.stream()
				.map(app -> {
					AppInfoEntity entity = new AppInfoEntity();
					BeanUtils.copyProperties(app, entity);
					return entity;
				})
				.collect(Collectors.toList());
		appInfoJpaRepository.deleteAll(appEntities);
	}

	@Override
	public void appInfoSaveAll(List<AppInfoDomainEntity> apps) {
		List<AppInfoEntity> appEntities = apps.stream()
				.map(app -> {
					AppInfoEntity entity = new AppInfoEntity();
					BeanUtils.copyProperties(app, entity);
					return entity;
				})
				.collect(Collectors.toList());
		appInfoJpaRepository.saveAll(appEntities);
	}

	@Override
	public LocalDateTime handleFindMaxUpdatedTime() {
		return handleJpaRepository.findMaxUpdatedTime();
	}

	@Override
	public List<HandleDomainEntity> handleFindAllById(List<Long> ids) {
		List<HandleDomainEntity> handleDomainEntities = new ArrayList<>();
		List<HandleEntity> handles = handleJpaRepository.findAllByIds(ids);
		for (HandleEntity handle : handles) {
			HandleDomainEntity handleDomainEntity = new HandleDomainEntity();
			BeanUtils.copyProperties(handle, handleDomainEntity);
			handleDomainEntities.add(handleDomainEntity);
		}
		return handleDomainEntities;
	}

	@Override
	public List<HandleItemDomainEntity> handleItemFindAllById(List<Long> ids) {
		List<HandleItemDomainEntity> handleItemDomainEntities = new ArrayList<>();
		List<HandleItemEntity> handleItems = handleItemJpaRepository.findAllByIds(ids);
		for (HandleItemEntity handleItem : handleItems) {
			HandleItemDomainEntity handleItemDomainEntity = new HandleItemDomainEntity();
			BeanUtils.copyProperties(handleItem, handleItemDomainEntity);
			handleItemDomainEntities.add(handleItemDomainEntity);
		}
		return handleItemDomainEntities;
	}

	@Override
	public List<HandleReferenceDomainEntity> handleReferenceFindAllById(List<Long> ids) {
		List<HandleReferenceDomainEntity> handleReferenceDomainEntities = new ArrayList<>();
		List<HandleReferenceEntity> handleReferences = handleReferenceJpaRepository.findAllByIds(ids);
		for (HandleReferenceEntity handleReference : handleReferences) {
			HandleReferenceDomainEntity handleReferenceDomainEntity = new HandleReferenceDomainEntity();
			BeanUtils.copyProperties(handleReference, handleReferenceDomainEntity);
			handleReferenceDomainEntities.add(handleReferenceDomainEntity);
		}
		return handleReferenceDomainEntities;
	}

	@Override
	public void handleSaveAll(List<HandleDomainEntity> handles) {
		List<HandleEntity> handleEntities = handles.stream()
				.map(handle -> {
					HandleEntity entity = new HandleEntity();
					BeanUtils.copyProperties(handle, entity);
					return entity;
				})
				.collect(Collectors.toList());
		handleJpaRepository.saveAll(handleEntities);
	}

	@Override
	public void handleItemSaveAll(List<HandleItemDomainEntity> handleItems) {
		List<HandleItemEntity> handleItemEntities = handleItems.stream()
				.map(handleItem -> {
					HandleItemEntity entity = new HandleItemEntity();
					BeanUtils.copyProperties(handleItem, entity);
					return entity;
				})
				.collect(Collectors.toList());
		handleItemJpaRepository.saveAll(handleItemEntities);
	}

	@Override
	public void handleReferenceSaveAll(List<HandleReferenceDomainEntity> handleReferences) {
		List<HandleReferenceEntity> handleReferenceEntities = handleReferences.stream()
				.map(handleReference -> {
					HandleReferenceEntity entity = new HandleReferenceEntity();
					BeanUtils.copyProperties(handleReference, entity);
					return entity;
				})
				.collect(Collectors.toList());
		handleReferenceJpaRepository.saveAll(handleReferenceEntities);
	}

	@Override
	public List<Long> handleReferenceFindIdBy() {
		return handleReferenceJpaRepository.findAll().stream()
				.map(HandleReferenceEntity::getId)
				.collect(Collectors.toList());
	}

	@Override
	public void handleReferenceDeleteAllById(List<Long> ids) {
		handleReferenceJpaRepository.deleteAllById(ids);
	}

	@Override
	public List<Long> handleItemFindIdBy() {
		return handleItemJpaRepository.findAll().stream()
				.map(HandleItemEntity::getId)
				.collect(Collectors.toList());
	}

	@Override
	public void handleItemDeleteAllById(List<Long> ids) {
		handleItemJpaRepository.deleteAllById(ids);
	}

	@Override
	public List<Long> handleFindIdBy() {
		return handleJpaRepository.findAll().stream()
				.map(HandleEntity::getId)
				.collect(Collectors.toList());
	}

	@Override
	public void handleDeleteAllById(List<Long> ids) {
		handleJpaRepository.deleteAllById(ids);
	}

	@Override
	public List<DataChannelDomainEntity> dataChannels() {
		List<DataChannelDomainEntity> channelDomainEntities = new ArrayList<>();
		List<DataChannelTO> channels = dataChannelJpaRepository.findDataChannels();
		for (DataChannelTO channel : channels) {
			DataChannelDomainEntity channelDomainEntity = new DataChannelDomainEntity();
			BeanUtils.copyProperties(channel, channelDomainEntity);
			channelDomainEntities.add(channelDomainEntity);
		}
		return channelDomainEntities;
	}

	@Override
	public DataChannelDomainEntity dataChannel(Long shareChannelId) {
		DataChannelTO channel = dataChannelJpaRepository.findDataChannel(shareChannelId);
		DataChannelDomainEntity channelDomainEntity = new DataChannelDomainEntity();
		BeanUtils.copyProperties(channel, channelDomainEntity);
		return channelDomainEntity;
	}
}
