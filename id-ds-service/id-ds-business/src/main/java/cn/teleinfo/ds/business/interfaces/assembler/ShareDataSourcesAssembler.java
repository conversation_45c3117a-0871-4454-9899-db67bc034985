package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.command.CreateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.command.CreateShareDataSourcesItemsCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.query.ListShareDataSourcesQuery;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesItem;
import cn.teleinfo.ds.business.interfaces.dto.request.CreateShareDataSourcesRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareDataSourcesRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareDataSourcesItemsDetailsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareDataSourcesItemsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.UpdateShareDataSourcesRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareDataSourcesDetailResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareDataSourcesItemsResponse;
import org.springframework.stereotype.Component;

@Component
public class ShareDataSourcesAssembler {

	public CreateShareDataSourcesCommand toCreateShareDataSourcesCommand(CreateShareDataSourcesRequest request) {
		CreateShareDataSourcesCommand command = new CreateShareDataSourcesCommand();
		command.setPlatformType(request.getPlatformType());

		ShareDataSourcesItemsRequest itemsRequest = request.getItems();

		command.setAppHandleCode(itemsRequest.getApp().getId());

		CreateShareDataSourcesItemsCommand items = new CreateShareDataSourcesItemsCommand();
		items.setAppHandleCode(itemsRequest.getApp().getId());
		items.setAppName(itemsRequest.getApp().getName());
		items.setProjectId(itemsRequest.getProject().getId());
		items.setProjectName(itemsRequest.getProject().getName());
		items.setInstanceId(itemsRequest.getInstance().getId());
		items.setInstanceName(itemsRequest.getInstance().getName());
		items.setWorkspace(itemsRequest.getWorkspace().getId());
		items.setWorkspaceName(itemsRequest.getWorkspace().getName());
		items.setClusterId(itemsRequest.getCluster().getId());
		items.setClusterName(itemsRequest.getCluster().getName());
		items.setStgConnId(itemsRequest.getStgConn().getId());
		items.setStgConnName(itemsRequest.getStgConn().getName());
		items.setStgDatabaseId(itemsRequest.getStgDatabase().getId());
		items.setStgDatabaseName(itemsRequest.getStgDatabase().getName());
		items.setStdDataConnId(itemsRequest.getStdDataConn().getId());
		items.setStdDataConnName(itemsRequest.getStdDataConn().getName());
		items.setStdDataDatabaseId(itemsRequest.getStdDataDatabase().getId());
		items.setStdDataDatabaseName(itemsRequest.getStdDataDatabase().getName());
		items.setStdDataDayuConnId(itemsRequest.getStdDataDayuConn().getId());
		items.setStdDataDayuConnName(itemsRequest.getStdDataDayuConn().getName());
		items.setStdDataDayuDatabaseId(itemsRequest.getStdDataDatabase().getId());
		items.setStdDataDayuDatabaseName(itemsRequest.getStdDataDatabase().getName());
		command.setItems(items);
		return command;
	}

	public ListShareDataSourcesQuery toListShareDataSourcesQuery(ListShareDataSourcesRequest request) {
		return BeanUtil.toBean(request, ListShareDataSourcesQuery.class);
	}

	public ShareDataSourcesDetailResponse toShareDataSourcesDetailResponse(
			ShareDataSourcesDomainEntity shareDataSourcesDetail) {
		ShareDataSourcesDetailResponse response = new ShareDataSourcesDetailResponse();
		response.setId(shareDataSourcesDetail.getId());
		response.setPlatformType(shareDataSourcesDetail.getPlatformType());

		ShareDataSourcesItem shareDataSourcesItem = shareDataSourcesDetail.getShareDataSourcesItem();

		ShareDataSourcesItemsResponse itemsResponse = new ShareDataSourcesItemsResponse();
		itemsResponse.setApp(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getAppHandleCode(), shareDataSourcesItem.getAppName()));
		itemsResponse.setProject(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getProjectId(), shareDataSourcesItem.getProjectName()));
		itemsResponse.setInstance(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getInstanceId(), shareDataSourcesItem.getInstanceName()));
		itemsResponse.setWorkspace(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getWorkspace(), shareDataSourcesItem.getWorkspaceName()));
		itemsResponse.setCluster(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getClusterId(), shareDataSourcesItem.getClusterName()));
		itemsResponse.setStgConn(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getStgConnId(), shareDataSourcesItem.getStgConnName()));
		itemsResponse.setStgDatabase(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getStgDatabaseId(), shareDataSourcesItem.getStgDatabaseName()));
		itemsResponse.setStdDataConn(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getStdDataConnId(), shareDataSourcesItem.getStdDataConnName()));
		itemsResponse.setStdDataDatabase(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getStdDataDatabaseId(), shareDataSourcesItem.getStdDataDatabaseName()));
		itemsResponse.setStdDataDayuConn(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getStdDataDayuConnId(), shareDataSourcesItem.getStdDataDayuConnName()));
		itemsResponse.setStdDataDayuDatabase(new ShareDataSourcesItemsDetailsRequest(shareDataSourcesItem.getStdDataDayuDatabaseId(), shareDataSourcesItem.getStdDataDayuDatabaseName()));
		response.setItems(itemsResponse);
		return response;
	}

	public UpdateShareDataSourcesCommand toUpdateShareDataSourcesCommand(UpdateShareDataSourcesRequest request) {
		UpdateShareDataSourcesCommand command = new UpdateShareDataSourcesCommand();
		command.setId(request.getId());
		command.setPlatformType(request.getPlatformType());
		command.setAppHandleCode(request.getItems().getApp().getId());

		ShareDataSourcesItemsRequest itemsRequest = request.getItems();

		command.setAppHandleCode(itemsRequest.getApp().getId());

		CreateShareDataSourcesItemsCommand items = new CreateShareDataSourcesItemsCommand();
		items.setAppHandleCode(itemsRequest.getApp().getId());
		items.setAppName(itemsRequest.getApp().getName());
		items.setProjectId(itemsRequest.getProject().getId());
		items.setProjectName(itemsRequest.getProject().getName());
		items.setInstanceId(itemsRequest.getInstance().getId());
		items.setInstanceName(itemsRequest.getInstance().getName());
		items.setWorkspace(itemsRequest.getWorkspace().getId());
		items.setWorkspaceName(itemsRequest.getWorkspace().getName());
		items.setClusterId(itemsRequest.getCluster().getId());
		items.setClusterName(itemsRequest.getCluster().getName());
		items.setStgConnId(itemsRequest.getStgConn().getId());
		items.setStgConnName(itemsRequest.getStgConn().getName());
		items.setStgDatabaseId(itemsRequest.getStgDatabase().getId());
		items.setStgDatabaseName(itemsRequest.getStgDatabase().getName());
		items.setStdDataConnId(itemsRequest.getStdDataConn().getId());
		items.setStdDataConnName(itemsRequest.getStdDataConn().getName());
		items.setStdDataDatabaseId(itemsRequest.getStdDataDatabase().getId());
		items.setStdDataDatabaseName(itemsRequest.getStdDataDatabase().getName());
		items.setStdDataDayuConnId(itemsRequest.getStdDataDayuConn().getId());
		items.setStdDataDayuConnName(itemsRequest.getStdDataDayuConn().getName());
		items.setStdDataDayuDatabaseId(itemsRequest.getStdDataDatabase().getId());
		items.setStdDataDayuDatabaseName(itemsRequest.getStdDataDatabase().getName());
		command.setItems(items);

		return command;
	}

}
