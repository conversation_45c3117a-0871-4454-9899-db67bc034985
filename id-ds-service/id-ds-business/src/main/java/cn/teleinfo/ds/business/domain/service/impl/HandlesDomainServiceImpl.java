package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.repository.AppInfoRepository;
import cn.teleinfo.ds.business.domain.repository.DataChannelsRepository;
import cn.teleinfo.ds.business.domain.repository.HandlesRepository;
import cn.teleinfo.ds.business.domain.service.DataChannelsDomainService;
import cn.teleinfo.ds.business.domain.service.HandleGraphDomainService;
import cn.teleinfo.ds.business.domain.service.HandlesDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.DataChannelsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleListView;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleDetailResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleItemDetailResponse;
import cn.teleinfo.ds.common.core.constant.UserConstants;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@AllArgsConstructor
@Service
public class HandlesDomainServiceImpl implements HandlesDomainService {

	private final HandlesRepository handlesRepository;

	private final HandleGraphDomainService handleGraphDomainService;

	private final DataChannelsRepository dataChannelsRepository;

	private final AppInfoRepository appInfoRepository;

	private final RoleService roleService;

	/**
	 * 对象标识列表
	 *
	 * @param handle 聚合根
	 */
	@Override
	public PageResponse<HandleListView> listHandles(Handle handle, Integer page, Integer size, LocalDateTime startTime, LocalDateTime endTime, String userHandleFilter) {
		HandleDomainEntity handleDomainEntity = handle.getHandleDomainEntity();
		List<String> userHandleCodes = null;
		if ("1".equals(userHandleFilter)) {
			Long id = SecurityUtils.getUser().getId();
			R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(id);
			if (ObjectUtil.isNotNull(userList.getData())) {
				RoleCommonVO sysRole = userList.getData().get(0);

				if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
					//获取该用户关联的所有应用
					List<String> handleCodes = appInfoRepository.findHandleCodeByUserId(id);
					if (!handleCodes.isEmpty()) {
						userHandleCodes = handleCodes;
					}
				}
			}
		}

		return handlesRepository.listHandles(handleDomainEntity.getAppHandleCode(),
				handleDomainEntity.getName(), handleDomainEntity.getHandle(), userHandleCodes, page, size, startTime, endTime);
	}

	/**
	 * 查询对象标识
	 *
	 * @param handle 对象标识
	 */
	@Override
	public HandleDomainEntity findByHandle(String handle) {
		HandleDomainEntity h = handlesRepository.findByHandle(handle);
		if (h == null) {
			throw new CheckedException("对象标识不存在或已删除！");
		}
		return h;
	}

	@Override
	public HandleDetailResponse handleDetail(Long id) {
		HandleListView handleDetailById = handlesRepository.findHandleDetailById(id);
		HandleDetailResponse response = new HandleDetailResponse();
		BeanUtil.copyProperties(handleDetailById, response);
		response.setId(handleDetailById.getId());
		response.setHandleName(handleDetailById.getHandleName());
		response.setHandle(handleDetailById.getHandle());
		response.setAppName(handleDetailById.getAppName());
		response.setEntityType(handleDetailById.getEntityType());
		Handle handle = handleGraphDomainService.handleItems(response.getHandle());
		if (ObjectUtil.isNotNull(handle)) {

			List<HandleItemDomainEntity> modelEntities = handle.getHandleItemModelEntities();

			modelEntities.forEach(item -> {
				if (ObjectUtil.isNotNull(item.getDataChannelId())) {
					DataChannelsView channel = dataChannelsRepository.findDataChannelById(item.getDataChannelId());
					if (ObjectUtil.isNotNull(channel)) {
						item.setDataChannelName(channel.getDataChannelName());
					}
				}
			});

			List<HandleItemDomainEntity> isFoundation = modelEntities.stream().filter(item -> item.getFieldSourceType() == 0).toList();
			// 扩展属性
			List<HandleItemDomainEntity> isExtend = modelEntities.stream().filter(item -> item.getFieldSourceType() == 1).toList();
			List<HandleItemDetailResponse> items = isFoundation.stream()
					.map(entity -> {
						HandleItemDetailResponse dto = new HandleItemDetailResponse();
						BeanUtils.copyProperties(entity, dto);
						return dto;
					}).toList();
			List<HandleItemDetailResponse> extendItems = isExtend.stream()
					.map(entity -> {
						HandleItemDetailResponse dto = new HandleItemDetailResponse();
						BeanUtils.copyProperties(entity, dto);
						return dto;
					}).toList();
			response.setItems(items);
			response.setExtendItems(extendItems);
		}
		return response;
	}
}
