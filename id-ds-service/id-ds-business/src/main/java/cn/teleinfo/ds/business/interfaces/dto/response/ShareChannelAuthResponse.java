package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ShareChannelAuthResponse {

	/**
	 * 审核时间
	 */
	private LocalDateTime updatedTime;

	/**
	 * 审核状态
	 */
	private String channelStatus;

	/**
	 * 审核人
	 */
	private String auditUser;

	/**
	 * 失败原因
	 */
	private String auditRemark;

}
