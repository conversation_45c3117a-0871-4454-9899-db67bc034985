package cn.teleinfo.ds.business.infrastructure.persistence.dto.graph;

import lombok.Data;

import java.util.List;

@Data
public class HandleItemDTO {

	/**
	 * 索引
	 */
	private Integer fieldIndex;
	/**
	 * 字段名称
	 */
	private String field;
	/**
	 * 字段描述
	 */
	private String description;
	/**
	 * 字段类型 1固定值 2标识解析数据源 3标识值 4标识-属性
	 */
	private Integer fieldType;
	/**
	 * 关联属性
	 */
	List<HandleReferenceDTO> references;

	/**
	 * 属性值
	 */
	private String fieldValue;

	/**
	 * 备注
	 */
	private String remark;

	private Integer fieldSourceType;
}
