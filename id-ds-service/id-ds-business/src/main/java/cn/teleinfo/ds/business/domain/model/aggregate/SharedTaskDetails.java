package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.valueobject.SharedTaskBasicInfoValue;
import cn.teleinfo.ds.business.domain.model.valueobject.SharedTaskShareDataValue;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskBasicInfoValueDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskShareDataValueDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SharedTaskDetails {
	private SharedTaskBasicInfoValue basicInfo;

	private List<SharedTaskShareDataValue> shareData;

	public SharedTaskDetails (SharedTaskBasicInfoValueDTO sharedTaskBasicInfoValueDTO,
							  List<SharedTaskShareDataValueDTO> sharedTaskShareDataValueDTOList) {
		this.basicInfo = BeanUtil.copyProperties(sharedTaskBasicInfoValueDTO, SharedTaskBasicInfoValue.class);
		this.shareData = BeanUtil.copyToList(sharedTaskShareDataValueDTOList, SharedTaskShareDataValue.class);
	}
}
