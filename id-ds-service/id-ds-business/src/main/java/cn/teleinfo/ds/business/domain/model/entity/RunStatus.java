package cn.teleinfo.ds.business.domain.model.entity;

// 运行状态：1-运行中，2-成功，3-失败，0-未运行
public enum RunStatus {
	NO(0, "未运行"),
	RUN(1, "运行中"),
	SUCCESS(2, "成功"),
	FAIL(3, "失败");


	private final int code;
	private final String message;

	RunStatus(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public int code() {
		return code;
	}

	public String message() {
		return message;
	}
}
