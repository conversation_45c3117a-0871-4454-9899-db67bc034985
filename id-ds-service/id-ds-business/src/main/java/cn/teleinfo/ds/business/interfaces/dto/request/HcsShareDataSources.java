package cn.teleinfo.ds.business.interfaces.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class HcsShareDataSources {

	/**
	 * 资源空间
	 */
	@NotNull(message = "资源空间 不能为空")
	private String project;

	/**
	 * 数据治理中心实例
	 */
	@NotNull(message = "数据治理中心实例 不能为空")
	private String dasInstance;

	/**
	 * 工作空间
	 */
	@NotNull(message = "工作空间 不能为空")
	private String workspace;

	/**
	 * 集群名称
	 */
	@NotNull(message = "集群名称 不能为空")
	private String cdmCluster;

	/**
	 * 业务层到帖源层 数据集成-连接名称
	 */
	@NotNull(message = "业务层到帖源层 数据集成-连接名称 不能为空")
	private String bizCdmConnection;

	/**
	 * 业务层到帖源层 数据集成-数据库名称
	 */
	@NotNull(message = "业务层到帖源层 数据集成-数据库名称 不能为空")
	private String bizCdmConnectionsDatabase;

	/**
	 * 帖源层到规范层 数据集成-连接名称
	 */
	@NotNull(message = "帖源层到规范层 数据集成-连接名称 不能为空")
	private String specCdmConnection;

	/**
	 * 帖源层到规范层 数据集成-数据库名称
	 */
	@NotNull(message = "帖源层到规范层 数据集成-数据库名称 不能为空")
	private String specCdmConnectionsDatabase;

	/**
	 * 帖源层到规范层 数据开发-连接名称
	 */
	@NotNull(message = "帖源层到规范层 数据开发-连接名称 不能为空")
	private String specDayuConnection;

	/**
	 * 帖源层到规范层 数据开发-数据库名称
	 */
	@NotNull(message = "帖源层到规范层 数据开发-数据库名称 不能为空")
	private String specDayuConnectionsDatabase;
}
