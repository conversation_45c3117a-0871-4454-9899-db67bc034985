package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

import java.util.List;

@Data
public class ShareTaskApplicationGraphResponse {

	/**
	 * 共享任务申请 id
	 */
	private Long id;

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务类型（1手动 2定时任务）
	 */
	private Integer taskType;

	/**
	 * 目标源id
	 */
	private Long targetSourceId;
	/**
	 * 目标源名称
	 */
	private String targetSourceName;

	/**
	 * 应用系统编码
	 */
	private String appHandleCode;
	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 企业前缀
	 */
	private String entPrefix;
	/**
	 * 企业名称
	 */
	private String entName;

	/**
	 * 定时任务表达式
	 */
	private String cronExpression;

	/**
	 * 图谱信息
	 */
	private GraphResponse graph;

	/**
	 * 购物车
	 */
	private List<CartResponse> cart;
}
