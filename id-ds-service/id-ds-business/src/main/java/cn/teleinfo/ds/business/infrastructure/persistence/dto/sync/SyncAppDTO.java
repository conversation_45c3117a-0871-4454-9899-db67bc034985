package cn.teleinfo.ds.business.infrastructure.persistence.dto.sync;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SyncAppDTO {
	/**
	 * 应用名称
	 */
	private String appName;
	/**
	 * 类型：1-中台应用,2-非中台应用
	 */
	private Integer appType;
	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;
	/**
	 * 部署地址
	 */
	private String deployAddress;
	/**
	 * 所属企业
	 */
	private String entPrefix;
	/**
	 * 标识编码
	 */
	private String handleCode;
	/**
	 * 主键id
	 */
	private Long id;
	/**
	 * 是否已删除：0否，null是
	 */
	private Integer isDeleted;
	/**
	 * 所属省级
	 */
	private String provincePrefix;
	/**
	 * 应用原始id
	 */
	private Long sourceId;
	/**
	 * 系统版本
	 */
	private String sysVersion;
	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;
}
