package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.query.ShareTaskAuthQuery;
import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareDataDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskAuth;
import cn.teleinfo.ds.business.domain.model.entity.AuthStatus;
import cn.teleinfo.ds.business.domain.model.entity.CreateJobConstant;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDetailDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TaskType;
import cn.teleinfo.ds.business.domain.model.valueobject.AuthInfo;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationsId;
import cn.teleinfo.ds.business.domain.repository.*;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsDomainService;
import cn.teleinfo.ds.business.domain.service.ShareTaskAuthDetailsRepository;
import cn.teleinfo.ds.business.domain.service.ShareTaskAuthDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskAuthEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleItemView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskView;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskAuthsRequest;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import cn.teleinfo.ds.quartz.api.dto.JobSaveReqDTO;
import cn.teleinfo.ds.quartz.api.feign.RemoteJobService;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Service
public class ShareTaskAuthDomainServiceImpl implements ShareTaskAuthDomainService {

	// 共享任务申请记录
	private final ShareTaskApplicationsDomainService shareTaskApplicationsDomainService;
	// 共享任务授权记录
	private final ShareTaskAuthRepository shareTaskAuthRepository;
	private final ShareTaskAuthDetailsRepository shareTaskAuthDetailsRepository;

	// 共享任务表
	private final SharedTaskRepository sharedTaskRepository;
	private final SharedTaskDetailsRepository sharedTaskDetailsRepository;
	private final RemoteJobService remoteJobService;

	//标识属性信息
	private final HandlesRepository handlesRepository;
	private final HandleItemRepository handleItemRepository;

	/**
	 * 共享任务授权
	 *
	 * @param shareTaskAuth 授权聚合根
	 * @param authInfo      审核信息值对象
	 */
	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void review(ShareTaskAuth shareTaskAuth, AuthInfo authInfo) {
		shareTaskAuth.auth(authInfo); // 判断是否可以授权

		// 应用层查询现在的授权的记录。生成聚合根。
		// 判断是否可以授权
		// 可以授权或驳回。开始复制数据。

		// 查询共享申请
		ShareTaskApplicationsDomainEntity shareTaskApplicationsDomainEntity = shareTaskApplicationsDomainService.findShareTaskApplicationsDomainEntity(shareTaskAuth.getApplicationsId());

		// t_share_task_applications 复制 t_share_task_auth。更新 auth_status
		ShareTaskAuthDomainEntity shareTaskAuthDomainEntity = BeanUtil.copyProperties(shareTaskApplicationsDomainEntity, ShareTaskAuthDomainEntity.class);
		shareTaskAuthDomainEntity.setShareTaskApplicationsId(shareTaskAuth.getApplicationsId());
		shareTaskAuthDomainEntity.setAuthStatus(authInfo.authStatus().code());
		shareTaskAuthDomainEntity.setAuditRemark(authInfo.auditRemark());
		shareTaskAuthDomainEntity.setAuditUserId(authInfo.auditUserId());
		shareTaskAuthDomainEntity.setTargetId(shareTaskApplicationsDomainEntity.getTargetSourceId());
		Long shareTaskAuthId = this.shareTaskAuthRepository.save(shareTaskAuthDomainEntity);


		// t_share_task_applications_details 复制 t_share_task_auth_details。
		if (shareTaskApplicationsDomainEntity.getDetailsDomainEntities() != null && !shareTaskApplicationsDomainEntity.getDetailsDomainEntities().isEmpty()) {
			for (ShareTaskApplicationsDetailsDomainEntity detailsDomainEntity : shareTaskApplicationsDomainEntity.getDetailsDomainEntities()) {
				ShareTaskAuthDetailsDomainEntity entity = BeanUtil.copyProperties(detailsDomainEntity, ShareTaskAuthDetailsDomainEntity.class);
				entity.setShareTaskAuthId(shareTaskAuthId);
				shareTaskAuthDetailsRepository.save(entity);
			}
		}

		if (authInfo.authStatus().code() == AuthStatus.PASS.code()) {
			// 授权成功后复制到 t_shared_task
			SharedTaskDomainEntity sharedTaskDomainEntity = new SharedTaskDomainEntity();
			sharedTaskDomainEntity.setAppHandleCode(shareTaskAuthDomainEntity.getAppHandleCode());
			sharedTaskDomainEntity.setEntPrefix(shareTaskAuthDomainEntity.getEntPrefix());
			sharedTaskDomainEntity.setTaskNo(shareTaskAuthDomainEntity.getTaskNo());
			sharedTaskDomainEntity.setTaskName(shareTaskAuthDomainEntity.getTaskName());
			sharedTaskDomainEntity.setTaskType(shareTaskAuthDomainEntity.getTaskType());
			sharedTaskDomainEntity.setCronExpression(shareTaskAuthDomainEntity.getCronExpression());
			sharedTaskDomainEntity.setTargetSourceId(shareTaskAuthDomainEntity.getTargetId());
			sharedTaskDomainEntity.setOperator(authInfo.auditUserId() + "");
			sharedTaskDomainEntity.setTaskStatus(1);

			Long sharedTaskId = sharedTaskRepository.save(sharedTaskDomainEntity);


			if (shareTaskApplicationsDomainEntity.getDetailsDomainEntities() != null && !shareTaskApplicationsDomainEntity.getDetailsDomainEntities().isEmpty()) {
				for (ShareTaskApplicationsDetailsDomainEntity detailsDomainEntity : shareTaskApplicationsDomainEntity.getDetailsDomainEntities()) {
					// 授权成功后复制到  t_shared_task_detail
					SharedTaskDetailDomainEntity sharedTaskDetailDomainEntity = new SharedTaskDetailDomainEntity();
					sharedTaskDetailDomainEntity.setSharedTaskId(sharedTaskId);
					sharedTaskDetailDomainEntity.setHandle(detailsDomainEntity.getHandle());
					sharedTaskDetailDomainEntity.setFields(detailsDomainEntity.getFields());
					sharedTaskDetailDomainEntity.setAppHandleCode(detailsDomainEntity.getAppHandleCode());
					sharedTaskDetailDomainEntity.setEntPrefix(detailsDomainEntity.getEntPrefix());
					sharedTaskDetailDomainEntity.setProvincePrefix(detailsDomainEntity.getProvincePrefix());

					sharedTaskDetailsRepository.save(sharedTaskDetailDomainEntity);
				}
			}

			if (shareTaskApplicationsDomainEntity.getTaskType() == TaskType.SCHEDULED.code()) {
				JobSaveReqDTO job = new JobSaveReqDTO();
				job.setName(shareTaskApplicationsDomainEntity.getTaskName());
				job.setStatus(CreateJobConstant.JOB_STATUS_INIT);
				job.setHandlerName(CreateJobConstant.SHARE_TASK_HANDLER_NAME);
				job.setHandlerParam(sharedTaskId + "");
				job.setCronExpression(sharedTaskDomainEntity.getCronExpression());
				job.setRetryCount(3);                      // TODO 重试次数等没有地方获取
				job.setRetryInterval(1000);
				job.setMonitorTimeout(1000);
				job.setId(sharedTaskId);

				R<Long> result = remoteJobService.createJob(job);
				log.info("result ={}", JSONUtil.toJsonPrettyStr(result));
			}
		}


		// 更新 t_share_task_applications applications_status
		shareTaskApplicationsDomainService.updateApplicationsStatus(shareTaskAuth.getApplicationsId(), authInfo.authStatus());

	}

	/**
	 * 查询当前已经授权的记录
	 *
	 * @param shareTaskApplicationsId 共享任务id
	 * @return 授权记录
	 */
	@Override
	public ShareTaskAuthDomainEntity findShareTaskAuthByPass(Long shareTaskApplicationsId) {
		return shareTaskAuthRepository.findShareTaskAuthByPass(shareTaskApplicationsId);
	}

	/**
	 * 查询授权记录
	 *
	 * @param query   查询条件
	 * @param request 请求参数
	 * @return 授权记录
	 */
	@Override
	public PageResponse<ShareTaskAuthView> listShareTaskAuths(ShareTaskAuthQuery query, ListShareTaskAuthsRequest request) {
		return shareTaskAuthRepository.listShareTaskAuths(query, request.getCurrent(), request.getSize());
	}

	/**
	 * 获取共享任务授权详情
	 *
	 * @param shareTaskAuthId 共享任务授权id
	 * @return 共享任务授权详情
	 */
	@Override
	public ShareTaskAuthDomainEntity getShareTaskAuth(Long shareTaskAuthId) {
		ShareTaskAuthDomainEntity entity = shareTaskAuthRepository.findById(shareTaskAuthId);
		if (entity == null) {
			throw new CheckedException("共享任务授权记录不存在！");
		}

		ShareTaskAuthDomainEntity shareAuth = BeanUtil.copyProperties(entity, ShareTaskAuthDomainEntity.class);

		List<ShareTaskAuthDetailsDomainEntity> details = shareTaskAuthDetailsRepository.findByShareTaskAuthId(shareTaskAuthId);
		shareAuth.setShareTaskAuthDetails(details);
		return shareAuth;
	}

	@Override
	public ShareDataDetails getHandleDetails(Long dataId) {
		ShareTaskAuthDetailsDomainEntity shareTaskAuthDetailsDomainEntity = shareTaskAuthDetailsRepository.findById(dataId);
		if (shareTaskAuthDetailsDomainEntity == null) {
			return new ShareDataDetails();
		}
		String fields = shareTaskAuthDetailsDomainEntity.getFields();
		ObjectMapper mapper = new ObjectMapper();
		String[] array;
		try {
			array = mapper.readValue(fields, String[].class);
		}
		catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
		List<String> list = Arrays.asList(array);

		SharedTaskView sharedTaskView = handlesRepository.getHandleDetails(shareTaskAuthDetailsDomainEntity.getHandle());
		if (sharedTaskView == null) {
			return new ShareDataDetails();
		}
		// 过滤并按属性来源类型分组
		Map<String, List<HandleItemView>> groupedByFieldSourceType = handleItemRepository
				.findAllByHandle(shareTaskAuthDetailsDomainEntity.getHandle())
				.stream()
				.filter(item -> list.contains(item.getField()))
				.collect(Collectors.groupingBy(HandleItemView::getFieldSourceType));

		return new ShareDataDetails(sharedTaskView, groupedByFieldSourceType.get("0"),
				groupedByFieldSourceType.get("1"));
	}
}
