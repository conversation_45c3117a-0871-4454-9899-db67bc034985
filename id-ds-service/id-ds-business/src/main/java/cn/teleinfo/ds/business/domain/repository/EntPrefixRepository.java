package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.EntPrefixDomainEntity;

import java.util.List;

public interface EntPrefixRepository {

	/**
	 * 查询省级下的企业前缀
	 * @param provincePrefix 省级前缀
	 * @return
	 */
	List<EntPrefixDomainEntity> findAllByProvincePrefix(String provincePrefix);


	EntPrefixDomainEntity findByEntPrefix(String entPrefix);


	List<EntPrefixDomainEntity> findAllByListEntPrefix(List<String> entPrefix);

	List<EntPrefixDomainEntity> findAll();
}
