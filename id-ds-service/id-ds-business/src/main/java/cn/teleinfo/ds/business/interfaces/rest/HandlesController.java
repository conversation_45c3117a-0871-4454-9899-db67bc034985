package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.query.ListHandlesQuery;
import cn.teleinfo.ds.business.application.service.HandlesApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleListView;
import cn.teleinfo.ds.business.interfaces.assembler.HandlesAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.ListHandlesRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.*;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

// 标识地图
@RestController
@RequestMapping("/handles")
@AllArgsConstructor
public class HandlesController {

	private final HandlesApplicationService handlesApplicationService;
	private final HandlesAssembler handlesAssembler;

	/**
	 * 对象标识列表
	 */
	@GetMapping
	public R<PageResponse<HandleListView>> listHandles(@Valid ListHandlesRequest request) {
		ListHandlesQuery query = handlesAssembler.toListHandlesQuery(request);
		return R.ok(handlesApplicationService.listHandles(query));
	}

	/**
	 * 对象标识详情
	 */
	@GetMapping("/handle")
	public R<HandInfoResponse> info(@RequestParam("handle") String handle) {
		return R.ok(handlesAssembler.toHandInfoResponse(handlesApplicationService.findByHandle(handle)));
	}


	@GetMapping("/directory")
	public R<List<HandleDirectoryResponse>> directory(){
		return R.ok(handlesAssembler.toHandleDirectoryResponse(handlesApplicationService.directory()));
	}

	@GetMapping("/directory/handle")
	public R<Handle> getHandleItemsByHandle(@RequestParam("handle") String handle) {
		return R.ok(handlesApplicationService.getHandleItemsByHandle(handle));
	}

	@GetMapping("/{id}")
	public R<HandleDetailResponse> handleDetail(@PathVariable Long id) {
		return R.ok(handlesApplicationService.handleDetail(id));
	}
}
