package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.command.ReviewCommand;
import cn.teleinfo.ds.business.application.query.ShareTaskAuthQuery;
import cn.teleinfo.ds.business.application.service.ShareTaskAuthApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareDataDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskAuth;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.EntPrefixDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ProvincePrefixDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.AuthInfo;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationsId;
import cn.teleinfo.ds.business.domain.service.AppInfoDomainService;
import cn.teleinfo.ds.business.domain.service.EntPrefixDomainService;
import cn.teleinfo.ds.business.domain.service.HandlesDomainService;
import cn.teleinfo.ds.business.domain.service.ProvincePrefixDomainService;
import cn.teleinfo.ds.business.domain.service.ShareTaskAuthDomainService;
import cn.teleinfo.ds.business.domain.service.TargetSourceDomainService;
import cn.teleinfo.ds.business.domain.util.CronParseUtil;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskAuthsRequest;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class ShareTaskAuthApplicationServiceImpl implements ShareTaskAuthApplicationService {
	private final ShareTaskAuthDomainService shareTaskAuthDomainService;

	private final TargetSourceDomainService targetSourceDomainService;

	private final AppInfoDomainService appInfoDomainService;

	private final EntPrefixDomainService entPrefixDomainService;

	private final ProvincePrefixDomainService provincePrefixDomainService;

	private final HandlesDomainService handlesDomainService;

	@Override
	public void review(ReviewCommand reviewCommand) {

		// 授权通过记录
		// 查询已经授权的共享任务
		ShareTaskAuthDomainEntity passEntity = shareTaskAuthDomainService.findShareTaskAuthByPass(reviewCommand.getShareTaskApplicationsId());

		// 授权聚合根
		ShareTaskAuth shareTaskAuth = new ShareTaskAuth(passEntity, reviewCommand.getShareTaskApplicationsId());

		// 审核人
		Long id = SecurityUtils.getUser().getId();

		// 当前授权信息
		AuthInfo authInfo = new AuthInfo(id, reviewCommand.getAuditRemark(), reviewCommand.getAuthStatus());

		shareTaskAuthDomainService.review(shareTaskAuth, authInfo);
	}

	/**
	 * 查询授权列表
	 *
	 * @param query   查询参数
	 * @param request 分页参数
	 * @return 分页结果
	 */
	@Override
	public PageResponse<ShareTaskAuthView> listShareTaskAuths(ShareTaskAuthQuery query, ListShareTaskAuthsRequest request) {
		return shareTaskAuthDomainService.listShareTaskAuths(query, request);
	}

	/**
	 * 查询共享任务授权详情
	 *
	 * @param shareTaskAuthId 共享任务授权id
	 * @return 共享任务授权详情
	 */
	@Override
	public ShareTaskAuthDomainEntity getShareTaskAuthDetail(Long shareTaskAuthId) {
		ShareTaskAuthDomainEntity shareTaskAuth = shareTaskAuthDomainService.getShareTaskAuth(shareTaskAuthId);

		TargetSourceDomainEntity targetSource = targetSourceDomainService.findById(shareTaskAuth.getTargetId());
		shareTaskAuth.setTargetSource(targetSource);

		AppInfoDomainEntity appInfo = appInfoDomainService.findByHandleCode(shareTaskAuth.getAppHandleCode());
		shareTaskAuth.setAppInfo(appInfo);

		EntPrefixDomainEntity ent = entPrefixDomainService.findByEntPrefix(shareTaskAuth.getEntPrefix());
		shareTaskAuth.setEnt(ent);

		shareTaskAuth.setCronDetail(CronParseUtil.parseToHumanReadable(shareTaskAuth.getCronExpression()));

		if (shareTaskAuth.getShareTaskAuthDetails() != null) {
			for (ShareTaskAuthDetailsDomainEntity each : shareTaskAuth.getShareTaskAuthDetails()) {
				EntPrefixDomainEntity e = entPrefixDomainService.findByEntPrefix(shareTaskAuth.getEntPrefix());
				if (e != null) {
					each.setEntName(e.getOrgName());
				}

				ProvincePrefixDomainEntity p = provincePrefixDomainService.findByProvincePrefix(each.getProvincePrefix());
				if (p != null) {
					each.setProvinceName(p.getOrgName());
				}

				AppInfoDomainEntity a = appInfoDomainService.findByHandleCode(each.getAppHandleCode());
				if (a != null) {
					each.setAppName(a.getAppName());
				}

				HandleDomainEntity h = handlesDomainService.findByHandle(each.getHandle());
				if (h != null) {
					each.setHandleName(h.getName());
				}
			}
		}

		return shareTaskAuth;
	}

	@Override
	public ShareDataDetails getHandleDetails(Long dataId) {
		return shareTaskAuthDomainService.getHandleDetails(dataId);
	}
}
