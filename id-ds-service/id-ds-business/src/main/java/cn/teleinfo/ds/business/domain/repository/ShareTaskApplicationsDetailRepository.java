package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDetailsDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsDetailDTO;

import java.util.List;

public interface ShareTaskApplicationsDetailRepository {

	List<ShareTaskApplicationsDetailDTO> findShareTaskApplicationsDetailsByTaskId(Long taskId);

	List<ShareTaskApplicationsDetailsDomainEntity> findShareTaskApplicationsDetailsDomainEntities(Long shareTaskApplicationsId);

	ShareTaskApplicationsDetailsDomainEntity findShareTaskApplicationsDetailsById(Long id);
}
