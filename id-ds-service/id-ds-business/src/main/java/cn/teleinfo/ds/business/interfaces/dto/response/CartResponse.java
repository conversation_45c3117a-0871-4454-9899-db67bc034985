package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

import java.util.List;

@Data
public class CartResponse {

	/**
	 * 标识编码
	 */
	private String handle;

	/**
	 * 对象标识名称
	 */
	private String name;

	/**
	 * 实体类型 1业务实体 2资源实体
	 */
	private Integer entityType;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 企业名称
	 */
	private String entName;

	/**
	 * app handle code
	 */
	private String appHandleCode;

	/**
	 * 应用名称
	 */
	private String appName;


	/**
	 * 标识属性字段
	 */
	private List<CartHandleFieldResponse> fields;

}
