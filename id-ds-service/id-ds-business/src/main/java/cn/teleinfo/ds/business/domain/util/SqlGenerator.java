package cn.teleinfo.ds.business.domain.util;

import com.huaweicloud.sdk.dataartsstudio.v1.model.ColumnsList;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

public class SqlGenerator {

	/**
	 * 生成建表SQL语句
	 *
	 * @param tableName 表名
	 * @param columns   字段列表
	 * @return 建表SQL
	 */
	public static String generateCreateTableSql(String tableName, List<ColumnsList> columns) {

		String format = """
				 ROW FORMAT SERDE 'org.apache.hadoop.hive.ql.io.orc.OrcSerde'
				 STORED AS INPUTFORMAT 'org.apache.hadoop.hive.ql.io.orc.OrcInputFormat'
				       OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.orc.OrcOutputFormat'
				""";
		// 按 seqNumber 排序
		columns.sort(Comparator.comparingInt(c -> c.getSeqNumber() == null ? 0 : c.getSeqNumber()));

		List<String> pkList = new ArrayList<>();
		List<String> columnDefs = new ArrayList<>();

		for (ColumnsList col : columns) {
			StringBuilder sb = new StringBuilder();
			sb.append("`").append(col.getColumnName()).append("` ")
					.append(col.getColumnType());

			if (col.getComment() != null && !col.getComment().isEmpty()) {
				sb.append(" COMMENT '").append(col.getComment().replace("'", "''")).append("'");
			}

			columnDefs.add(sb.toString());

			if (Boolean.TRUE.equals(col.getPrimary())) {
				pkList.add("`" + col.getColumnName() + "`");
			}
		}

		StringBuilder sql = new StringBuilder();
		sql.append("DROP TABLE IF EXISTS  ").append(tableName).append(";\n");
		sql.append("CREATE TABLE `").append(tableName).append("` (\n  ");
		sql.append(String.join(",\n  ", columnDefs));
		if (!pkList.isEmpty()) {
			sql.append(",\n  PRIMARY KEY (").append(String.join(", ", pkList)).append(")");
		}
		sql.append("\n)").append(format);

		return sql.toString();
	}

	/**
	 * @param databaseName  数据库名
	 * @param fromTable     来源表
	 * @param toTable       目标表
	 * @param ywColumn      业务字段
	 * @param stgFromColumn 业务字段->帖源表字段
	 * @param stgToColumn   帖源表字段->规范表字段
	 * @param stdColumn     规范表字段
	 * @return
	 */
	public static String generateInsertSql(String databaseName, String fromTable, String toTable, String ywColumn, String stgToColumn, String stgFromColumn, String stdColumn, String stdDataDayuDataBaseName) {
		ywColumn = ywColumn.toLowerCase();
		stgToColumn = stgToColumn.toLowerCase();
		stgFromColumn = stgFromColumn.toLowerCase();
		stdColumn = stdColumn.toLowerCase();
		// columnName 按 & 分割
		String[] yw = ywColumn.split("&");
		String[] stgFrom = stgFromColumn.split("&");
		String[] stgTo = stgToColumn.split("&");
		String[] std = stdColumn.split("&");

		// 1. 创建stgFrom到stgTo的映射
		Map<String, String> stgMap = new HashMap<>();
		for (int i = 0; i < stgTo.length; i++) {
			stgMap.put(yw[i], stgTo[i]);
		}

		// 2. 创建stgTo到std的映射
		Map<String, String> stdMap = new HashMap<>();
		for (int i = 0; i < stgFrom.length; i++) {
			stdMap.put(stgFrom[i], std[i]);
		}

		// 3. 根据yw的顺序，找到对应的std字段
		List<String> mappedColumns = new ArrayList<>();
		for (String ywField : yw) {
			// 通过stgFromToMap找到对应的stgTo字段
			String stgToField = stgMap.get(ywField);
			if (stgToField != null) {
				// 通过stgToStdMap找到对应的std字段
				String stdField = stdMap.get(stgToField);
				if (stdField != null) {
					mappedColumns.add(stdField);
				} else {
					mappedColumns.add("NULL");
				}
			}
		}

		// 4. 将映射后的字段用逗号连接
		String selectColumns = String.join(",", mappedColumns);

		String sql = String.format(
				"INSERT OVERWRITE TABLE %s.%s\nSELECT %s\nFROM %s.%s",
				stdDataDayuDataBaseName, toTable, selectColumns, databaseName, fromTable
		);
		return sql;
	}

	public static void main(String[] args) {

	}

	public static String getStdColumns(List<ColumnsList> columnsLists) {
		StringBuilder sb = new StringBuilder();
		columnsLists.sort(Comparator.comparingInt(ColumnsList::getSeqNumber));
		for (ColumnsList columnsList : columnsLists) {
			sb.append(columnsList.getColumnName()).append("&");
		}
		sb.deleteCharAt(sb.length() - 1);
		return sb.toString();
	}
}