package cn.teleinfo.ds.business.application.query;

import cn.teleinfo.ds.common.core.util.PageRequest;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ListShareDataSourcesQuery extends PageRequest {
	/**
	 * 应用标识
	 */
	private String appHandleCode;

	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 平台类型
	 * 0 华为 1 阿里 2 自建
	 */
	private Integer platformType;

	/**
	 * 连接状态
	 */
	private Integer connState;

	/**
	 * 操作开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 操作结束时间
	 */
	private LocalDateTime endTime;
}
