package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.command.UpdateConnectionSettingCommand;
import cn.teleinfo.ds.business.application.service.PlatformConnectionApplicationService;
import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.service.PlatformConnectionDomainService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;


@Service
@AllArgsConstructor
public class PlatformConnectionApplicationServiceImpl implements PlatformConnectionApplicationService {

	private final PlatformConnectionDomainService platformConnectionDomainService;

	/**
	 * 查询连接设置
	 *
	 * @param platformType 平台类型
	 * @return 连接设置信息
	 */
	@Override
	public PlatformConnectionDomainEntity getConnectionSetting(Integer platformType) {
		return platformConnectionDomainService.findByPlatformType(platformType)
				.getPlatformConnection();
	}

	/**
	 * 更新连接设置
	 *
	 * @param command 连接设置信息
	 */
	@Override
	public void updateConnectionSetting(UpdateConnectionSettingCommand command) {
		platformConnectionDomainService.updateConnectionSetting(command.getPlatformType(), command.getPlatformConnection());
	}
}
