package cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
public class ShareTaskApplicationsDetailDTO {

	private Long id;

	private String handleName;

	private String handle;

	private String provinceName;

	private String entName;

	private String appName;

	private String fields;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	private String appHandleCode;
}
