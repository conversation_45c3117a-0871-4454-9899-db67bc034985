package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskAuthEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import feign.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShareTaskAuthJpaRepository extends BaseRepository<ShareTaskAuthEntity, Long> {

	@Query(nativeQuery = true,
			value = """
					SELECT
					    s.id,
					    s.task_no AS taskNo,
					    s.auth_status AS authStatus,
					    u.name AS auditUserName,
					    s.update_time AS updateTime,
					    s.audit_remark AS auditRemark
					FROM t_share_task_auth s,  sys_user u
					WHERE s.share_task_applications_id = :applicationId
					AND s.update_by = u.user_id
					AND s.is_deleted = 0
					"""
	)
	List<ShareTaskAuthView> findShareTaskAuthsByApplicationId(@Param("applicationId") Long applicationId);


	@Query(nativeQuery = true,
			value = """
					SELECT
					    s.id,
					    s.task_no AS taskNo,
					    s.task_name as taskName,
					    s.task_type as taskType,
					    s.`app_handle_code` as appHandleCode,
					    (select app_name from t_app_info a where a.handle_code = s.`app_handle_code` and a.`is_deleted` = 0) as appName,
					    s.auth_status AS authStatus,
					    s.audit_user_id as auditUserId,
					    u.name AS auditUserName,
					    s.update_time AS updateTime,
					    s.audit_remark AS auditRemark
					FROM t_share_task_auth s
					LEFT JOIN sys_user u ON s.audit_user_id = u.user_id and u.del_flag = 0
					WHERE
					  IF(:appHandleCode != '' AND :appHandleCode is not null, s.app_handle_code = :appHandleCode, 1=1 )
					and IF(:taskNo != '' AND :taskNo is not null, s.task_no like CONCAT('%',:taskNo,'%'), 1=1 )
					and IF(:taskName != '' AND :taskName is not null, s.task_name like CONCAT('%',:taskName,'%'), 1=1 )
					and IF(:authStatus != '' AND :authStatus is not null, s.auth_status = :authStatus, 1=1 )
					and IF(:startTime != '' AND :startTime is not null, s.update_time >= :startTime, 1=1 )
					and IF(:endTime != '' AND :endTime is not null, s.update_time <= :endTime, 1=1 )
					and s.is_deleted = 0
					ORDER BY s.update_time DESC 
					""",
			countQuery = """
						SELECT
					    COUNT(*)
					FROM t_share_task_auth s
					LEFT JOIN sys_user u ON s.audit_user_id = u.user_id and u.del_flag = 0
					WHERE
					 IF(:appHandleCode != '' AND :appHandleCode is not null, s.app_handle_code = :appHandleCode, 1=1 )
					and IF(:taskNo != '' AND :taskNo is not null, s.task_no like CONCAT('%',:taskNo,'%'), 1=1 )
					and IF(:taskName != '' AND :taskName is not null, s.task_name like CONCAT('%',:taskName,'%'), 1=1 )
					and IF(:authStatus != '' AND :authStatus is not null, s.auth_status = :authStatus, 1=1 )
					and IF(:startTime != '' AND :startTime is not null, s.update_time >= :startTime, 1=1 )
					and IF(:endTime != '' AND :endTime is not null, s.update_time <= :endTime, 1=1 )
					and s.is_deleted = 0
					"""
	)
	Page<ShareTaskAuthView> findShareTaskAuths(
			@Param("appHandleCode") String appHandleCode,
			@Param("taskNo") String taskNo,
			@Param("taskName") String taskName,
			@Param("authStatus") Integer authStatus,
			@Param("startTime") LocalDateTime startTime,
			@Param("endTime") LocalDateTime endTime,
			Pageable pageable);

	ShareTaskAuthEntity findFirstByShareTaskApplicationsIdAndAuthStatus(Long applicationId, Integer authStatus);

	@Query(nativeQuery = true,
			value = """
					SELECT
					    a.id AS id,
					    a.task_name AS taskName,
					    a.task_no AS taskNo,
					    a.task_type AS taskType,
					    a.auth_status AS authStatus,
					    a.committed_by AS committedBy,
					    a.source_type AS sourceType,
					    a.province_prefix AS provincePrefix,
					    a.ent_prefix AS entPrefix,
					    a.app_handle_code AS appHandleCode,
					    a.audit_user_id AS auditUserId,
					    a.audit_remark AS auditRemark,
					    a.share_task_applications_id AS shareTaskApplicationsId,
					    a.target_id AS targetId,
					    a.cron_expression AS cronExpression,
					    a.create_time AS createTime,
					    a.update_time AS updateTime,
					    a.create_by AS createBy,
					    a.update_by AS updateBy,
						(select u.name from sys_user u where u.user_id = a.create_by and u.`del_flag` = 0) AS createByName,
						(select u.name from sys_user u where u.user_id = a.update_by and u.`del_flag` = 0) AS updateByName,
						(select u.name from sys_user u where u.user_id = a.audit_user_id and u.`del_flag` = 0) AS auditUserName,
						(select u.name from sys_user u where u.user_id = a.committed_by and u.`del_flag` = 0) AS committedByName
					FROM
					    t_share_task_auth a
					WHERE
					    id = :id
					AND is_deleted = 0
					"""
	)
	ShareTaskAuthView findShareTaskAuthById(@Param("id") Long id);
}