package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.ShareChannelApplicationsDomainEntity;

public interface ShareChannelApplicationsRepository {

	ShareChannelApplicationsDomainEntity queryById(String applicationId);

	void save(ShareChannelApplicationsDomainEntity shareChannelApplicationsDomainEntity);

    ShareChannelApplicationsDomainEntity findByShareChannelIdAndVersion(String shareChannelId, Integer mainVersion, Integer minorVersion);
}
