package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelApplicationsEntity;
import org.springframework.stereotype.Repository;

@Repository
public interface ShareChannelApplicationsJpaRepository extends BaseRepository<ShareChannelApplicationsEntity, Long> {
	ShareChannelApplicationsEntity queryById(long id);

	ShareChannelApplicationsEntity findByShareChannelIdAndMainVersionAndMinorVersion(String shareChannelId, Integer mainVersion, Integer minorVersion);
}