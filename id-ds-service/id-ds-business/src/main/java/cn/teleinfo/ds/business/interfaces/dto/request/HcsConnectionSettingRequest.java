package cn.teleinfo.ds.business.interfaces.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class HcsConnectionSettingRequest {
	/**
	 * IAM_ENDPOINT
	 */
	@NotNull(message = "IAM_ENDPOINT 不能为空")
	private String iamEndpoint;

	/**
	 * CDM_ENDPOINT
	 */
	@NotNull(message = "CDM_ENDPOINT 不能为空")
	private String cdmEndpoint;

	/**
	 * Dgc_ENDPOINT
	 */
	@NotNull(message = "Dgc_ENDPOINT 不能为空")
	private String dgcEndpoint;

	/**
	 * DataArtsStudio_ENDPOINT
	 */
	@NotNull(message = "DataArtsStudio_ENDPOINT 不能为空")
	private String dataArtsStudioEndpoint;

	/**
	 * ak
	 */
	@NotNull(message = "ak 不能为空")
	private String ak;

	/**
	 * sk
	 */
	@NotNull(message = "sk 不能为空")
	private String sk;

	/**
	 * 数据集成-规范层连接名称
	 */
	@NotNull(message = "数据集成-规范层连接名称 不能为空")
	private String cdmConnection;

	/**
	 * 数据集成-规范层数据库名称
	 */
	@NotNull(message = "数据集成-规范层数据库名称 不能为空")
	private String cdmConnectionDatabase;

	/**
	 * 数据开发-规范层连接名称
	 */
	@NotNull(message = "数据开发-规范层连接名称 不能为空")
	private String dayuConnection;

	/**
	 * 数据开发-规范层数据库名称
	 */
	@NotNull(message = "数据开发-规范层数据库名称 不能为空")
	private String dayuConnectionDatabase;
}
