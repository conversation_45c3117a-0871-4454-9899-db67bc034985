package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

import java.util.List;

@Data
public class SharedDataDetailsResponse {
	/**
	 * id
	 */
	private String id;

	/**
	 * 标识名
	 */
	private String name;

	/**
	 * 标识
	 */
	private String handle;

	/**
	 * 省级名称
	 */
	private String provinceName;

	/**
	 * 企业名称
	 */
	private String entName;

	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 基础属性
	 */
	List<SharedDataHandleItemResponse> items;

	/**
	 * 扩展属性
	 */
	List<SharedDataHandleItemResponse> extendItems;

}