package cn.teleinfo.ds.business.domain.model.entity;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserDomainEntity {
	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除: 0 未删除 null 已删除
	 */
	private Integer isDeleted;
	/**
	 * 用户原始id
	 */
	private Long sourceId;

	/**
	 * 应用身份标识
	 */
	private String appHandleCode;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * sys_admin,sys_app_user
	 */
	private String roleCode;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 用户名
	 */
	private String username;

	/**
	 * 昵称
	 */
	private String nickName;

	/**
	 * 密码
	 */
	private String password;

	/**
	 * 地址
	 */
	private String address;

	/**
	 * 电话
	 */
	private String phone;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 1-省；2-企业
	 */
	private Integer sourceType;

	/**
	 * 备注
	 */
	private String remark;
}
