package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

import java.util.List;

@Data
public class HandInfoResponse {
	private Long id;
	/**
	 * 企业前缀
	 */
	private String entPrefix;
	private String entName;

	/**
	 * 通配符
	 */
	private String wildcard;

	/**
	 * 标识名称
	 */
	private String name;

	/**
	 * 标识
	 */
	private String handle;

	/**
	 * 实体类型 1业务实体 2资源实体
	 */
	private Integer entityType;

	/**
	 * 应用标识身份
	 */
	private String appHandleCode;
	private String appName;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;
	private String provinceName;

	/**
	 * 属性
	 */
	private List<HandleItemResponse> items;
	/**
	 * 扩展属性
	 */
	private List<HandleItemResponse> extendItems;

}
