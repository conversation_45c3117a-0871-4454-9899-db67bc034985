package cn.teleinfo.ds.business.infrastructure.external.hcs;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DatabaseClient {

	public void exec(String jdbcUrl, String username, String password, String sql) {
		try (HikariDataSource dataSource = new HikariDataSource()) {
			dataSource.setDriverClassName("org.postgresql.Driver");
			dataSource.setJdbcUrl(jdbcUrl);
			dataSource.setUsername(username);
			dataSource.setPassword(password);

			JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

			jdbcTemplate.execute(sql);
		} catch (DataAccessException e) {
			throw new RuntimeException(e);
		}
	}
}
