package cn.teleinfo.ds.business.infrastructure.persistence.jpa;


import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 实体基类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@MappedSuperclass
@EntityListeners({BaseEntityListeners.class, AuditingEntityListener.class})
public class BaseEntity implements Serializable {

	@Id
	private Long id;

	/**
	 * 创建时间
	 */
	@CreatedDate
	@Column(name = "create_time")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@LastModifiedDate
	@Column(name = "update_time")
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除: 0 未删除 null 已删除
	 */
	@Column(name = "is_deleted")
	private Integer isDeleted = 0;
}

