package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.query.DataChannelsListQuery;
import cn.teleinfo.ds.business.domain.repository.DataChannelsRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.DataChannelEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.DataChannelsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.DataChannelJpaRepository;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@AllArgsConstructor
public class DataChannelsRepositoryImpl implements DataChannelsRepository {

	private final DataChannelJpaRepository repository;
	@Override
	public PageResponse<DataChannelsView> listDataChannels(DataChannelsListQuery query, Integer page, Integer size) {

		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		Page<DataChannelsView> resultPage = repository.findListDataChannels(query.getHandle(),query.getAppHandleCode(), pageable);
		return new PageResponse<>(
				resultPage.getContent(),
				resultPage.getTotalElements(),
				(long) size,
				(long) page,
				(long) resultPage.getTotalPages());
	}

	@Override
	public DataChannelsView findDataChannelById(Long id) {
		Optional<DataChannelEntity> byId = repository.findById(id);
		return byId.map(entity -> BeanUtil.copyProperties(entity, DataChannelsView.class)).orElse(null);
	}
}
