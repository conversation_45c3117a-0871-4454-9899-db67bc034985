package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import cn.teleinfo.ds.business.interfaces.dto.response.AppInfoTreeResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ProvincePrefixResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.util.List;

public interface AppInfoService {

	/**
	 * 应用信息列表查询
	 */
	PageResponse<HandleSignAppInfoDTO> listHandleSignAppInfo(ListAppInfoQuery query);

	/**
	 * 应用信息详情查询
	 */
	HandleSignAppInfoDTO queryHandleSignAppInfoDetail(String id);

	/**
	 * 应用信息树查询
	 */
	List<AppInfoTreeResponse> queryAppInfoTree();

	ProvincePrefixResponse getGlobalConfig();

}
