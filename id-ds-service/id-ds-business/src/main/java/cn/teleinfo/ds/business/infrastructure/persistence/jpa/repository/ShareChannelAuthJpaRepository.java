package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelAuthEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShareChannelAuthJpaRepository extends BaseRepository<ShareChannelAuthEntity, Long> {
	@Query(nativeQuery = true, value = "select " +
			"a.share_channel_id as shareChannelId, " +
			"a.updated_time as updatedTime, " +
			"a.channel_status as channelStatus, " +
			"b.name as auditUser, " +
			"a.audit_remark as auditRemark, " +
			"a.main_version as mainVersion, " +
			"a.minor_version as minorVersion " +
			"from t_share_channel_auth a " +
			"left join sys_user b on a.audit_user_id = b.user_id and b.del_flag = '0' " +
			"where a.share_channel_id = :shareChannelId " +
			"and a.is_deleted = 0 ")
	List<ShareChannelsVersionAuthDTO> queryShareChannelsVersionAuthDTO(@Param("shareChannelId") String shareChannelId);


	@Query(nativeQuery = true, value = "select " +
			"a.share_channel_id as shareChannelId, " +
			"a.updated_time as updatedTime, " +
			"a.channel_status as channelStatus, " +
			"b.name as auditUser, " +
			"a.audit_remark as auditRemark, " +
			"a.main_version as mainVersion, " +
			"a.minor_version as minorVersion " +
			"from t_share_channel_auth a " +
			"left join sys_user b on a.audit_user_id = b.user_id and b.del_flag = '0' " +
			"where a.share_channel_id = :shareChannelId and a.main_version = :main_version and a.minor_version = :minor_version " +
			"and a.is_deleted = 0 ")
	List<ShareChannelsVersionAuthDTO> queryShareChannelsVersionAuthDTO(@Param("shareChannelId") String shareChannelId,
																	   @Param("main_version") String main_version,
																	   @Param("minor_version") String minor_version);
}