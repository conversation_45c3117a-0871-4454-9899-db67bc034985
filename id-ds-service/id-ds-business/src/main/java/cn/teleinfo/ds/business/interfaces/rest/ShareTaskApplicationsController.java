package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.command.ReviewCommand;
import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.application.service.ShareTaskApplicationsAppService;
import cn.teleinfo.ds.business.application.service.ShareTaskAuthApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.interfaces.assembler.HandleItemDetailAssembler;
import cn.teleinfo.ds.business.interfaces.assembler.ShareTaskApplicationsAssemble;
import cn.teleinfo.ds.business.interfaces.assembler.ShareTaskAuthApplicationAssemble;
import cn.teleinfo.ds.business.interfaces.dto.request.HandleItemDetailRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskApplicationsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ReviewRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareTaskApplicationsRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationDetailResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationGraphResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationTargetSourceResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedDataDetailsResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 共享任务申请
 */
@RestController
@RequestMapping("/share-task-applications")
@AllArgsConstructor
public class ShareTaskApplicationsController {

	private final ShareTaskApplicationsAppService service;

	private final ShareTaskApplicationsAssemble assembler;

	private final ShareTaskAuthApplicationService shareTaskAuthApplicationService;
	private final ShareTaskAuthApplicationAssemble shareTaskAuthApplicationAssemble;
	private final HandleItemDetailAssembler handleItemDetailAssembler;

	/**
	 * 共享任务申请获取目标源列表
	 *
	 * @return
	 */
	@GetMapping("/target-sources")
	public R<List<ShareTaskApplicationTargetSourceResponse>> findTargetSource() {
		List<TargetSource> targetSources= service.findTargetSource();
		List<ShareTaskApplicationTargetSourceResponse> resp = assembler.toShareTaskApplicationTargetSourceResponse(targetSources);
		return R.ok(resp);
	}

	/**
	 * 创建共享任务申请
	 */
	@PostMapping
	public R createShareTaskApplications(@RequestBody @Valid ShareTaskApplicationsRequest request) {
		var command = assembler.toCommand(request);
		service.createShareTaskApplications(command);
		return R.ok();
	}

	/**
	 * 修改共享任务申请
	 *
	 * @param request 修改参数
	 * @return 修改结果
	 */
	@PutMapping("{applicationId}")
	public R updateShareTaskApplications(@PathVariable Long applicationId, @RequestBody @Valid ShareTaskApplicationsRequest request) {
		var command = assembler.toCommand(request);
		service.updateShareTaskApplications(applicationId, command);
		return R.ok();
	}

	/**
	 * 查询共享任务列表
	 *
	 * @param request 查询参数
	 * @return 共享任务列表
	 */
	@GetMapping
	public R<PageResponse<ShareTaskApplicationsView>> listShareTaskApplications(@Valid ListShareTaskApplicationsRequest request) {
		var query = assembler.toQueryList(request);
		return R.ok(service.listShareTaskApplications(query));
	}

	/**
	 * 查询共享任务详情
	 *
	 * @param applicationId 共享任务ID
	 * @return 共享任务详情
	 */
	@GetMapping("/{applicationId}")
	public R<ShareTaskApplicationDetailResponse> getShareTaskApplicationDetail(@PathVariable Long applicationId) {
		ShareTaskApplicationDetails detail = service.getShareTaskApplicationDetail(applicationId);
		return R.ok(assembler.toDetailResponse(detail));
	}

	// 查询共享任务修改详情
	// 共享任务申请列表点击修改。查询图谱信息。
	@GetMapping("/{applicationId}/graph")
	public R<ShareTaskApplicationGraphResponse> getShareTaskApplicationGraph(@PathVariable Long applicationId) {
		ShareTaskApplicationsDomainEntity graph = service.getShareTaskApplicationGraph(applicationId);
		return R.ok(assembler.toShareTaskApplicationGraphResponse(graph));
	}

	@PostMapping("/{shareTaskApplicationsId}/review")
	public R review(@PathVariable("shareTaskApplicationsId") Long shareTaskApplicationsId, @RequestBody @Valid ReviewRequest request) {
		ReviewCommand command = shareTaskAuthApplicationAssemble.toReviewCommand(shareTaskApplicationsId, request);
		shareTaskAuthApplicationService.review(command);
		return R.ok();
	}

	@DeleteMapping("/{shareTaskapplicationId}")
	public R deleteShareTaskApplications(@PathVariable Long shareTaskapplicationId) {
		service.deleteShareTaskApplications(shareTaskapplicationId);
		return R.ok();
	}



	/**
	 * 共享任务数据详情
	 *
	 * @param dataId 共享任务明细ID
	 * @return 任务执行详情
	 */
	@GetMapping("/share-data/{dataId}")
	public R<SharedDataDetailsResponse> getHandleDetails(@PathVariable("dataId") Long dataId) {
		var sharedDataDetails = service.getHandleDetails(dataId);
		return R.ok(assembler.toSharedDataDetailsResponse(sharedDataDetails));
	}
}

