package cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection;

import java.time.LocalDateTime;

public interface TargetSourceListView {

	Long getId();

	Integer getPlatformType();

	String getAppName();

	String getAppHandleCode();

	String getTargetSourceName();

	String getItems();

	LocalDateTime getUpdateTime();

	String getUpdateUser();

	Long getCreateBy();

	Long getUpdateBy();

	String getCreateByName();

	String getUpdateByName();

	/**
	 * 创建时间
	 */
	LocalDateTime getCreateTime();
}
