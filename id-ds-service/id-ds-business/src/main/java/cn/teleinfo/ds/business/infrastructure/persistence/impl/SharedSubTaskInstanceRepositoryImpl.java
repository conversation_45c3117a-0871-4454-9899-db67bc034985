package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.SharedSubTaskInstance;
import cn.teleinfo.ds.business.domain.repository.SharedSubTaskInstanceRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedSubTaskInstanceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.SharedSubTaskInstanceJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@AllArgsConstructor
public class SharedSubTaskInstanceRepositoryImpl implements SharedSubTaskInstanceRepository {
	private final SharedSubTaskInstanceJpaRepository sharedSubTaskInstanceJpaRepository;

	@Override
	public Long save(SharedSubTaskInstance sharedSubTaskInstance) {
		SharedSubTaskInstanceEntity entity = null;
		if (sharedSubTaskInstance.getId() != null) {
			entity = sharedSubTaskInstanceJpaRepository.findById(sharedSubTaskInstance.getId()).orElse(new SharedSubTaskInstanceEntity());
		}else{
			entity = new SharedSubTaskInstanceEntity();
		}

		BeanUtils.copyProperties(sharedSubTaskInstance, entity);
		sharedSubTaskInstanceJpaRepository.save(entity);
		return entity.getId();
	}

	/**
	 * 更新子任务
	 * <p>
	 * 执行时长，数据总量，执行状态等
	 *
	 * @param sharedSubTaskInstance
	 */
	@Override
	public void updateRuntimeInfo(SharedSubTaskInstance sharedSubTaskInstance) {
		Optional<SharedSubTaskInstanceEntity> optional = sharedSubTaskInstanceJpaRepository.findById(sharedSubTaskInstance.getId());
		if (optional.isPresent()) {
			SharedSubTaskInstanceEntity sharedSubTaskInstanceEntity = optional.get();
			sharedSubTaskInstanceEntity.setSubTaskStatus(sharedSubTaskInstance.getSubTaskStatus());
			sharedSubTaskInstanceEntity.setCurrentStep(sharedSubTaskInstance.getCurrentStep());
			sharedSubTaskInstanceEntity.setRunDuration(sharedSubTaskInstance.getRunDuration());
			sharedSubTaskInstanceEntity.setSharedDataCount(sharedSubTaskInstance.getSharedDataCount());
			sharedSubTaskInstanceEntity.setErrorMessage(sharedSubTaskInstance.getErrorMessage());
			sharedSubTaskInstanceJpaRepository.save(sharedSubTaskInstanceEntity);
		}
	}
}
