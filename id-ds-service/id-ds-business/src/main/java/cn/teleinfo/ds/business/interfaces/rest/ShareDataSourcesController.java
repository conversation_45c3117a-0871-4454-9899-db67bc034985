package cn.teleinfo.ds.business.interfaces.rest;


import cn.teleinfo.ds.business.application.command.CreateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.query.ListShareDataSourcesQuery;
import cn.teleinfo.ds.business.application.service.ShareDataSourcesApplicationService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.business.interfaces.assembler.ShareDataSourcesAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.CreateShareDataSourcesRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareDataSourcesRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.UpdateShareDataSourcesRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareDataSourcesDetailResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 共享源管理
 * 数据源管理
 */
@RestController
@RequestMapping("/share-data-sources")
@AllArgsConstructor
public class ShareDataSourcesController {
	private final ShareDataSourcesApplicationService shareDataSourcesApplicationService;
	private final ShareDataSourcesAssembler shareDataSourcesAssembler;

	/**
	 * 创建共享数据源
	 *
	 * @param request 共享数据源信息
	 */
	@PostMapping
	public R createShareDataSources(@RequestBody @Valid CreateShareDataSourcesRequest request) {
		CreateShareDataSourcesCommand command = shareDataSourcesAssembler.toCreateShareDataSourcesCommand(request);
		shareDataSourcesApplicationService.createShareDataSources(command);
		return R.ok();
	}


	@GetMapping
	public R<PageResponse<ShareSourceDTO>> listShareDataSources(@Valid ListShareDataSourcesRequest request) {
		ListShareDataSourcesQuery query = shareDataSourcesAssembler.toListShareDataSourcesQuery(request);
		return R.ok(shareDataSourcesApplicationService.listShareDataSources(query));
	}

	/**
	 * 共享数据源详情
	 *
	 * @param id id
	 */
	@GetMapping("/{id}")
	public R<ShareDataSourcesDetailResponse> queryShareDataSourcesDetail(@PathVariable("id") String id) {
		var shareDataSourcesDetail = shareDataSourcesApplicationService.queryShareDataSourcesDetail(id);
		return R.ok(shareDataSourcesAssembler.toShareDataSourcesDetailResponse(shareDataSourcesDetail));
	}

	/**
	 * 修改共享数据源
	 *
	 * @param request 共享数据源信息
	 */
	@PutMapping
	public R updateShareDataSources(@RequestBody @Valid UpdateShareDataSourcesRequest request) {
		UpdateShareDataSourcesCommand command = shareDataSourcesAssembler.toUpdateShareDataSourcesCommand(request);
		shareDataSourcesApplicationService.updateShareDataSources(command);
		return R.ok();
	}


	@DeleteMapping("{id}")
	public R deleteShareDataSources(@PathVariable("id") Long id) {
		shareDataSourcesApplicationService.deleteShareDataSources(id);
		return R.ok();
	}
}
