package cn.teleinfo.ds.business.application.command.sharetaskapplications;

import cn.teleinfo.ds.business.interfaces.dto.request.CartRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.GraphRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskApplicationsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareTaskApplicationsRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ShareTaskApplicationsCommand {

	private Long id;

	/**
	 * 共享任务名称
	 */
	@NotNull(message = "共享任务名称 不能为空")
	private String taskName;

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 任务类型(1：手动任务 2：定时任务)
	 */
	@NotNull(message = "任务类型 不能为空")
	private Integer taskType;

	/**
	 * 目标源ID
	 */
	@NotNull(message = "目标源 不能为空")
	private Long targetSourceId;

	/**
	 * 应用系统编码
	 */
	@NotNull(message = "应用系统编码 不能为空")
	private String appHandleCode;

	/**
	 * 企业前缀
	 */
	@NotNull(message = "企业前缀 不能为空")
	private String entPrefix;

	/**
	 * 定时任务详情
	 */
	private String taskCron;

	/**
	 * 根节点
	 */
	private String rootHandle;

	/**
	 * 省份前缀
	 */
	private String provincePrefix;

	/**
	 * 购物车
	 */
	private List<CartRequest> cart;

	/**
	 * 图谱信息
	 */
	private GraphRequest graph;

	private boolean isUpdate;

	/**
	 * 定时任务表达式
	 */
	private String cronExpression;
}
