package cn.teleinfo.ds.business.domain.model.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class HandleItemDomainEntity {

	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除: 0 未删除 null 已删除
	 */
	private Integer isDeleted;

	/**
	 * 属性字段
	 */
	private String field;

	/**
	 * 索引
	 */
	private Integer fieldIndex;

	/**
	 * 描述
	 */
	private String description;

	/**
	 * 属性类型（1固定值 2标识解析数据源 3标识值 4标识-属性）
	 */
	private Integer fieldType;

	/**
	 * 标识ID（关联t_handle.id）
	 */
	private Long handleId;

	/**
	 * 属性值
	 */
	private String fieldValue;

	/**
	 * 数据通道ID
	 */
	private Long dataChannelId;

	/**
	 * 数据通道名称
	 */
	private String dataChannelName;

	/**
	 * 通道
	 */
	private ShareChannel shareChannel;

	/**
	 * 属性来源类型（0基础属性 1扩展属性）
	 */
	private Integer fieldSourceType;

	/**
	 * 应用标识编码
	 */
	private String appHandleCode;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 数据通道类型
	 */
	private Integer dataChannelType;

	/**
	 * 数据库名称
	 */
	private String databaseName;

	/**
	 * 数据库IP
	 */
	private String databaseIp;

	/**
	 * 所属表
	 */
	private String tableName;

	/**
	 * 所属字段
	 */
	private String columnName;

	/**
	 * 关联属性
	 */
	List<HandleReferenceDomainEntity> references;
}
