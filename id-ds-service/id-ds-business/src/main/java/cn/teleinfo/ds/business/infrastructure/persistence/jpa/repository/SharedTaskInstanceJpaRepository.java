package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskInstanceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskStatusView;
import feign.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface SharedTaskInstanceJpaRepository extends BaseRepository<SharedTaskInstanceEntity, Long> {

	@Query(nativeQuery = true,
			value = """
					         SELECT
					             s.id,
					             s.shared_task_id AS sharedTaskId,
					             s.task_instance_no AS taskInstanceNo,
					             t.task_no as taskNo,
					             s.task_name as taskName,
					             s.task_type as taskType,
					             s.execution_type AS executionType,
					             s.run_status AS runStatus,
					              s.run_time AS runTime,
					              s.log_path as logPath,
								 a.app_name as appHandleName 
					FROM t_shared_task_instance s
					LEFT join t_shared_task t on t.id = s.shared_task_id and t.is_deleted = 0 
					LEFT JOIN t_app_info a on s.app_handle_code = a.handle_code 
					where IF(:executionType IS NOT NULL, s.execution_type = :executionType, 1=1 )
					         and IF(:runStatus IS NOT NULL, s.run_status = :runStatus, 1=1 )
					         and IF(:sharedTaskId IS NOT NULL, s.shared_task_id = :sharedTaskId, 1=1 )
					         and IF(:taskInstanceNo != '' AND :taskInstanceNo is not null, s.task_instance_no like CONCAT('%',:taskInstanceNo,'%'), 1=1 )
							 and IF(:taskInstanceName != '' AND :taskInstanceName is not null, s.task_name like CONCAT('%',:taskInstanceName,'%'), 1=1 )
					         and IF(:taskNo != '' AND :taskNo is not null, t.task_no like CONCAT('%',:taskNo,'%'), 1=1 )
					         and IF(:taskName != '' AND :taskName is not null, s.task_name like CONCAT('%',:taskName,'%'), 1=1 )
					         AND IF(:appHandleCode != '' AND :appHandleCode is not null, s.app_handle_code like CONCAT('%',:appHandleCode,'%'), 1=1 )
							 AND IF(COALESCE(:userHandleCode) IS NOT NULL, s.app_handle_code in (:userHandleCode), 1=1)
							 AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, s.run_time BETWEEN :startTime AND :endTime , 1=1) 
							 ORDER BY s.update_time DESC 
					""",
			countQuery = """
					SELECT
					  count(*)
					   FROM t_shared_task_instance s
					   LEFT join t_shared_task t on t.id = s.shared_task_id and t.is_deleted = 0
					   LEFT JOIN t_app_info a on s.app_handle_code = a.handle_code 
					                where IF(:executionType IS NOT NULL, s.execution_type = :executionType, 1=1 )
					                and IF(:runStatus IS NOT NULL, s.run_status = :runStatus, 1=1 )
					                and IF(:sharedTaskId IS NOT NULL, s.shared_task_id = :sharedTaskId, 1=1 )
					                and IF(:taskInstanceNo != '' AND :taskInstanceNo is not null, s.task_instance_no like CONCAT('%',:taskInstanceNo,'%'), 1=1 )
							 		and IF(:taskInstanceName != '' AND :taskInstanceName is not null, s.task_name like CONCAT('%',:taskInstanceName,'%'), 1=1 )
					                and IF(:taskNo != '' AND :taskNo is not null, t.task_no like CONCAT('%',:taskNo,'%'), 1=1 )
					                and IF(:taskName != '' AND :taskName is not null, s.task_name like CONCAT('%',:taskName,'%'), 1=1 )
					                AND IF(:appHandleCode != '' AND :appHandleCode is not null, s.app_handle_code like CONCAT('%',:appHandleCode,'%'), 1=1 )
									AND IF(COALESCE(:userHandleCode) IS NOT NULL, s.app_handle_code in (:userHandleCode), 1=1)
							 		AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, s.run_time BETWEEN :startTime AND :endTime , 1=1) 
					"""
	)
	Page<SharedTaskInstanceListView> listSharedTaskInstances(@Param("sharedTaskId") Long sharedTaskId,
															 @Param("taskInstanceNo") String taskInstanceNo,
															 @Param("taskInstanceName") String taskInstanceName,
															 @Param("executionType") Integer executionType,
															 @Param("runStatus") Integer runStatus,
															 @Param("taskNo") String taskNo,
															 @Param("taskName") String taskName,
															 @Param("appHandleCode") String appHandleCode,
															 @Param("userHandleCode") List<String> userHandleCode,
															 @Param("startTime") LocalDateTime startTime,
															 @Param("endTime") LocalDateTime endTime,
															 Pageable pageable);


	@Query(nativeQuery = true,
			value = """
					SELECT
					  s.id,
					  s.shared_task_id AS sharedTaskId,
					  s.task_instance_no AS taskInstanceNo,
					  s.task_name as taskName,
					  t.task_no as taskNo,
					  s.task_name as taskName,
					  s.task_type as taskType,
					  s.task_status as taskStatus,
					  s.execution_type AS executionType,
					  s.run_status AS runStatus,
					  s.target_source_id as targetSourceId,
					  s.run_duration as runDuration,
					  s.run_time as runTime,
					  s.log_path as logPath,
					  s.cron_expression as cronExpression, 
					  a.target_source_name as targetName,
					  s.shared_data_count as sharedDataCount,
					  su.name as operatorName
					 from t_shared_task_instance s
					 left join t_shared_task t on t.id = s.shared_task_id and t.is_deleted = 0
					 left join t_target_source a on s.target_source_id = a.id and a.is_deleted = 0 
					 left join sys_user su on su.user_id = s.operator and su.del_flag = 0
					 where s.id = :id
					 and s.is_deleted = 0
					"""
	)
	SharedTaskInstanceView getSharedTaskInstanceById(@Param("id") Long id);

	@Query(nativeQuery = true,
			value = "SELECT distinct " +
					"a.id, " +
					"b.id as subId, " +
					"c.handle as handle, " +
					"c.name as handleName, " +
					"b.sub_task_status as subTaskStatus, " +
					"b.current_step as currentStep, " +
					"b.run_time as runTime, " +
					"IF(b.sub_task_status = 1, TIMESTAMPDIFF(SECOND, b.run_time, NOW()), b.run_duration) as runDuration, " +
					"b.shared_data_count as sharedDataCount, " +
					"b.error_message as errorMessage, " +
					"a.execution_type as executionType, " +
					"a.task_instance_no as taskInstanceNo, " +
					"d.target_source_name as sourceName, " +
					"f.platform_type as shareType " +
					"from t_shared_task e " +
					"left join t_shared_task_instance a on e.last_test_instance_id = a.id and a.is_deleted = 0 " +
					"left join t_shared_sub_task_instance b on a.id = b.shared_task_instance_id " +
					"left join t_handle c on b.handle = c.handle and c.is_deleted = 0 " +
					"left join t_target_source d on a.target_source_id = d.id and d.is_deleted = 0 " +
					"left join t_share_source f on e.app_handle_code = f.app_handle_code " +
					"where e.id = :id and e.is_deleted = 0 "
	)
	List<SharedTaskStatusView> getSharedTaskTestStatus(@Param("id") Long id);

	@Query(nativeQuery = true,
			value = "SELECT distinct " +
					"a.id, " +
					"b.id as subId, " +
					"c.handle as handle, " +
					"c.name as handleName, " +
					"b.sub_task_status as subTaskStatus, " +
					"b.current_step as currentStep, " +
					"b.run_time as runTime, " +
					"IF(b.sub_task_status = 1, TIMESTAMPDIFF(SECOND, b.run_time, NOW()), b.run_duration) as runDuration, " +
					"b.shared_data_count as sharedDataCount, " +
					"b.error_message as errorMessage, " +
					"a.execution_type as executionType, " +
					"a.task_instance_no as taskInstanceNo, " +
					"d.target_source_name as sourceName, " +
					"f.platform_type as shareType " +
					"from t_shared_task e " +
					"left join t_shared_task_instance a on e.last_execution_instance_id = a.id and a.is_deleted = 0 " +
					"left join t_shared_sub_task_instance b on a.id = b.shared_task_instance_id " +
					"left join t_handle c on b.handle = c.handle and c.is_deleted = 0 " +
					"left join t_target_source d on a.target_source_id = d.id and d.is_deleted = 0 " +
					"left join t_share_source f on e.app_handle_code = f.app_handle_code " +
					"where e.id = :id and e.is_deleted = 0 "
	)
	List<SharedTaskStatusView> getSharedTaskExecuteStatus(@Param("id") Long id);
}