package cn.teleinfo.ds.business.application.query;

import cn.teleinfo.ds.common.core.util.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
public class ListHandlesQuery extends PageRequest {
	/**
	 * 对象标识名称
	 */
	private String name;

	/**
	 * 对象标识
	 */
	private String handle;

	/**
	 * 应用编码
	 */
	private String appHandleCode;

	/**
	 * 开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 使用用户过滤
	 */
	private String userHandleFilter;

}
