package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntityListeners;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 共享任务表
 */
@Getter
@Setter
@Entity
@Table(name = "t_shared_task")
@SQLDelete(sql = "update t_shared_task set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
@EntityListeners({BaseEntityListeners.class, AuditingEntityListener.class})
public class SharedTaskEntity {

	@Id
	private Long id;

	/**
	 * 创建时间
	 */
	@CreatedDate
	@Column(name = "create_time")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@LastModifiedDate
	@Column(name = "update_time")
	private LocalDateTime updateTime;


    /**
     * 任务编号，如RW202506001
     */
    @Column(name = "task_no", nullable = false)
    private String taskNo;

    /**
     * 任务名称
     */
    @Column(name = "task_name", nullable = false)
    private String taskName;

    /**
     * 任务类型：1-手动任务，2-定时任务
     */
    @Column(name = "task_type", nullable = false)
    private Integer taskType;

    /**
     * 任务状态：1-启用，0-禁用
     */
    @Column(name = "task_status", nullable = false)
    private Integer taskStatus;

    /**
     * 测试状态：1-成功，2-失败，0-未测试
     */
    @Column(name = "test_status")
    private Integer testStatus;

    /**
     * 最后一次测试执行的任务实例ID
     */
    @Column(name = "last_test_instance_id")
    private Long lastTestInstanceId;

    /**
     * 最后一次正常执行的任务实例ID
     */
    @Column(name = "last_execution_instance_id")
    private Long lastExecutionInstanceId;

    /**
     * 运行状态：1-运行中，2-成功，3-失败，0-未运行
     */
    @Column(name = "run_status")
    private Integer runStatus;

    /**
     * 目标源ID
     */
    @Column(name = "target_source_id")
    private Long targetSourceId;

    /**
     * 数据库名
     */
    @Column(name = "database_name")
    private String databaseName;

    /**
     * 应用身份编码
     */
    @Column(name = "app_handle_code")
    private String appHandleCode;

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * CRON表达式，如0 0 12 * * ?（仅定时任务使用）
     */
    @Column(name = "cron_expression")
    private String cronExpression;

    /**
     * 最后一次任务执行编号，如REDO202506011001
     */
    @Column(name = "last_execution_no")
    private String lastExecutionNo;

    /**
     * 最后一次运行时间
     */
    @Column(name = "last_run_time")
    private LocalDateTime lastRunTime;

    /**
     * 最后一次运行时长(秒)
     */
    @Column(name = "last_run_duration")
    private Integer lastRunDuration;

    /**
     * 最后一次共享数据总量
     */
    @Column(name = "last_shared_data_count")
    private Integer lastSharedDataCount;

    /**
     * 操作人
     */
    @Column(name = "operator", nullable = false)
    private String operator;

	/**
	 * 0:否, NULL:是
	 */
	@Column(name = "is_deleted")
	private Integer isDeleted;
} 