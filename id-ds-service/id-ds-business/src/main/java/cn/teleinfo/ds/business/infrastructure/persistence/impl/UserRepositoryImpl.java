package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.teleinfo.ds.business.domain.model.entity.UserInfo;
import cn.teleinfo.ds.business.domain.repository.UserRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.UserAppView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.UserInfoView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.UserJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class UserRepositoryImpl implements UserRepository {
	private final UserJpaRepository userJpaRepository;

	@Override
	public UserInfo findById(String username) {
		List<UserInfoView> userInfoViews = userJpaRepository.findInfoById(username);
		if (userInfoViews != null && !userInfoViews.isEmpty()) {
			UserInfoView userInfoView = userInfoViews.get(0);
			UserInfo userInfo = new UserInfo();
			userInfo.setUserId(userInfoView.getUserId());
			userInfo.setUsername(userInfoView.getUsername());
			userInfo.setPassword(userInfoView.getPassword());
			userInfo.setUcOpenId(userInfoView.getUcOpenId());
			userInfo.setSalt(userInfoView.getSalt());
			userInfo.setPhone(userInfoView.getPhone());
			userInfo.setAvatar(userInfoView.getAvatar());
			userInfo.setNickname(userInfoView.getNickname());
			userInfo.setName(userInfoView.getName());
			userInfo.setEmail(userInfoView.getEmail());
			userInfo.setDeptId(userInfoView.getDeptId());
			userInfo.setCreateBy(userInfoView.getCreateBy());
			userInfo.setUpdateBy(userInfoView.getUpdateBy());
			userInfo.setCreateTime(userInfoView.getCreateTime());
			userInfo.setUpdateTime(userInfoView.getUpdateTime());
			userInfo.setLockFlag(userInfoView.getLockFlag());
			userInfo.setDelFlag(userInfoView.getDelFlag());
			userInfo.setWxOpenid(userInfoView.getWxOpenid());
			userInfo.setMiniOpenid(userInfoView.getMiniOpenid());
			userInfo.setQqOpenid(userInfoView.getQqOpenid());
			userInfo.setGiteeLogin(userInfoView.getGiteeLogin());
			userInfo.setOscId(userInfoView.getOscId());
			userInfo.setAppIds(new ArrayList<>());
			for (UserInfoView u : userInfoViews) {
				if(u.getAppId() != null){
					userInfo.getAppIds().add(u.getAppId());
				}
			}

			return userInfo;
		}

		return null;
	}

	@Override
	public List<UserAppView> findUserAppByUserId(Long userId) {
		return userJpaRepository.findUserAppByUserId(userId);
	}
}
