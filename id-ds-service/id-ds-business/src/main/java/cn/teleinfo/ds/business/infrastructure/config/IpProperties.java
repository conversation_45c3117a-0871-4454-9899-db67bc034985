package cn.teleinfo.ds.business.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "api")
public class IpProperties {

    private String ip;


    private String syncUserUrl = "/api/v1/public/integrated-data-channels";

    private String syncAppUrl = "/api/v1/public/integrated-applications";

    private String syncChannelUrl = "/api/v1/public/integrated-channels";

	private String syncHandleUrl = "/api/v1/public/integrated-handles";

}
