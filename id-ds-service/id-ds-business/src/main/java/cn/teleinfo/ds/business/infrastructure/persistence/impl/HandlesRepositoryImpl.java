package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.repository.HandlesRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleItemEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.HandleItemJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.HandleJpaRepository;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import cn.teleinfo.ds.common.core.util.PageResponse;
import jakarta.persistence.criteria.Predicate;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.Check;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Component
public class HandlesRepositoryImpl implements HandlesRepository {

	private final HandleJpaRepository handleJpaRepository;
	private final HandleItemJpaRepository handleItemJpaRepository;

	@Override
	public PageResponse<HandleListView> listHandles(String appHandleCode, String name, String handle, List<String> userHandleCode, Integer page, Integer size, LocalDateTime startTime, LocalDateTime endTime) {
		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);

		Page<HandleListView> listHandles = handleJpaRepository.findListHandles(appHandleCode, name, handle, userHandleCode, startTime, endTime, pageable);

		return new PageResponse<>(
				listHandles.getContent(),
				listHandles.getTotalElements(),
				(long) size,
				(long) page,
				(long) listHandles.getTotalPages());
	}

	@Override
	public HandleDomainEntity findByHandle(String handle) {
		HandleEntity entity = handleJpaRepository.findByHandle(handle);
		if (entity == null) {
			return null;
		}

		HandleDomainEntity handleDomainEntity = BeanUtil.copyProperties(entity, HandleDomainEntity.class);
		List<HandleItemEntity> items = handleItemJpaRepository.findByHandleId(handleDomainEntity.getId());
		if (items != null && !items.isEmpty()) {
			handleDomainEntity.setHandleItems(BeanUtil.copyToList(items, HandleItemDomainEntity.class));
		}
		return handleDomainEntity;
	}

	@Override
	public SharedTaskView getHandleDetails(String handle) {
		return handleJpaRepository.getHandleDetails(handle);
	}

	@Override
	public HandleListView findHandleDetailById(Long id) {
		return handleJpaRepository.findHandleDetailById(id);
	}
}
