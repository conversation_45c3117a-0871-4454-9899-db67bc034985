package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.command.ReviewCommand;
import cn.teleinfo.ds.business.application.query.ShareTaskAuthQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareDataDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskAuthsRequest;
import cn.teleinfo.ds.common.core.util.PageResponse;

public interface ShareTaskAuthApplicationService {

	void review(ReviewCommand reviewCommand);

	PageResponse<ShareTaskAuthView> listShareTaskAuths(ShareTaskAuthQuery query, ListShareTaskAuthsRequest request);

	ShareTaskAuthDomainEntity getShareTaskAuthDetail(Long shareTaskAuthId);

	ShareDataDetails getHandleDetails(Long dataId);
}
