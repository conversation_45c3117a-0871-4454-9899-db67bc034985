package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDetailsDomainEntity;

import java.util.List;

public interface ShareTaskAuthDetailsRepository {
	/**
	 * 插入
	 * @param shareTaskAuthDetailsDomainEntity
	 * @return id
	 */
	Long save(ShareTaskAuthDetailsDomainEntity shareTaskAuthDetailsDomainEntity);

	/**
	 * 查询授权记录详情
	 * @param shareTaskAuthId
	 * @return
	 */
	List<ShareTaskAuthDetailsDomainEntity> findByShareTaskAuthId(Long shareTaskAuthId);

	ShareTaskAuthDetailsDomainEntity findById(Long dataId);
}
