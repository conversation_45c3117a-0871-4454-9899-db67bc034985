package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleReferenceDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.Graph;
import cn.teleinfo.ds.business.domain.repository.HandleGraphRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.graph.*;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.GraphHandlesDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.*;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.*;
import cn.teleinfo.ds.business.infrastructure.persistence.mapper.HandleGraphMapper;
import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Component
@AllArgsConstructor
public class HandleGraphRepositoryImpl implements HandleGraphRepository {

	private final HandleJpaRepository handleJpaRepository;
	private final HandleItemJpaRepository handleItemJpaRepository;
	private final HandleReferenceJpaRepository handleReferenceJpaRepository;
	private final HandleGraphMapper handleGraphMapper;

	private final AppInfoJpaRepository appInfoJpaRepository;
	private final EntPrefixJpaRepository entPrefixJpaRepository;

	@Override
	public Graph handleChildren(String handle) {
		return handleGraph(handle, 3);
	}

	@Override
	public Graph handleGraph(String handle) {
		Graph graph = handleGraph(handle, 4);
		graph.setRootHandle(handle);
		return graph;
	}

	/**
	 * 对象标识模糊查询
	 */
	@Override
	public List<GraphHandlesDTO> getGraphHandles(String handle) {
		return handleJpaRepository.getGraphHandlesByHandle(handle);
	}

	private Graph handleGraph(String handle, int maxLayer) {
		// handle 最小层级
		Map<String, Integer> handleLayerMap = new HashMap<>();
		// handle节点集合
		Map<String, NodeDTO> nodeMap = new HashMap<>();
		List<EdgeDTO> edges = new ArrayList<>();
		Set<String> visited = new HashSet<>();
		deepNode(handle, 1, null, handleLayerMap, nodeMap, edges, visited, maxLayer);

		// 统计hasChildren
		for (EdgeDTO edge : edges) {
			NodeDTO parent = nodeMap.get(edge.getSource());
			if (parent != null)
				parent.setHasChildren(true);
		}

		// 过滤掉最后一层的节点和边
		List<NodeDTO> filteredNodes = new ArrayList<>();
		List<EdgeDTO> filteredEdges = new ArrayList<>();

		for (NodeDTO node : nodeMap.values()) {
			if (node.getLayer() < maxLayer) {
				filteredNodes.add(node);
			}
		}

		for (EdgeDTO edge : edges) {
			NodeDTO sourceNode = nodeMap.get(edge.getSource());
			NodeDTO targetNode = nodeMap.get(edge.getTarget());
			if (sourceNode != null && targetNode != null &&
					sourceNode.getLayer() < maxLayer && targetNode.getLayer() < maxLayer) {
				filteredEdges.add(edge);
			}
		}
		// 没有被作为source的节点hasChildren=0
		for (NodeDTO node : filteredNodes) {
			if (node.getHasChildren() == null)
				node.setHasChildren(false);
		}
		GraphDTO graphDTO = new GraphDTO();
		graphDTO.setNodes(filteredNodes);
		graphDTO.setEdges(filteredEdges);
		return handleGraphMapper.toGraph(graphDTO);
	}

	/**
	 * 获取下层节点
	 *
	 * @param handle         当前节点
	 * @param layer          当前层级
	 * @param parentHandle   上级节点
	 * @param handleLayerMap 节点最小层级集合 用来过滤重复节点保证只出现一次且为最小节点
	 * @param nodeMap        节点集合
	 * @param edges          线集合
	 * @param visited        记录当前递归路径中已访问过的标识，确保每个标识在一条路径中只被访问一次
	 */
	private void deepNode(String handle, int layer, String parentHandle,
			Map<String, Integer> handleLayerMap,
			Map<String, NodeDTO> nodeMap,
			List<EdgeDTO> edges,
			Set<String> visited,
			int maxLayer) {
		// 如果层深大于maxLayer 或者被访问过 不继续查子节点
		if (layer > maxLayer || visited.contains(handle))
			return;
		visited.add(handle);
		// 查询handle
		HandleEntity entity = handleJpaRepository.findByHandle(handle);
		if (entity == null)
			return;
		// 记录最小层级
		if (handleLayerMap.containsKey(handle) && handleLayerMap.get(handle) <= layer)
			return;
		handleLayerMap.put(handle, layer);
		// 构建节点
		NodeDTO node = new NodeDTO();
		node.setId(handle);
		node.setCode(handle);
		node.setLabel(entity.getName());
		node.setLayer(layer);
		node.setType(entity.getEntityType());
		nodeMap.put(handle, node);
		// 构建线
		if (parentHandle != null) {
			edges.add(new EdgeDTO(parentHandle, handle));
		}
		// 查询属性
		List<HandleItemEntity> items = handleItemJpaRepository.findByHandleId(entity.getId());
		for (HandleItemEntity item : items) {
			if (item.getFieldType() == 2) {
				String childHandle = item.getFieldValue();
				if (childHandle.contains(",")) {
					String[] handles = childHandle.split(",");
					for (String handle1 : handles) {
						deepNode(handle1, layer + 1, handle, handleLayerMap, nodeMap, edges, visited, maxLayer);
					}
				} else {
					deepNode(childHandle, layer + 1, handle, handleLayerMap, nodeMap, edges, visited, maxLayer);

				}
			} else if (item.getFieldType() == 3) {
				List<HandleReferenceEntity> refs = handleReferenceJpaRepository.findByHandleItemId(item.getId());
				for (HandleReferenceEntity ref : refs) {
					String childHandle = ref.getReferenceHandle();
					deepNode(childHandle, layer + 1, handle, handleLayerMap, nodeMap, edges, visited, maxLayer);
				}
			}
		}
	}

	@Override
	public Handle handleItems(String handle) {
		HandleEntity handleEntity = handleJpaRepository.findByHandle(handle);
		AppInfoEntity app = appInfoJpaRepository.findByHandleCode(handleEntity.getAppHandleCode());
		EntPrefixEntity ent = entPrefixJpaRepository.findByEntPrefix(handleEntity.getEntPrefix());
		List<HandleItemEntity> handleItemEntities = handleItemJpaRepository.findByHandleId(handleEntity.getId());
		List<HandleItemDomainEntity> itemVOS = Lists.newArrayList();
		HandleDomainEntity handleModel = new HandleDomainEntity();
		BeanUtils.copyProperties(handleEntity, handleModel);
		handleModel.setAppName(app.getAppName());
		handleModel.setEntName(ent.getOrgName());
		if (!CollectionUtils.isEmpty(handleItemEntities)) {
			handleItemEntities.forEach(itemEntity -> {
				HandleItemDomainEntity itemVO = new HandleItemDomainEntity();
				BeanUtils.copyProperties(itemEntity, itemVO);
				if (itemEntity.getFieldType() == 1) {
					itemVO.setFieldValue(itemEntity.getFieldValue());
				}
				if (itemEntity.getFieldType() == 2 && StrUtil.isNotEmpty(itemEntity.getFieldValue())) {
					itemVO.setFieldValue(itemEntity.getFieldValue());
				}

				// 查询reference
				List<HandleReferenceEntity> referenceEntities = handleReferenceJpaRepository
						.findByHandleItemId(itemEntity.getId());
				if (!CollectionUtils.isEmpty(referenceEntities)) {
					List<HandleReferenceDomainEntity> referenceVOS = Lists.newArrayList();
					referenceEntities.forEach(referenceEntity -> {
						HandleReferenceDomainEntity referenceVO = new HandleReferenceDomainEntity();
						referenceVO.setReferenceHandle(referenceEntity.getReferenceHandle());
						if (StringUtils.isNotEmpty(referenceEntity.getReferenceHandleProp())) {
							referenceVO.setReferenceHandleProp(referenceEntity.getReferenceHandleProp());
						}
						if (StringUtils.isNotEmpty(referenceEntity.getQueryProp())) {
							referenceVO.setQueryProp(referenceEntity.getQueryProp());
						}
						if (StringUtils.isNotEmpty(referenceEntity.getParamProp())) {
							referenceVO.setParamProp(referenceEntity.getParamProp());
						}
						referenceVOS.add(referenceVO);
					});
					itemVO.setReferences(referenceVOS);
				}

				itemVOS.add(itemVO);
			});
		}
		return handleGraphMapper.toHandle(itemVOS, handleModel);
	}

	@Override
	public HandleDomainEntity findHandleDomainByHandle(String handle) {
		var entity = handleJpaRepository.findByHandle(handle);
		if (entity == null)
			return null;
		return handleGraphMapper.toHandleDomainEntity(entity);
	}

	@Override
	public List<HandleItemDomainEntity> findHandleItemsByHandleId(Long handleId) {
		var items = handleItemJpaRepository.findByHandleId(handleId);
		return handleGraphMapper.toHandleItemDomainEntities(items);
	}

	@Override
	public List<HandleReferenceDomainEntity> findReferencesByHandleItemId(Long itemId) {
		var refs = handleReferenceJpaRepository.findByHandleItemId(itemId);
		return handleGraphMapper.toHandleReferenceDomainEntities(refs);
	}

	@Override
	public String findAppNameByHandleCode(String appHandleCode) {
		var app = appInfoJpaRepository.findByHandleCode(appHandleCode);
		return app != null ? app.getAppName() : null;
	}

	@Override
	public String findEntNameByEntPrefix(String entPrefix) {
		var ent = entPrefixJpaRepository.findByEntPrefix(entPrefix);
		return ent != null ? ent.getOrgName() : null;
	}
}
