package cn.teleinfo.ds.business.domain.repository;


import cn.teleinfo.ds.business.infrastructure.persistence.dto.sync.*;

import java.time.LocalDateTime;
import java.util.List;

public interface DataSyncRepository {
//	List<SyncUserDTO> integratedUsers();

	List<SyncChannelDTO> integratedDataChannels();

	List<SyncAppDTO> integratedApplications();

	HandlePageDTO integratedHandles(Integer page, Integer size, LocalDateTime updatedTime);



}
