package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.repository.DataIntegratedRepository;
import cn.teleinfo.ds.business.domain.repository.DataSyncRepository;
import cn.teleinfo.ds.business.domain.service.DataIntegratedDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.sync.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class DataIntegratedDomainServiceImpl implements DataIntegratedDomainService {
	private final DataIntegratedRepository dataIntegratedRepository;

	private final DataSyncRepository dataSyncRepository;

//    public void integratedUsers() {
//        List<SyncUserDTO> users = dataSyncRepository.integratedUsers();
//        if (users.isEmpty()) {
//            return;
//        }
//        // 从数据库获取所有用户数据
//        List<UserDomainEntity> allExistingUsers = dataIntegratedRepository.userFindAll();
//
//        // 将API返回的用户数据ID集合化，用于快速判断
//        Set<Long> apiUserIds = users.stream().map(SyncUserDTO::getId).collect(Collectors.toSet());
//        // 将现有的用户数据转换为Map，以便快速查找
//        Map<Long, UserDomainEntity> existingUserMap = allExistingUsers.stream().collect(Collectors.toMap(UserDomainEntity::getId, user -> user));
//        // 收集需要新增和更新的实体
//        List<UserDomainEntity> usersToSave = new ArrayList<>();
//        // 收集需要删除的实体（数据库有但API中没有的）
//        List<UserDomainEntity> usersToDelete = new ArrayList<>();
//        // 处理数据库中存在但API中不存在的用户（需要删除）
//        for (UserDomainEntity existingUser : allExistingUsers) {
//            if (!apiUserIds.contains(existingUser.getId())) {
//                usersToDelete.add(existingUser);
//            }
//        }
//        // 处理每个API返回的用户
//        for (SyncUserDTO userDTO : users) {
//            UserDomainEntity userEntity;
//
//            // 检查是否存在此用户
//            if (existingUserMap.containsKey(userDTO.getId())) {
//                // 已存在，获取现有实体
//                userEntity = existingUserMap.get(userDTO.getId());
//
//                // 检查更新时间，如果相同则跳过更新
//                if (userEntity.getUpdateTime() != null && userEntity.getUpdateTime().equals(userDTO.getUpdatedTime())) {
//                    continue; // 跳过此用户的处理，因为没有变化
//                }
//            } else {
//                // 不存在，创建新实体
//                userEntity = new UserDomainEntity();
//            }
//
//            // 复制基本属性
//            BeanUtils.copyProperties(userDTO, userEntity);
//            // 确保更新时间字段正确映射
//            userEntity.setUpdateTime(userDTO.getUpdatedTime());
//
//            // 添加到待保存列表
//            usersToSave.add(userEntity);
//        }
//        // 批量保存所有实体
//        if (!usersToSave.isEmpty()) {
//            dataIntegratedRepository.userSaveAll(usersToSave);
//            log.info("同步用户数据：更新/新增 {} 条记录", usersToSave.size());
//        }
//        // 批量删除不存在的实体
//        if (!usersToDelete.isEmpty()) {
//            // 使用软删除方式删除
//            dataIntegratedRepository.userDeleteAll(usersToDelete);
//            log.info("同步用户数据：删除 {} 条记录", usersToDelete.size());
//        }
//    }

	public void integratedDataChannels() {
		List<SyncChannelDTO> channels = dataSyncRepository.integratedDataChannels();
		if (channels.isEmpty()) {
			return;
		}
		// 从数据库获取所有通道数据
		List<DataChannelDomainEntity> allExistingChannels = dataIntegratedRepository.dataChannelFindAll();

		// 将API返回的通道数据ID集合化，用于快速判断
		Set<Long> apiChannelIds = channels.stream().map(SyncChannelDTO::getId).collect(Collectors.toSet());

		// 将现有的通道数据转换为Map，以便快速查找
		Map<Long, DataChannelDomainEntity> existingChannelMap = allExistingChannels.stream().collect(Collectors.toMap(DataChannelDomainEntity::getId, channel -> channel));

		// 收集需要新增和更新的实体
		List<DataChannelDomainEntity> channelsToSave = new ArrayList<>();
		// 收集需要删除的实体（数据库有但API中没有的）
		List<DataChannelDomainEntity> channelsToDelete = new ArrayList<>();

		// 处理数据库中存在但API中不存在的通道（需要删除）
		for (DataChannelDomainEntity existingChannel : allExistingChannels) {
			if (!apiChannelIds.contains(existingChannel.getId())) {
				channelsToDelete.add(existingChannel);
			}
		}

		// 处理每个API返回的通道
		for (SyncChannelDTO channelDTO : channels) {
			DataChannelDomainEntity channelEntity;

			// 检查是否存在此通道
			if (existingChannelMap.containsKey(channelDTO.getId())) {
				// 已存在，获取现有实体
				channelEntity = existingChannelMap.get(channelDTO.getId());

				// 检查更新时间，如果相同则跳过更新
				if (channelEntity.getUpdateTime() != null && channelEntity.getUpdateTime().equals(channelDTO.getUpdatedTime())) {
					continue; // 跳过此通道的处理，因为没有变化
				}
			} else {
				// 不存在，创建新实体
				channelEntity = new DataChannelDomainEntity();
			}

			// 复制基本属性
			BeanUtils.copyProperties(channelDTO, channelEntity);
			// 确保更新时间字段正确映射
			channelEntity.setCreateTime(channelDTO.getCreatedTime());
			channelEntity.setUpdateTime(channelDTO.getUpdatedTime());
			// 处理isShare字段类型转换（Integer转Boolean）
			if (channelDTO.getIsShare() != null) {
				channelEntity.setIsShare(channelDTO.getIsShare() == 1);
			}

			// 添加到待保存列表
			channelsToSave.add(channelEntity);
		}

		// 批量保存所有实体
		if (!channelsToSave.isEmpty()) {
			dataIntegratedRepository.dataChannelSaveAll(channelsToSave);
			log.info("同步通道数据：更新/新增 {} 条记录", channelsToSave.size());
		}

		// 批量删除不存在的实体
		//if (!channelsToDelete.isEmpty()) {
		//	// 使用软删除方式删除
		//	dataIntegratedRepository.dataChannelDeleteAll(channelsToDelete);
		//	log.info("同步通道数据：删除 {} 条记录", channelsToDelete.size());
		//}
	}


	public void integratedApplications() {
		List<SyncAppDTO> apps = dataSyncRepository.integratedApplications();
		if (apps.isEmpty()) {
			return;
		}
		// 从数据库获取所有应用数据
		List<AppInfoDomainEntity> allExistingApps = dataIntegratedRepository.appInfoFindAll();
		// 将API返回的应用数据ID集合化，用于快速判断
		Set<Long> apiAppIds = apps.stream()
				.map(SyncAppDTO::getId)
				.collect(Collectors.toSet());

		// 将现有的应用数据转换为Map，以便快速查找
		Map<Long, AppInfoDomainEntity> existingAppMap = allExistingApps.stream()
				.collect(Collectors.toMap(AppInfoDomainEntity::getId, app -> app));

		// 收集需要新增和更新的实体
		List<AppInfoDomainEntity> appsToSave = new ArrayList<>();
		// 收集需要删除的实体（数据库有但API中没有的）
//		List<AppInfoDomainEntity> appsToDelete = new ArrayList<>();

		// 处理数据库中存在但API中不存在的应用（需要删除）
//		for (AppInfoDomainEntity existingApp : allExistingApps) {
//			if (!apiAppIds.contains(existingApp.getId())) {
//				appsToDelete.add(existingApp);
//			}
//		}

		// 处理每个API返回的应用
		for (SyncAppDTO appDTO : apps) {
			AppInfoDomainEntity appEntity;

			// 检查是否存在此应用
			if (existingAppMap.containsKey(appDTO.getId())) {
				// 已存在，获取现有实体
				appEntity = existingAppMap.get(appDTO.getId());

				// 检查更新时间，如果相同则跳过更新
				if (appEntity.getUpdateTime() != null &&
						appEntity.getUpdateTime().equals(appDTO.getUpdatedTime())) {
					continue; // 跳过此应用的处理，因为没有变化
				}
			} else {
				// 不存在，创建新实体
				appEntity = new AppInfoDomainEntity();
			}

			// 复制基本属性
			BeanUtils.copyProperties(appDTO, appEntity);
			// 确保更新时间字段正确映射
			appEntity.setUpdateTime(appDTO.getUpdatedTime());
			appEntity.setCreateTime(appDTO.getCreatedTime());

			// 添加到待保存列表
			appsToSave.add(appEntity);
		}

		// 批量保存所有实体
		if (!appsToSave.isEmpty()) {
			dataIntegratedRepository.appInfoSaveAll(appsToSave);
			log.info("同步应用数据：更新/新增 {} 条记录", appsToSave.size());
		}

		// 批量删除不存在的实体
		//if (!appsToDelete.isEmpty()) {
		//	// 使用软删除方式删除
		//	dataIntegratedRepository.appInfoDeleteAll(appsToDelete);
		//	log.info("同步应用数据：删除 {} 条记录", appsToDelete.size());
		//}
	}


	public void integratedHandles() {
		log.info("开始标识同步");
		// 记录此次同步所有处理过的ID，用于最后的删除操作
//		Set<Long> processedHandleIds = new HashSet<>();
//		Set<Long> processedItemIds = new HashSet<>();
//		Set<Long> processedReferenceIds = new HashSet<>();

		LocalDateTime updatedTime = dataIntegratedRepository.handleFindMaxUpdatedTime();
		int page = 0;
		int size = 1000;
		int totalPage = 0;
		boolean hasMoreData = true;
		// 使用标记来表示是否是最后一批数据
		boolean isLastPage = false;
		while (hasMoreData) {
			HandlePageDTO handlePageDTO = dataSyncRepository.integratedHandles(page, size, updatedTime);
			if (Objects.isNull(handlePageDTO)) {
				if (page >= totalPage) {
					hasMoreData = false;
				}
				page++;
				continue;
			}
			totalPage = handlePageDTO.getTotalPage();
			log.info("总页数：{}", totalPage);
			log.info("总条数：{}", handlePageDTO.getTotalCount());
			log.info("当前处理页数{}", page+1);
			List<SyncHandleDTO> handles = handlePageDTO.getContent();
			// 检查是否是最后一页数据
			if (handles.size() < size) {
				isLastPage = true;
			}

			// 获取所有待处理的ID列表
			List<Long> handleIds = handles.stream().map(SyncHandleDTO::getId).toList();

			// 一次性从数据库获取所有现有的Handle数据
			List<HandleDomainEntity> existingHandles = dataIntegratedRepository.handleFindAllById(handleIds);

			// 将现有的Handle数据转换为Map，以便快速查找
			Map<Long, HandleDomainEntity> existingHandleMap = existingHandles.stream()
					.collect(Collectors.toMap(HandleDomainEntity::getId, handle -> handle));

			// 收集需要新增和更新的实体
			List<HandleDomainEntity> handlesToSave = new ArrayList<>();
			List<HandleItemDomainEntity> itemsToSave = new ArrayList<>();
			List<HandleReferenceDomainEntity> referencesToSave = new ArrayList<>();

			// 处理每个Handle
			for (SyncHandleDTO handleDTO : handles) {
				// 记录所有处理过的ID
//				processedHandleIds.add(handleDTO.getId());

				HandleDomainEntity handleEntity;
				boolean isNew = false;

				// 检查是否存在此Handle
				if (existingHandleMap.containsKey(handleDTO.getId())) {
					// 已存在，获取现有实体
					handleEntity = existingHandleMap.get(handleDTO.getId());

					// 检查更新时间，如果相同则跳过更新
					if (handleEntity.getUpdateTime() != null &&
							handleEntity.getUpdateTime().equals(handleDTO.getUpdatedTime())) {
						continue; // 跳过此Handle的处理，因为没有变化
					}
				} else {
					// 不存在，创建新实体
					handleEntity = new HandleDomainEntity();
					isNew = true;
				}

				// 复制基本属性
				BeanUtils.copyProperties(handleDTO, handleEntity);
				// 确保更新时间字段正确映射
				handleEntity.setUpdateTime(handleDTO.getUpdatedTime());
				handleEntity.setCreateTime(handleDTO.getCreatedTime());

				// 添加到待保存列表
				handlesToSave.add(handleEntity);

				// 处理HandleItem
				List<HandleItemDTO> handleItemDTOs = handleDTO.getHandleItems();
				if (handleItemDTOs != null && !handleItemDTOs.isEmpty()) {
					// 获取所有Item的ID
					List<Long> itemIds = handleItemDTOs.stream().map(HandleItemDTO::getId).toList();
					// 记录所有处理过的ItemID
//					processedItemIds.addAll(itemIds);

					// 如果是新Handle或需要更新，则处理其所有HandleItem
					Map<Long, HandleItemDomainEntity> existingItemsMap = new HashMap<>();

					// 如果不是新Handle，则获取现有的HandleItem
					if (!isNew) {
						List<HandleItemDomainEntity> existingItems = dataIntegratedRepository.handleItemFindAllById(itemIds);
						existingItemsMap = existingItems.stream()
								.collect(Collectors.toMap(HandleItemDomainEntity::getId, item -> item));
					}

					// 处理每个HandleItem
					for (HandleItemDTO itemDTO : handleItemDTOs) {
						HandleItemDomainEntity itemEntity;
						boolean isNewItem = false;

						// 检查是否存在此HandleItem
						if (existingItemsMap.containsKey(itemDTO.getId())) {
							// 已存在，获取现有实体
							itemEntity = existingItemsMap.get(itemDTO.getId());

							// 检查更新时间，如果相同则跳过更新
							if (itemEntity.getUpdateTime() != null &&
									itemEntity.getUpdateTime().equals(itemDTO.getUpdatedTime())) {
								continue; // 跳过此Item的处理，因为没有变化
							}
						} else {
							// 不存在，创建新实体
							itemEntity = new HandleItemDomainEntity();
							isNewItem = true;
						}

						// 复制基本属性
						BeanUtils.copyProperties(itemDTO, itemEntity);
						// 确保更新时间字段正确映射
						itemEntity.setUpdateTime(itemDTO.getUpdatedTime());
						itemEntity.setCreateTime(itemDTO.getCreatedTime());
						// 确保HandleId正确设置
						itemEntity.setHandleId(handleDTO.getId());

						// 添加到待保存列表
						itemsToSave.add(itemEntity);

						// 处理HandleReference
						List<HandleReferenceDTO> referenceDTOs = itemDTO.getReferences();
						if (referenceDTOs != null && !referenceDTOs.isEmpty()) {
							// 获取所有Reference的ID
							List<Long> referenceIds = referenceDTOs.stream().map(HandleReferenceDTO::getId)
									.toList();
							// 记录所有处理过的ReferenceID
//							processedReferenceIds.addAll(referenceIds);

							// 如果是新HandleItem或需要更新，则处理其所有HandleReference
							Map<Long, HandleReferenceDomainEntity> existingRefsMap = new HashMap<>();

							// 如果不是新HandleItem，则获取现有的HandleReference
							if (!isNewItem) {
								List<HandleReferenceDomainEntity> existingRefs = dataIntegratedRepository.handleReferenceFindAllById(referenceIds);
								existingRefsMap = existingRefs.stream()
										.collect(Collectors.toMap(HandleReferenceDomainEntity::getId, ref -> ref));
							}

							// 处理每个HandleReference
							for (HandleReferenceDTO refDTO : referenceDTOs) {
								HandleReferenceDomainEntity refEntity;

								// 检查是否存在此HandleReference
								if (existingRefsMap.containsKey(refDTO.getId())) {
									// 已存在，获取现有实体
									refEntity = existingRefsMap.get(refDTO.getId());

									// 检查更新时间，如果相同则跳过更新
									if (refEntity.getUpdateTime() != null &&
											refEntity.getUpdateTime().equals(refDTO.getUpdatedTime())) {
										continue; // 跳过此Reference的处理，因为没有变化
									}
								} else {
									// 不存在，创建新实体
									refEntity = new HandleReferenceDomainEntity();
								}

								// 复制基本属性
								BeanUtils.copyProperties(refDTO, refEntity);
								// 确保更新时间字段正确映射
								refEntity.setUpdateTime(refDTO.getUpdatedTime());
								refEntity.setCreateTime(refDTO.getCreatedTime());
								// 确保HandleItemId正确设置
								refEntity.setHandleItemId(itemDTO.getId());

								// 添加到待保存列表
								referencesToSave.add(refEntity);
							}
						}
					}
				}
			}

			// 批量保存所有实体
			if (!handlesToSave.isEmpty()) {
				dataIntegratedRepository.handleSaveAll(handlesToSave);
				log.info("同步标识数据：更新/新增Handle {} 条记录", handlesToSave.size());
			}
			if (!itemsToSave.isEmpty()) {
				dataIntegratedRepository.handleItemSaveAll(itemsToSave);
				log.info("同步标识数据：更新/新增HandleItem {} 条记录", itemsToSave.size());
			}
			if (!referencesToSave.isEmpty()) {
				dataIntegratedRepository.handleReferenceSaveAll(referencesToSave);
				log.info("同步标识数据：更新/新增HandleReference {} 条记录", referencesToSave.size());
			}

			// 如果是最后一页或者返回的数据少于请求的大小，说明已经没有更多数据了
			if (isLastPage) {
				hasMoreData = false;

				// 在处理完最后一页后，执行删除操作
//				cleanupDeletedEntities(processedHandleIds, processedItemIds, processedReferenceIds);
			} else {
				// 增加页码，准备获取下一页数据
				page++;
			}


		}
	}


	/**
	 * 清理已删除的实体
	 * 删除数据库中存在但API中不存在（未被处理）的实体
	 */
	private void cleanupDeletedEntities(Set<Long> processedHandleIds, Set<Long> processedItemIds,
										Set<Long> processedReferenceIds) {
		// 1. 处理HandleReference
		List<Long> allReferences = dataIntegratedRepository.handleReferenceFindIdBy();
		List<Long> referencesToDelete = allReferences.stream()
				.filter(id -> !processedReferenceIds.contains(id))
				.collect(Collectors.toList());

		if (!referencesToDelete.isEmpty()) {
			dataIntegratedRepository.handleReferenceDeleteAllById(referencesToDelete);
			log.info("同步标识数据：删除 HandleReference {} 条记录", referencesToDelete.size());
		}

		// 2. 处理HandleItem
		List<Long> allItems = dataIntegratedRepository.handleItemFindIdBy();
		List<Long> itemsToDelete = allItems.stream()
				.filter(id -> !processedItemIds.contains(id))
				.collect(Collectors.toList());

		if (!itemsToDelete.isEmpty()) {
			dataIntegratedRepository.handleItemDeleteAllById(itemsToDelete);
			log.info("同步标识数据：删除 HandleItem {} 条记录", itemsToDelete.size());
		}

		// 3. 处理Handle
		List<Long> allHandles = dataIntegratedRepository.handleFindIdBy();
		List<Long> handlesToDelete = allHandles.stream()
				.filter(id -> !processedHandleIds.contains(id))
				.toList();

		if (!handlesToDelete.isEmpty()) {
			dataIntegratedRepository.handleDeleteAllById(handlesToDelete);
			log.info("同步标识数据：删除 Handle {} 条记录", handlesToDelete.size());
		}
	}


}
