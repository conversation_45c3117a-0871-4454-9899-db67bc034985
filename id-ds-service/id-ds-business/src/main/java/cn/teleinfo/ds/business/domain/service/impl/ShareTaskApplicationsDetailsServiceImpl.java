package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplications;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.Cart;
import cn.teleinfo.ds.business.domain.repository.ShareTaskApplicationsDetailsRepository;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsDetailsService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareTaskApplicationsDetailsJpaRepository;
import cn.teleinfo.ds.business.interfaces.dto.request.CartRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ShareTaskApplicationsDetailsServiceImpl implements ShareTaskApplicationsDetailsService {
	private final ShareTaskApplicationsDetailsRepository ShareTaskApplicationsDetailsRepository;

	@Override
	public Cart setCartObject(CartRequest request) {
		return new ShareTaskApplications().setCartObject(request);
	}

	@Override
	public ShareTaskApplicationsDetailsDomainEntity findById(Long id) {
		return ShareTaskApplicationsDetailsRepository.findById(id);
	}
}
