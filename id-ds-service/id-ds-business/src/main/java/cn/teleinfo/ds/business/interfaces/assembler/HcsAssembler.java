package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.interfaces.dto.response.FindCdmClustersResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindCdmConnectionsResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindDasInstancesResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindDasWorkspacesResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindDayuConnectionsDatabasesResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindDayuConnectionsResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.FindProjectsResponse;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigCommodityOrder;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigDataSourceView;
import com.huaweicloud.sdk.dataartsstudio.v1.model.DatabasesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.Workspacebody;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class HcsAssembler {

	// 资源空间
	public List<FindProjectsResponse> toFindProjectsResponse(List<AuthProjectResult> request) {
		return request.stream().map(p -> {
			var name = "";
			if (StrUtil.isNotBlank(p.getDescription())) {
				name = StrUtil.format("{} ({})", p.getDescription(), p.getName());
			} else {
				name = p.getName();
			}

			return new FindProjectsResponse(p.getId(), name);
		}).toList();
	}


	// 数据治理中心实例
	public List<FindDasInstancesResponse> toFindDasInstancesResponse(List<ApigCommodityOrder> request) {
		return BeanUtil.copyToList(request, FindDasInstancesResponse.class);
	}

	// 工作空间列表
	public List<FindDasWorkspacesResponse> toFindDasWorkspaces(List<Workspacebody> request) {
		return BeanUtil.copyToList(request, FindDasWorkspacesResponse.class);
	}

	// 获取CDM集群名称列表
	public List<FindCdmClustersResponse> toFindCdmClusters(List<Clusters> request) {
		return BeanUtil.copyToList(request, FindCdmClustersResponse.class);
	}

	// 数据开发-规范层连接名称
	public List<FindDayuConnectionsResponse> toFindDayuConnections(List<ApigDataSourceView> request) {
		return BeanUtil.copyToList(request, FindDayuConnectionsResponse.class);
	}

	// 获取数据集成-连接名称列表
	public List<FindCdmConnectionsResponse> toFindCdmConnections(List<Links> request) {
		 return BeanUtil.copyToList(request, FindCdmConnectionsResponse.class);
	}

	// 数据开发-规范层数据库名称
	public List<FindDayuConnectionsDatabasesResponse> toFindDayuConnectionsDatabases(List<DatabasesList> request) {
		return BeanUtil.copyToList(request, FindDayuConnectionsDatabasesResponse.class);
	}
}
