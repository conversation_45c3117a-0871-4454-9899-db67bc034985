package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.teleinfo.ds.business.domain.repository.HandleItemRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleItemView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.HandleItemJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@AllArgsConstructor
@Component
public class HandleItemRepositoryImpl implements HandleItemRepository {

	private HandleItemJpaRepository handleItemJpaRepository;

	@Override
	public List<HandleItemView> findAllByHandle(String handle) {
		return handleItemJpaRepository.findAllByHandle(handle);
	}

}
