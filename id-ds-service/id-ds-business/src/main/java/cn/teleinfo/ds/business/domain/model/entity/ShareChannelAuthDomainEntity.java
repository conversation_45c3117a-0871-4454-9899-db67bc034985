package cn.teleinfo.ds.business.domain.model.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.sql.Timestamp;

@Data
public class ShareChannelAuthDomainEntity extends BaseEntity {

	/**
	 * 共享通道ID
	 */
	private String shareChannelId;

	/**
	 * 任务状态(1:申请中 2:已驳回 3:已授权)
	 */
	private Integer channelStatus;

	/**
	 * 审核人ID
	 */
	private Long auditUserId;

	/**
	 * 审核备注
	 */
	private String auditRemark;

	/**
	 * 0:否, NULL:是
	 */
	private Integer isDeleted;

	/**
	 * 创建时间
	 */
	private Timestamp createdTime;

	/**
	 * 更新时间
	 */
	private Timestamp updatedTime;

	/**
	 * 主版本号
	 */
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	private Integer minorVersion;

}
