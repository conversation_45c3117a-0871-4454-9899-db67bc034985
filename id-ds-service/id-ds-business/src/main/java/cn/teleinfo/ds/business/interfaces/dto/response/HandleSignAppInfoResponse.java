package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class HandleSignAppInfoResponse {

	/**
	 * id
	 */
	private Long id;

	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 所属企业
	 */
	private String entName;

	/**
	 * 应用前缀
	 */
	private String handlePrefix;

	/**
	 * 应用身份标识
	 */
	private String handleCode;

	/**
	 * 部署地址
	 */
	private String deployAddress;

	/**
	 * 系统版本
	 */
	private String sysVersion;

	/**
	 * 更新时间
	 */
	private Timestamp updatedTime;

}
