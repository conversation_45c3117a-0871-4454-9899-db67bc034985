package cn.teleinfo.ds.business.interfaces.assembler;

import cn.teleinfo.ds.business.application.query.DataChannelsListQuery;
import cn.teleinfo.ds.business.interfaces.dto.response.ListDataChannelsResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
public class DataChannelsAssemble {

	public DataChannelsListQuery toQuery(ListDataChannelsResponse response) {
		DataChannelsListQuery query = new DataChannelsListQuery();
		BeanUtils.copyProperties(response, query);
		return query;
	}
}
