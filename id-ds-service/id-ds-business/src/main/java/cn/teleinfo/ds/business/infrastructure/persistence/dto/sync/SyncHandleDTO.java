package cn.teleinfo.ds.business.infrastructure.persistence.dto.sync;


import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SyncHandleDTO {
	/**
	 * 应用id
	 */
	private String appHandleCode;
	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;
	/**
	 * 实体类型 1业务实体 2资源实体
	 */
	private Integer entityType;
	/**
	 * 企业前缀
	 */
	private String entPrefix;
	/**
	 * 标识
	 */
	private String handle;
	/**
	 * 标识属性列表
	 */
	private List<HandleItemDTO> handleItems;
	/**
	 * 主键ID
	 */
	private Long id;
	/**
	 * 0:否, NULL:是
	 */
	private Integer isDeleted;
	/**
	 * 标识名称
	 */
	private String name;
	/**
	 * 省级ID
	 */
	private String provincePrefix;
	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;
	/**
	 * 通配符
	 */
	private String wildcard;
}
