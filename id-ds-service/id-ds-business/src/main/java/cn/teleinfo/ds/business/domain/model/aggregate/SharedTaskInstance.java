package cn.teleinfo.ds.business.domain.model.aggregate;


import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.common.security.service.PigUser;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
public class SharedTaskInstance {
	private final SharedTaskDomainEntity sharedTask;
	private final ExecutionType execType;

	// 主任务实例
	private SharedTaskInstanceDomainEntity taskInstance;

	public SharedTaskInstance(SharedTaskDomainEntity sharedTask, ExecutionType execType) {
		this.execType = execType;
		this.sharedTask = sharedTask;
	}

	// 初始化任务执行
	public void initTaskInstance(LocalDateTime runTime, String logPath) {
		this.taskInstance = new SharedTaskInstanceDomainEntity();
		this.taskInstance.setSharedTaskId(this.sharedTask.getId());
		this.taskInstance.setTaskInstanceNo(UUID.randomUUID().toString().toUpperCase());
		this.taskInstance.setTaskName(this.sharedTask.getTaskName());
		this.taskInstance.setTaskNo(this.sharedTask.getTaskNo());
		this.taskInstance.setTaskType(this.sharedTask.getTaskType());
		this.taskInstance.setTaskStatus(this.sharedTask.getTaskStatus());
		this.taskInstance.setExecutionType(this.execType.code());
		this.taskInstance.setRunStatus(RunStatus.RUN.code());
		this.taskInstance.setTargetSourceId(this.sharedTask.getTargetSourceId());
		this.taskInstance.setDatabaseName(this.sharedTask.getDatabaseName());
		this.taskInstance.setAppHandleCode(this.sharedTask.getAppHandleCode());
		this.taskInstance.setEntPrefix(this.sharedTask.getEntPrefix());
		this.taskInstance.setCronExpression(this.sharedTask.getCronExpression());
		this.taskInstance.setRunTime(runTime);
		this.taskInstance.setRunDuration(0);
		this.taskInstance.setSharedDataCount(0);
		this.taskInstance.setLogPath(logPath);

		PigUser user = SecurityUtils.getUser();
		if (user == null) {
			this.taskInstance.setOperator("--");
		} else {
			this.taskInstance.setOperator(user.getId() + "");
		}

	}
}
