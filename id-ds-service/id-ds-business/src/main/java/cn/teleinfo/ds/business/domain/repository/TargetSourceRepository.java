package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.TargetSourceView;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.time.LocalDateTime;
import java.util.List;

public interface TargetSourceRepository {

	Long save(TargetSourceDomainEntity targetSourceDomainEntity);

	/**
	 * 查询应用下目标源名称是否存在
	 *
	 * @param appHandleCode    app handle code
	 * @param targetSourceName 目标源名称
	 * @return > 0 存在
	 */
	Integer findByAppHandleCodeAndTargetSourceNameCount(String appHandleCode, String targetSourceName);

	PageResponse<TargetSourceDomainEntity> listTargetSource(String appHandleCode,
															String targetSourceName,
															Integer platformType,
															List<String> userHandleCode,
															LocalDateTime start,
															LocalDateTime end,
															Integer page,
															Integer size
	);

	TargetSourceDomainEntity findById(Long id);

	TargetSourceView queryTargetSourceDetail(String id);

	void delTargetSource(Long id);

	List<TargetSourceDomainEntity> findAllByAppHandleCodeAndTargetSourceName(String appHandleCode, String targetSourceName);

	List<TargetSourceDomainEntity> findByAppHandleCode(String appHandleCode);
}
