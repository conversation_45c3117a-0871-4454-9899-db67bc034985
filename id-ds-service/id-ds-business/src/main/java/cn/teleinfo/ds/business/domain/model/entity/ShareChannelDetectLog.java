package cn.teleinfo.ds.business.domain.model.entity;

import lombok.Data;


import java.time.Instant;
import java.time.LocalDateTime;

@Data
public class ShareChannelDetectLog {

	private Long id;

	private Long shareChannelId;

	private Integer connectTest;

	private Integer grammarCheck;

	private Integer execTest;

	private String logPath;

	private Integer mainVersion;

	private Integer minorVersion;

	private LocalDateTime createTime;

	private LocalDateTime updateTime;

	private Integer isDeleted;

	private String log;

}