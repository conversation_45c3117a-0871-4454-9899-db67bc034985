package cn.teleinfo.ds.business.interfaces.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateShareDataSourcesRequest {
	/**
	 * 平台类型
	 * t_connection platform_type
	 */
	@NotNull(message = "平台类型 不能为空")
	private Integer platformType;

	/**
	 * 共享源信息
	 */
	@NotNull(message = "共享源信息不能为空")
	private ShareDataSourcesItemsRequest items;
}
