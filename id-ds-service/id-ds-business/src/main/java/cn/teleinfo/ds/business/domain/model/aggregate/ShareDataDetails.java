package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.teleinfo.ds.business.domain.model.valueobject.HandleItemValue;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.HandleItemView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareDataDetails {

	/**
	 * 基础属性
	 */
	List<HandleItemValue> items;
	/**
	 * 扩展属性
	 */
	List<HandleItemValue> extendItems;
	/**
	 * id
	 */
	private String id;
	/**
	 * 标识名
	 */
	private String name;
	/**
	 * 标识
	 */
	private String handle;
	/**
	 * 省级名称
	 */
	private String provinceName;
	/**
	 * 企业名称
	 */
	private String entName;
	/**
	 * 应用名称
	 */
	private String appName;

	public ShareDataDetails(SharedTaskView sharedTaskView, List<HandleItemView> items,
			List<HandleItemView> extendItems) {
		this.id = sharedTaskView.getId();
		this.name = sharedTaskView.getName();
		this.handle = sharedTaskView.getHandle();
		this.provinceName = sharedTaskView.getProvinceName();
		this.entName = sharedTaskView.getEntName();
		this.appName = sharedTaskView.getAppName();
		this.items = new ArrayList<>();
		this.extendItems = new ArrayList<>();
		if (items != null) {
			for (HandleItemView item : items) {
				HandleItemValue handleItemValue = new HandleItemValue();
				handleItemValue.setField(item.getField());
				handleItemValue.setDescription(item.getDescription());
				handleItemValue.setFieldType(item.getFieldType());
				handleItemValue.setFieldValue(item.getFieldValue());
				handleItemValue.setRemark(item.getRemark());
				this.items.add(handleItemValue);
			}
		}
		if (extendItems != null) {
			for (HandleItemView extendItem : extendItems) {
				HandleItemValue handleItemValue = new HandleItemValue();
				handleItemValue.setField(extendItem.getField());
				handleItemValue.setDescription(extendItem.getDescription());
				handleItemValue.setFieldType(extendItem.getFieldType());
				handleItemValue.setFieldValue(extendItem.getFieldValue());
				handleItemValue.setRemark(extendItem.getRemark());
				this.extendItems.add(handleItemValue);
			}
		}
	}

}
