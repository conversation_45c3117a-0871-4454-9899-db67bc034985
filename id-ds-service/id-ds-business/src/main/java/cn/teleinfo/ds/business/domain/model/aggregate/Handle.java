package cn.teleinfo.ds.business.domain.model.aggregate;


import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

// 聚合根
@Data
@NoArgsConstructor
public class Handle {

	/**
	 * 对象标识领域实体
	 */
	private HandleDomainEntity handleDomainEntity;

	/**
	 * 属性
	 */
	private List<HandleItemDomainEntity> handleItemModelEntities;

	public Handle(HandleDomainEntity handleDomainEntity) {
		this.handleDomainEntity = handleDomainEntity;
	}

	public Handle(HandleDomainEntity handleDomainEntity, List<HandleItemDomainEntity> handleItemModelEntities) {
		this.handleDomainEntity = handleDomainEntity;
		this.handleItemModelEntities = handleItemModelEntities;
	}


}
