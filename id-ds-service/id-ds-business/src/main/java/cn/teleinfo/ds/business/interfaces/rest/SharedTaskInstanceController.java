package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.application.service.SharedTaskInstanceAppService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.interfaces.assembler.SharedTaskInstanceAssemble;
import cn.teleinfo.ds.business.interfaces.dto.request.ListSharedTaskInstancesRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceDetailResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 共享任务执行记录
 */
@RestController
@RequestMapping("/shared-task-instances")
@AllArgsConstructor
public class SharedTaskInstanceController {

	private final SharedTaskInstanceAssemble assemble;

	private final SharedTaskInstanceAppService service;

	/**
	 * 查询任务执行记录列表
	 *
	 * @param request 查询条件
	 * @return 任务执行记录列表
	 */
	@GetMapping
	public R<PageResponse<SharedTaskInstanceListView>> listSharedTaskInstances(@Valid ListSharedTaskInstancesRequest request) {
		var query = assemble.toListQuery(request);
		return R.ok(service.listSharedTaskInstances(query));
	}


	/**
	 * 查询任务执行记录详情
	 * @param instanceId 任务执行记录ID
	 * @return 任务执行记录详情
	 */
	@GetMapping("/{instanceId}")
	public R<SharedTaskInstanceDetailResponse> getShareTaskInstanceDetail(@PathVariable Long instanceId) {
		return R.ok(service.getShareTaskInstanceDetail(instanceId));
	}

	@GetMapping("/handle/{sharedTaskId}")
	public R<HandleApplicationResponse> getHandleItemsByHandle(@PathVariable Long sharedTaskId) {
		return R.ok(service.getHandleItemsByHandle(sharedTaskId));
	}

}
