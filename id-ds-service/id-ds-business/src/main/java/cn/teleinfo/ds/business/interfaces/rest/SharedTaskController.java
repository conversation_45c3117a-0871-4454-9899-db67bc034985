package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.query.ListSharedTaskQuery;
import cn.teleinfo.ds.business.application.service.SharedTaskApplicationService;
import cn.teleinfo.ds.business.domain.model.entity.ExecutionType;
import cn.teleinfo.ds.business.interfaces.dto.request.ExecRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedDataDetailsResponse;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskStatusView;
import cn.teleinfo.ds.business.interfaces.assembler.SharedTaskAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.ListSharedTaskRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ListSharedTaskResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskDetailResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.security.annotation.Inner;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PutMapping;

import java.util.List;

/**
 * 共享任务
 */
@Slf4j
@RestController
@RequestMapping("/shared-tasks")
@AllArgsConstructor
public class SharedTaskController {

	private final SharedTaskAssembler sharedTaskAssembler;
	private final SharedTaskApplicationService sharedTaskApplicationService;

	/**
	 * 查询任务列表
	 *
	 * @return 任务列表
	 */
	@GetMapping
	public R<PageResponse<ListSharedTaskResponse>> listSharedTask(ListSharedTaskRequest request) {
		ListSharedTaskQuery query = sharedTaskAssembler.toListSharedTaskQuery(request);
		return R.ok(sharedTaskAssembler.toListSharedTaskResponse(sharedTaskApplicationService.listSharedTask(query)));
	}

	/**
	 * 查询任务详情
	 *
	 * @param id 任务ID
	 * @return 任务详情
	 */
	@GetMapping("{id}")
	public R<SharedTaskDetailResponse> getSharedTask(@PathVariable("id") Long id) {
		return R.ok(sharedTaskAssembler.toSharedTaskDetailResponse(sharedTaskApplicationService.getSharedTaskDetail(id)));
	}

	/**
	 * 共享任务启用停用
	 *
	 * @param id id
	 * @return
	 */
	@PutMapping("{id}")
	public R updateSharedTaskStatus(@PathVariable("id") Long id) {
		sharedTaskApplicationService.updateSharedTaskStatus(id);
		return R.ok();
	}

	/**
	 * 执行任务
	 *
	 * @param id 任务ID
	 * @return 结果
	 */
	@Inner
	@PostMapping("/execute")
	public R execute(@RequestParam("id") Long id) {
		sharedTaskApplicationService.execute(id, ExecutionType.FORMAL.code());
		return R.ok();
	}

	/**
	 * 共享任务手动执行
	 *
	 * @param id
	 */
	@PostMapping("/{id}/instances")
	public R exec(@PathVariable("id") Long id, @RequestBody ExecRequest request) {
		sharedTaskApplicationService.execute(id, request.getExecutionType());
		return R.ok();
	}


	/**
	 * 删除共享任务
	 *
	 * @param id 任务ID
	 * @return 删除结果
	 */
	@DeleteMapping("/{id}")
	public R deleteSharedTask(@PathVariable Long id) {
		log.info("delete shared task id={}", id);
		sharedTaskApplicationService.deleteSharedTaskId(id);
		return R.ok();
	}

	/**
	 * 查看测试任务执行状态
	 *
	 * @param id 共享任务ID
	 * @return 任务执行详情
	 */
	@GetMapping("/instances/{id}/sub-tasks-test")
	public R<List<SharedTaskStatusView>> getSharedTaskTestStatus(@PathVariable("id") Long id) {
		return R.ok(sharedTaskApplicationService.getSharedTaskStatus(id, "test"));
	}

	/**
	 * 查看执行任务执行状态
	 *
	 * @param id 共享任务ID
	 * @return 任务执行详情
	 */
	@GetMapping("/instances/{id}/sub-tasks-execute")
	public R<List<SharedTaskStatusView>> getSharedTaskExecuteStatus(@PathVariable("id") Long id) {
		return R.ok(sharedTaskApplicationService.getSharedTaskStatus(id, "execute"));
	}

	/**
	 * 共享任务数据详情
	 *
	 * @param dataId 共享任务明细ID
	 * @return 任务执行详情
	 */
	@GetMapping("/share-data/{dataId}")
	public R<SharedDataDetailsResponse> getSharedDataDetails(@PathVariable("dataId") Long dataId) {
		var sharedDataDetails = sharedTaskApplicationService.getSharedDataDetails(dataId);
		return R.ok(sharedTaskAssembler.toSharedDataDetailsResponse(sharedDataDetails));
	}

}
