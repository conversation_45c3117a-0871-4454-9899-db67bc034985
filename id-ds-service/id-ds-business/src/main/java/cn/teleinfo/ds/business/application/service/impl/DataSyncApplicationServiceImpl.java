package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.service.DataSyncApplicationService;
import cn.teleinfo.ds.business.domain.service.DataIntegratedDomainService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class DataSyncApplicationServiceImpl implements DataSyncApplicationService {

	private final DataIntegratedDomainService dataIntegratedDomainService;

//	@Override
//	public void integratedUsers() {
//		dataIntegratedDomainService.integratedUsers();
//
//	}

	@Override
	public void integratedDataChannels() {
		dataIntegratedDomainService.integratedDataChannels();
	}

	@Override
	public void integratedApplications() {
		dataIntegratedDomainService.integratedApplications();
	}

	@Override
	public void integratedHandles() {
		dataIntegratedDomainService.integratedHandles();
	}
}
