package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

/**
 * 共享源信息表
 */
@Getter
@Setter
@Entity
@Table(name = "t_share_source")
@SQLDelete(sql = "update t_share_source set is_deleted = null where id = ?")
//@SQLDeleteAll(sql = "update t_share_source set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class ShareSourceEntity extends AuditableEntity {

	/**
	 * 平台类型 0 华为 1 阿里 2 自建
	 */
	@Column(name = "platform_type", nullable = false)
	private Integer platformType;

	/**
	 * 连接状态 0 异常 1 正常
	 */
	@Column(name = "conn_state", nullable = false)
	private Integer connState;

    /**
     * 应用标识
     */
    @Column(name = "app_handle_code", nullable = false)
    private String appHandleCode;

    /**
     * 配置内容
     */
    @Column(name = "items", columnDefinition = "text")
    private String items;

} 