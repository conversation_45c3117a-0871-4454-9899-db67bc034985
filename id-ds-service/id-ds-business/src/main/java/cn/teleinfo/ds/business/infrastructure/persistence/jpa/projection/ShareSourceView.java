package cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection;

import java.time.LocalDateTime;

public interface ShareSourceView {
	/**
	 * 主键ID
	 */
	Long getId();

	/**
	 * 平台类型 0 华为 1 阿里 2 自建
	 */
	Integer getPlatformType();

	/**
	 * 连接状态
	 */
	Integer getConnState();

	/**
	 * 应用标识
	 */
	String getAppHandleCode();

	/**
	 * 应用名称
	 */
	String getAppName();

	/**
	 * 更新人ID（
	 */
	Long getUpdateBy();

	/**
	 * 创建时间
	 */
	LocalDateTime getCreateTime();

	/**
	 * 更新时间
	 */
	LocalDateTime getUpdateTime();

	/**
	 * 更新人姓名
	 */
	String getUpdateByName();
}
