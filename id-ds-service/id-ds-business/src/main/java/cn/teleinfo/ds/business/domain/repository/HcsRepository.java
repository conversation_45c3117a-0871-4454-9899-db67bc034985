package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmJobResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmJobStatusResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmLinkResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CreateJobTableDTO;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.HcsBaseResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ListScriptResultsResponseDTO;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ScriptInfoDTO;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.CreateJobResponse;
import com.huaweicloud.sdk.cdm.v1.model.CreateLinkResponse;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.cdm.v1.model.ShowLinkResponse;
import com.huaweicloud.sdk.cdm.v1.model.Submission;
import com.huaweicloud.sdk.cdm.v1.model.UpdateLinkResponse;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigCommodityOrder;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigDataSourceView;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ColumnsList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.DatabasesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.TablesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.Workspacebody;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;

import java.util.List;
import java.util.Map;

public interface HcsRepository {

	/**
	 * 数据集成-规范层连接名称
	 */
	List<Links> findCdmConnections(String ak, String sk, String projectId, List<String> endpoints, String clusterId);

	/**
	 * 数据开发-规范层连接名称
	 */
	List<ApigDataSourceView> findDayuConnections(String ak, String sk, String projectId,
												 List<String> endpoints, String workspace, Integer offset, Integer limit);

	/**
	 * 数据开发-规范层数据库名称
	 */
	List<DatabasesList> findDayuConnectionsDatabases(String ak, String sk, String projectId,
													 List<String> endpoints, String workspace, String connectionId, Integer offset, Integer limit);

	/**
	 * 获取资源空间列表
	 */
	List<AuthProjectResult> findProjects(String ak, String sk, List<String> endpoints);

	/**
	 * 获取数据治理中心实例列表
	 */
	List<ApigCommodityOrder> findDasInstances(String ak, String sk, String projectId,
											  List<String> endpoints, Integer offset, Integer limit);

	/**
	 * 获取工作空间列表
	 */
	List<Workspacebody> findDasWorkspaces(String ak, String sk, String projectId, String instanceId,
										  List<String> endpoints, Integer offset, Integer limit);

	/**
	 * 获取CDM集群名称列表
	 */
	List<Clusters> findCdmClusters(String ak, String sk, String projectId, List<String> endpoints);

	/**
	 * 获取作业列表
	 */
	List<Job> findJobs(String ak, String sk, String projectId, List<String> endpoints, String clusterId);

	/**
	 * 创建脚本
	 */
	void createScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String scriptContent, String workspace, String dataBaseName, String connectionName);

	/**
	 * 查询脚本
	 */
	ScriptInfo findScript(String ak, String sk, String projectId, List<String> endpoints, String workspace, String scriptName);

	/**
	 * 查询所有脚本
	 */
	ScriptInfoDTO findScriptList(String ak, String sk, String projectId, List<String> endpoints, String workspace, Integer limit, Integer offset);


	/**
	 * 删除脚本
	 */
	void deleteScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String workspace);

	/**
	 * 执行脚本
	 */
	String executeScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String workspace);

	/**
	 * 查询脚本实例执行结果
	 */
	ListScriptResultsResponseDTO listScriptResults(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String workspace, String instanceId);

	/**
	 * 获取数据源中的表
	 */
	List<TablesList> listTables(String ak, String sk, String projectId, List<String> endpoints, String connectionId, String databaseName, String tableName, String workSpace);

	/**
	 * 获取表的字段
	 */
	List<ColumnsList> listTableColumns(String ak, String sk, String projectId, List<String> endpoints, String connectionId, String workSpace, String tableId);

	/**
	 * 创建作业
	 *
	 * @param ak           ak
	 * @param sk           sk
	 * @param projectId    projectId
	 * @param endpoints    endpoints
	 * @param clusterId    集群 Id
	 * @param table        迁移的表信息
	 * @param fromLinkName 源链接名称
	 * @param fromDatabase 源数据库
	 * @param toLinkName   目标链接名称
	 * @param toDatabase   目标数据库
	 */
	HcsBaseResponse createJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId,
								CreateJobTableDTO table, String fromLinkName, String fromDatabase,
								String toLinkName, String toDatabase);

	HcsBaseResponse startJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName);

	CdmJobStatusResponse showJobStatus(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName);

	HcsBaseResponse deleteJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName);

	CdmJobResponse showJobs(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName);


	void createTable(String jdbcUrl, String username, String password, String sql);

	// 创建连接
	HcsBaseResponse createLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId,
							   String host, Integer port, String database, String username, String password, String linkName);

	// 查询连接
	CdmLinkResponse listLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String linkName);

	// 更新连接
	HcsBaseResponse updateLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId,
								  String host, Integer port, String database, String username, String password, String linkName);

	// 删除连接
	HcsBaseResponse delLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String linkName);
}
