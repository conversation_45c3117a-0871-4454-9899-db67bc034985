package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.entity.EntPrefixDomainEntity;
import cn.teleinfo.ds.business.domain.repository.EntPrefixRepository;
import cn.teleinfo.ds.business.domain.service.EntPrefixDomainService;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class EntPrefixDomainServiceImpl implements EntPrefixDomainService {
	private final EntPrefixRepository entPrefixRepository;



	@Override
	public EntPrefixDomainEntity findByEntPrefix(String entPrefix) {
		EntPrefixDomainEntity ent = entPrefixRepository.findByEntPrefix(entPrefix);
		if(ent == null){
			throw new CheckedException("企业不存在或已删除！");
		}
		return ent;
	}
}
