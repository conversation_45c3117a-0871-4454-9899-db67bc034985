package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import cn.teleinfo.ds.business.interfaces.dto.response.AppInfoTreeResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ProvincePrefixResponse;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.util.List;

public interface AppInfoDomainService {

	PageResponse<HandleSignAppInfoDTO> listHandleSignAppInfo(ListAppInfoQuery query);

	HandleSignAppInfoDTO queryHandleSignAppInfoDetail(String id);

	AppInfoDomainEntity findByHandleCode(String appHandleCode);
	/**
	 * 应用信息树查询
	 */
	List<AppInfoTreeResponse> queryAppInfoTree();


	AppInfoDomainEntity findById(Long id);

	ProvincePrefixResponse getGlobalConfig();


}
