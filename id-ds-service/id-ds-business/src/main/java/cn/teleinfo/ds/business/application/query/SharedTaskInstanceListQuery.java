package cn.teleinfo.ds.business.application.query;

import cn.teleinfo.ds.common.core.util.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class SharedTaskInstanceListQuery extends PageRequest {

	private  Long sharedTaskId;

	private String taskInstanceNo;

	private String taskInstanceName;

	private Integer executionType;

	private Integer runStatus;

	private String taskNo;

	private String taskName;

	private String appHandleCode;

	private List<String> userHandleCode;

	private LocalDateTime startTime;

	private LocalDateTime endTime;

}
