package cn.teleinfo.ds.business.domain.model.entity;

// 1：手动任务 2：定时任务
public enum TaskType {
	MANUAL(1),
	SCHEDULED(2);
	private final int code;

	TaskType(int code) {
		this.code = code;
	}

	public int code() {
		return code;
	}

	public static TaskType findByCode(int code) {
		for (TaskType value : TaskType.values()) {
			if (value.code() == code) {
				return value;
			}
		}

		throw new IllegalArgumentException("无效的任务类型: " + code);
	}
}
