package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskInstanceDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskStatusView;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.util.List;

public interface SharedTaskInstanceRepository {

	PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query, Integer page,
			Integer size);

	SharedTaskInstanceView getSharedTaskInstanceById(Long instanceId);

	void deleteById(Long id);

	Long save(SharedTaskInstanceDomainEntity sharedTaskInstance);

	/**
	 * 更新任务实例信息
	 * 运行时长 共享数据量总量
	 */
	void updateRuntimeInfo(SharedTaskInstanceDomainEntity sharedTaskInstance);

	List<SharedTaskStatusView> getSharedTaskStatus(Long id, String type);

}
