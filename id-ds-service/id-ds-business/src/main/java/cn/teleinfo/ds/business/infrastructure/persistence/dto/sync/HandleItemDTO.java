package cn.teleinfo.ds.business.infrastructure.persistence.dto.sync;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.List;

@Data
public class HandleItemDTO {

	/**
	 * 应用标识编码
	 */
	private String appHandleCode;
	/**
	 * 所属字段
	 */
	private String columnName;
	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;
	/**
	 * 数据库IP
	 */
	private String databaseIp;
	/**
	 * 数据库名称
	 */
	private String databaseName;
	/**
	 * 数据服务ID
	 */
	private Long dataChannelId;
	/**
	 * 数据通道类型
	 */
	private Integer dataChannelType;
	/**
	 * 描述
	 */
	private String description;
	/**
	 * 企业ID
	 */
	private String entPrefix;
	/**
	 * 字段
	 */
	private String field;
	/**
	 * 属性来源类型 0基础属性 1扩展属性
	 */
	private Integer fieldSourceType;
	/**
	 * 属性类型 1固定值 2标识解析数据源 3标识值 4标识-属性
	 */
	private Integer fieldType;
	/**
	 * 字段值
	 */
	private String fieldValue;
	/**
	 * 标识ID
	 */
	private Long handleId;
	/**
	 * 主键ID
	 */
	private Long id;
	/**
	 * 0:否, NULL:是
	 */
	private Integer isDeleted;
	/**
	 * 省级ID
	 */
	private String provincePrefix;
	private List<HandleReferenceDTO> references;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 所属表
	 */
	private String tableName;
	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;
}
