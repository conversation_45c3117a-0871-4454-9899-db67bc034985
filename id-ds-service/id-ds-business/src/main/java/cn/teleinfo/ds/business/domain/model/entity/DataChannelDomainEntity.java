package cn.teleinfo.ds.business.domain.model.entity;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DataChannelDomainEntity {

	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除: 0 未删除 null 已删除
	 */
	private Integer isDeleted;
	/**
	 * 通道原始id
	 */
	private Long sourceId;

	/**
	 * 数据通道名称
	 */
	private String dataChannelName;

	/**
	 * 所属对象标识编码
	 */
	private String objectHandle;

	/**
	 * 所属对象标识类型
	 */
	private Integer objectHandleType;

	/**
	 * 数据通道ID
	 */
	private Long dataChannelId;

	/**
	 * 实例数据类型
	 */
	private Integer dataType;

	/**
	 * 解析sql
	 */
	private String resolveSql;

	/**
	 * 查询sql
	 */
	private String querySql;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 应用身份编码
	 */
	private String appHandleCode;

	/**
	 * 是否共享 1-共享 2-未共享
	 */
	private Boolean isShare;

	/**
	 * 1基础属性 2扩展属性
	 */
	private Integer itemType;
}
