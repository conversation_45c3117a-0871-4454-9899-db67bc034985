package cn.teleinfo.ds.business.domain.repository;


import cn.teleinfo.ds.business.domain.model.entity.*;

import java.time.LocalDateTime;
import java.util.List;

public interface DataIntegratedRepository {
//    List<UserDomainEntity> userFindAll();

//    void userSaveAll(List<UserDomainEntity> users);

//    void userDeleteAll(List<UserDomainEntity> users);

    List<DataChannelDomainEntity> dataChannelFindAll();

    void dataChannelSaveAll(List<DataChannelDomainEntity> channels);

    void dataChannelDeleteAll(List<DataChannelDomainEntity> channels);

    List<AppInfoDomainEntity> appInfoFindAll();

    void appInfoDeleteAll(List<AppInfoDomainEntity> apps);

    void appInfoSaveAll(List<AppInfoDomainEntity> apps);

    LocalDateTime handleFindMaxUpdatedTime();

    List<HandleDomainEntity> handleFindAllById(List<Long> ids);

    List<HandleItemDomainEntity> handleItemFindAllById(List<Long> ids);

    List<HandleReferenceDomainEntity> handleReferenceFindAllById(List<Long> ids);

    void handleSaveAll(List<HandleDomainEntity> handle);

    void handleItemSaveAll(List<HandleItemDomainEntity> handleItem);

    void handleReferenceSaveAll(List<HandleReferenceDomainEntity> handleReference);

    List<Long> handleReferenceFindIdBy();

    void handleReferenceDeleteAllById(List<Long> ids);

    List<Long> handleItemFindIdBy();

    void handleItemDeleteAllById(List<Long> ids);

    List<Long> handleFindIdBy();

    void handleDeleteAllById(List<Long> ids);


	List<DataChannelDomainEntity> dataChannels();

	DataChannelDomainEntity dataChannel(Long shareChannelId);
}
