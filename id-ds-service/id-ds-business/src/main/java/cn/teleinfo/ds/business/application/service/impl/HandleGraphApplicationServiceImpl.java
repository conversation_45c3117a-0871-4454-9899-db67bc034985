package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.valueobject.Graph;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.GraphHandlesDTO;
import cn.teleinfo.ds.business.application.service.HandleGraphApplicationService;
import cn.teleinfo.ds.business.domain.service.HandleGraphDomainService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class HandleGraphApplicationServiceImpl implements HandleGraphApplicationService {

    private final HandleGraphDomainService handleGraphDomainService;

    @Override
    public Graph handleChildren(String handle) {
        return handleGraphDomainService.handleChildren(handle);
    }

    @Override
    public Graph handleGraph(String handle) {
        return handleGraphDomainService.handleGraph(handle);

	}

    @Override
    public List<GraphHandlesDTO> getGraphHandles(String handle) {
        return handleGraphDomainService.getGraphHandles(handle);
    }


    @Override
    public Handle handleItems(String handle) {
       return handleGraphDomainService.handleItems(handle);
    }
}
