package cn.teleinfo.ds.business.domain.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
public class SharedTaskListDetail {

	private Long id;

	/**
	 * 任务编号，如RW202506001
	 */
	private String taskNo;

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务类型：1-手动任务，2-定时任务
	 */
	private Integer taskType;

	/**
	 * 任务状态：1-启用，0-禁用
	 */
	private Integer taskStatus;

	/**
	 * 测试状态：1-成功，2-失败，0-未测试
	 */
	private Integer testStatus;

	/**
	 * 最后一次测试执行的任务实例ID
	 */
	private Long last_test_instance_id;

	/**
	 * 运行状态：1-运行中，2-成功，3-失败，0-未运行
	 */
	private Integer runStatus;

	/**
	 * 最后一次正常执行的任务实例ID
	 */
	private Long last_execution_instance_id;

	/**
	 * 最后一次运行时间
	 */
	private Timestamp lastRunTime;

	/**
	 * 操作人
	 */
	private String operator;

	/**
	 * 创建时间
	 */
	private Timestamp createTime;

	/**
	 * 更新时间
	 */
	private Timestamp updateTime;

	/**
	 * 应用系统
	 */
	private String appHandleName;
}
