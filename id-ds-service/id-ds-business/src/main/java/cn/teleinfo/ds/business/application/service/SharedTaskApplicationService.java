package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.query.ListSharedTaskQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareDataDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTaskDetails;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskStatusView;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.util.List;

public interface SharedTaskApplicationService {


	PageResponse<SharedTaskDomainEntity> listSharedTask(ListSharedTaskQuery query);

	SharedTaskDomainEntity getSharedTask(Long id);


	/**
	 * 执行共享任务
	 * @param id 任务 id
	 */

    void execute(Long id,Integer executionType);

	void deleteSharedTaskId(Long id);


	void updateSharedTaskStatus(Long id);

	List<SharedTaskStatusView> getSharedTaskStatus(Long id, String type);

	ShareDataDetails getSharedDataDetails(Long dataId);

	SharedTaskDetails getSharedTaskDetail(Long id);
}
