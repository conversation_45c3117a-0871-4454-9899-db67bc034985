package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTask;
import cn.teleinfo.ds.business.domain.model.aggregate.PlatformConnection;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTaskDetails;
import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.repository.*;
import cn.teleinfo.ds.business.domain.service.ShareChannelsDomainService;
import cn.teleinfo.ds.business.domain.service.SharedTaskDomainService;
import cn.teleinfo.ds.business.domain.util.CronParseUtil;
import cn.teleinfo.ds.business.infrastructure.config.AppConfig;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.*;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskBasicInfoValueDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.SharedTaskShareDataValueDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskDetailEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskEntity;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskDetailResponse;
import cn.teleinfo.ds.common.core.constant.UserConstants;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.log.util.BusinessLoggerOption;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import cn.teleinfo.ds.quartz.api.feign.RemoteJobService;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.cronutils.descriptor.CronDescriptor;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.parser.CronParser;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Submission;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.common.core.util.SqlUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileUpload;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.Charset;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
@AllArgsConstructor
public class SharedTaskDomainServiceImpl implements SharedTaskDomainService {
	private final SharedTaskRepository sharedTaskRepository;

	private final SharedTaskDetailsRepository sharedTaskDetailsRepository;
	//@Autowired
	//@Qualifier("mockHcsRepository")
	private final HcsRepository hcsRepository;

	private final RemoteJobService remoteJobService;

	private final AppInfoRepository appInfoRepository;

	private final RoleService roleService;

	private final HandlesRepository handlesRepository;
	private final ShareDataSourcesRepository shareDataSourcesRepository;
	private final ShareChannelRepository shareChannelRepository;
	private final TargetSourceRepository targetSourceRepository;
	private final PlatformConnectionRepository sysConnectionRepository;
	private final BusinessLoggerOption businessLoggerOption;
	private final ShareChannelsDomainService shareChannelsDomainService;

	private final AppConfig appConfig;


	@Override
	public PageResponse<SharedTaskDomainEntity> listSharedTask(SharedTaskDomainEntity entity, LocalDateTime start, LocalDateTime end, Integer page, Integer size,
															   String taskCode, String appHandleCode, LocalDateTime editStart, LocalDateTime editEnd) {
		List<String> userHandleCodes = null;
		Long id = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(id);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				//获取该用户关联的所有应用
				List<String> handleCodes = appInfoRepository.findHandleCodeByUserId(id);
				if (!handleCodes.isEmpty()) {
					userHandleCodes = handleCodes;
				}
			}
		}
		return sharedTaskRepository.listSharedTask(entity, start, end, page, size, taskCode, appHandleCode, userHandleCodes, editStart, editEnd);
	}

	@Override
	public SharedTaskDomainEntity getSharedTask(Long id) {
		SharedTaskDomainEntity sharedTask = sharedTaskRepository.findById(id);
		if (sharedTask == null) {
			throw new NoSuchElementException("共享任务不存在！");
		}
		return sharedTask;
	}

	@Override
	public SharedTask sharedTask(Long id, ExecutionType execType) {
		SharedTaskDomainEntity task = this.getSharedTask(id);

		List<HandleDomainEntity> handles = new ArrayList<>();
		List<SharedTaskDetail> details = task.getDetails();
		for (SharedTaskDetail detail : details) {

			HandleDomainEntity handle = handlesRepository.findByHandle(detail.getHandle());
			if (handle == null) {
				log.error("对象标识不存在或已删除 handle={}", detail.getHandle());
				throw new CheckedException("对象标识不存在或已删除！");
			}

			handles.add(handle);
		}

		var appInfo = appInfoRepository.findByHandleCode(task.getAppHandleCode());
		if (appInfo == null) {
			log.error("应用不存在或已删除  app_handle_code={}", task.getAppHandleCode());
			throw new CheckedException("应用不存在或已删除！");
		}

		// 查询共享源
		ShareDataSourcesDomainEntity shareDataSource = shareDataSourcesRepository.findByAppHandleCode(task.getAppHandleCode());
		if (shareDataSource == null) {
			log.error("共享源不存在或已删除  app_handle_code={}", task.getAppHandleCode());
			throw new CheckedException("共享源不存在或已删除！");
		}
		appInfo.setShareDataSources(shareDataSource);


		var targetSource = targetSourceRepository.findById(task.getTargetSourceId());

		if (targetSource == null) {
			log.error("目标源不存在或已删除  target_source_id={}", task.getTargetSourceId());
			throw new CheckedException("目标源不存在或已删除！");
		}

		var code = PlatformConnectionType.findByCode(targetSource.getPlatformType());
		var conn = sysConnectionRepository.findByPlatformType(code);

		if (conn == null) {
			log.error("平台链接不存在或已删除  target_source_id={}", task.getTargetSourceId());
			throw new CheckedException("平台链接不存在或已删除！");
		}

		var connection = new PlatformConnection(conn);
		return new SharedTask(task, handles, appInfo, targetSource, connection.getPlatformConnection(), businessLoggerOption, execType);
	}

	// 根据对象标识生成输出表 SQL
	@Override
	public String genOutputTablesSQL(SharedTask sharedTask) {
		List<HandleDomainEntity> handles = sharedTask.getHandles();
		StringBuilder allSQL = new StringBuilder();

		for (HandleDomainEntity handle : handles) {
			// 99.1000.1/YMZ12N35 => YMZ12N35
			String masterTableName = handle.getHandle().substring(handle.getHandle().lastIndexOf("/") + 1).toUpperCase();

			masterTableName = "TSTD_" + masterTableName + "_BASIC";

			// 基础属性
			List<HandleItemDomainEntity> basic = new ArrayList<>();

			// 扩展属性
			List<HandleItemDomainEntity> extend = new ArrayList<>();

			for (HandleItemDomainEntity item : handle.getHandleItems()) {
				if (FieldSourceType.BASIC.code() == item.getFieldSourceType()) {
					basic.add(item);
				}

				if (FieldSourceType.EXTEND.code() == item.getFieldSourceType()) {
					extend.add(item);
				}
			}

			// 基础属性
			StringBuilder basicCreateSQL = new StringBuilder();
			String basicDropSQL = "-- 创建【" + handle.getName() + "】基础属性表\n" +
					"DROP TABLE IF EXISTS " + masterTableName + ";\n";

			basicCreateSQL.append("CREATE TABLE IF NOT EXISTS ").append(masterTableName).append(" (\n");

			for (int i = 0; i < basic.size(); i++) {
				var item = basic.get(i);

				var field = StrUtil.toUpperCase(item.getField());

				basicCreateSQL.append("    ").append(field).append(" STRING COMMENT '").append(field).append("'");

				if (i < basic.size() - 1) {
					basicCreateSQL.append(",\n");
				} else {
					basicCreateSQL.append("\n");
				}
			}
			basicCreateSQL.append(") COMMENT '").append(handle.getName()).append("基础属性表';\n\n");

			var basicSql = basicCreateSQL.toString();
			sharedTask.getOutputTables().get(handle.getHandle()).add(basicSql);
			allSQL.append(basicDropSQL).append(basicSql).append("\n");

			// 扩展属性 => 搞一个新表
			if (extend.isEmpty()) {
				sharedTask.getTaskLogger().warn("对象标识无扩展属性 taskId={} handle={}", sharedTask.getSharedTask().getId(), handle.getHandle());
				continue;
			}

			for (HandleItemDomainEntity item : extend) {
				var extendTableName = "TSTD_" + masterTableName + "_EXTEND_" + item.getField().toUpperCase();

				if (item.getShareChannel() == null) {
					sharedTask.getTaskLogger().error("扩展属性无共享通道 taskId={} handle={} field={}", sharedTask.getSharedTask().getId(), handle.getHandle(), item.getField());

					sharedTask.updateSubRuntime(handle.getHandle(), SubTaskCurrentStep.SHARE_CHANNEL_SQL, 0, RunStatus.FAIL, "扩展属性无共享通道");
					continue;
				}

				var sql = StrUtil.isEmpty(item.getShareChannel().getCustomSql()) ? item.getShareChannel().getDefaultSql() : item.getShareChannel().getCustomSql();

				List<String> fields = SqlUtils.extractSelectSqlField(sql);
				if (fields == null) {
					sharedTask.getTaskLogger().error("扩展属性共享通道 SQL 错误。 taskId={} handle={} SQL={}", sharedTask.getSharedTask().getId(), handle.getHandle(), sql);

					sharedTask.updateSubRuntime(handle.getHandle(), SubTaskCurrentStep.SHARE_CHANNEL_SQL, 0, RunStatus.FAIL, "扩展属性无共享通道");
					continue;
				}

				StringBuilder extendCreteSQL = new StringBuilder();
				String extendDropSQL = "-- 创建【" + handle.getName() + "】扩展属性表\n" +
						"DROP TABLE IF EXISTS " + extendTableName + ";\n";

				extendCreteSQL.append("CREATE TABLE IF NOT EXISTS ").append(extendTableName).append(" (\n");

				for (int i = 0; i < fields.size(); i++) {
					String field = StrUtil.toUpperCase(fields.get(i));


					if (i == 0) {
						extendCreteSQL.append("    ").append(field).append(" STRING COMMENT '").append(field).append("'");
					} else {
						extendCreteSQL.append(",\n    ").append(field).append(" STRING COMMENT '").append(field).append("'");
					}
				}

				extendCreteSQL.append("\n) COMMENT '").append(handle.getName()).append("扩展属性表-").append(item.getField()).append("';\n\n");

				var extendSql = extendCreteSQL.toString();
				sharedTask.getOutputTables().get(handle.getHandle()).add(extendSql);
				allSQL.append(extendDropSQL).append(extendSql).append("\n");


				sharedTask.updateSubRuntime(handle.getHandle(), SubTaskCurrentStep.SHARE_CHANNEL_SQL, 0, RunStatus.RUN, null);
			}
		}

		sharedTask.updateRuntime(RunStatus.RUN, null);
		return allSQL.toString();
	}

	/**
	 * 生成提取 SQL
	 */
	@Override
	public String genExtractSQL(SharedTask sharedTask) {
		sharedTask.getTaskLogger().info("开始生成提取 SQL");

		StringBuilder sqlBuilder = new StringBuilder();

		List<HandleDomainEntity> handles = sharedTask.getHandles();

		for (HandleDomainEntity handle : handles) {
			// 99.1000.1/YMZ12N35 => YMZ12N35

			sharedTask.getTaskLogger().info("提取对象标识 SQL {}", handle.getHandle());

			String masterTableName = handle.getHandle().substring(handle.getHandle().lastIndexOf("/") + 1).toUpperCase();
			masterTableName = "TSTD_" + masterTableName + "_BASIC";

			// 基础属性
			List<HandleItemDomainEntity> basic = new ArrayList<>();

			// 扩展属性
			List<HandleItemDomainEntity> extend = new ArrayList<>();

			for (HandleItemDomainEntity item : handle.getHandleItems()) {
				if (FieldSourceType.BASIC.code() == item.getFieldSourceType()) {
					basic.add(item);
				}

				if (FieldSourceType.EXTEND.code() == item.getFieldSourceType()) {
					extend.add(item);
				}
			}

			// 基础属性。对应同一个通道。
			HandleItemDomainEntity item = basic.get(0);

			var basicSQL = StrUtil.isEmpty(item.getShareChannel().getCustomSql())
					? item.getShareChannel().getDefaultSql() :
					item.getShareChannel().getCustomSql();

			String basicCreteSQL = sharedTask.findSQLByTableName(masterTableName);

			String basicInsertSQL = null;
			try {
				basicInsertSQL = SqlUtils.genInsertSQLByCreateSQLAndSelectSQL(basicCreteSQL, basicSQL);
			} catch (Exception e) {
				sharedTask.getTaskLogger().error("解析 SQL 失败  handle={}", handle.getHandle());
				sharedTask.updateSubRuntime(handle.getHandle(), SubTaskCurrentStep.SHARE_CHANNEL_SQL, 0, RunStatus.FAIL, "解析 SQL 失败");
				continue;
			}

			if (basicInsertSQL == null) {
				sharedTask.getTaskLogger().error("解析 SQL 失败  handle={}", handle.getHandle());
				sharedTask.updateSubRuntime(handle.getHandle(), SubTaskCurrentStep.SHARE_CHANNEL_SQL, 0, RunStatus.FAIL, "解析 SQL 失败");
				continue;
			}

			sqlBuilder.append("-- 生成基础属性表的数据抽取语句 ").append(masterTableName).append("\n");
			sqlBuilder.append(basicInsertSQL).append(";").append("\n");

			// 扩展属性
			for (HandleItemDomainEntity extendItem : extend) {
				var extendSQL = StrUtil.isEmpty(extendItem.getShareChannel().getCustomSql())
						? extendItem.getShareChannel().getDefaultSql() :
						extendItem.getShareChannel().getCustomSql();

				var extendTableName = "TSTD_" + masterTableName + "_EXTEND_" + extendItem.getField().toUpperCase();

				String extendCreteSQL = sharedTask.findSQLByTableName(extendTableName);

				String extendInsertSQL = null;
				try {
					extendInsertSQL = SqlUtils.genInsertSQLByCreateSQLAndSelectSQL(extendCreteSQL, extendSQL);
				} catch (Exception e) {
					sharedTask.getTaskLogger().error("解析扩展字段 SQL 失败  handle={} field={}", handle.getHandle(), extendItem.getField());
					sharedTask.updateSubRuntime(handle.getHandle(), SubTaskCurrentStep.SHARE_CHANNEL_SQL, 0, RunStatus.FAIL, "解析扩展字段 SQL 失败");
					continue;
				}
				if (extendInsertSQL == null) {
					sharedTask.getTaskLogger().error("解析扩展字段 SQL 失败  handle={} field={}", handle.getHandle(), extendItem.getField());
					sharedTask.updateSubRuntime(handle.getHandle(), SubTaskCurrentStep.SHARE_CHANNEL_SQL, 0, RunStatus.FAIL, "解析扩展字段 SQL 失败");
					continue;
				}

				sqlBuilder.append("-- 生成扩展属性表的数据抽取语句 ").append(extendTableName).append("\n");
				sqlBuilder.append(extendInsertSQL).append(";").append("\n");
			}
		}

		var s = StrUtil.replace(sqlBuilder.toString(), "INSERT INTO ", "INSERT OVERWRITE TABLE ");

		sharedTask.getTaskLogger().info("生成提取 SQL 成功。{}", s);
		return s;
	}

	/**
	 * 创建输出表结构
	 *
	 * @param sharedTask 共享任务
	 */
	@Override
	public void createOutputTables(SharedTask sharedTask, String SQL) {
		sharedTask.getTaskLogger().info("数据写入对象标识，创建对象标识表");
		sharedTask.updateAllSubRuntime(SubTaskCurrentStep.WRITE_HANDLE, 0, RunStatus.RUN, null);

		String scriptName = "outputSQL_" + sharedTask.getSharedTask().getTaskNo();
		execSQL(sharedTask, SQL, scriptName);
	}

	/**
	 * 写入输出表数据
	 */
	@Override
	public void writeOutputTables(SharedTask sharedTask, String extractSQL) {
		String scriptName = "extractSQL_" + sharedTask.getSharedTask().getTaskNo();
		execSQL(sharedTask, extractSQL, scriptName);
	}

	private void execSQL(SharedTask sharedTask, String SQL, String scriptName) {
		sharedTask.getTaskLogger().info("开始执行脚本 scriptName={} scriptContent={}", scriptName, SQL);

		PlatformConnection platformConnection = new PlatformConnection(sharedTask.getSysConnection());
		String ak = platformConnection.getHcsConnContent().getAk();
		String sk = platformConnection.getHcsConnContent().getSk();
		List<String> endpoints = List.of(platformConnection.getHcsConnContent().getDgcEndpoint());

		// 共享源
		ShareDataSourcesDomainEntity shareDataSources = sharedTask.getAppInfo().getShareDataSources();
		ShareDataSourcesItem shareDataSourcesItem = shareDataSources.getShareDataSourcesItem();
		String projectId = shareDataSourcesItem.getProjectId();
		String workspace = shareDataSourcesItem.getWorkspace();
		String stdDataConnName = shareDataSourcesItem.getStdDataConnName();
		String stdDataDatabaseName = shareDataSourcesItem.getStdDataDatabaseName();

		ScriptInfo script;

		try {
			script = hcsRepository.findScript(ak, sk, projectId, endpoints, workspace, scriptName);
		} catch (Exception e) {
			var errorMessage = "查询脚本失败";
			sharedTask.getTaskLogger().error(errorMessage, e);
			sharedTask.updateRuntime(RunStatus.FAIL, errorMessage);
			return;
		}

		if (script != null) {
			try {
				hcsRepository.deleteScript(ak, sk, projectId, endpoints, scriptName, workspace);
			} catch (Exception e) {
				var errorMessage = "删除脚本失败";
				sharedTask.getTaskLogger().error(errorMessage, e);
				sharedTask.updateRuntime(RunStatus.FAIL, errorMessage);
				return;
			}
		}

		// 创建脚本
		try {
			hcsRepository.createScript(ak, sk, projectId, endpoints, scriptName, SQL, workspace, stdDataDatabaseName, stdDataConnName);
		} catch (Exception e) {
			var errorMessage = "创建脚本失败";
			sharedTask.getTaskLogger().error(errorMessage, e);
			sharedTask.updateRuntime(RunStatus.FAIL, errorMessage);
			return;
		}

		// 执行脚本
		String instanceId = null;
		try {
			instanceId = hcsRepository.executeScript(ak, sk, projectId, endpoints, scriptName, workspace);
		} catch (Exception e) {
			var errorMessage = "执行脚本失败";
			sharedTask.getTaskLogger().error(errorMessage, e);
			sharedTask.updateRuntime(RunStatus.FAIL, errorMessage);
			return;
		}

		while (true) {
			// 查询脚本实例执行结果
			ListScriptResultsResponseDTO results = null;
			try {
				results = hcsRepository.listScriptResults(ak, sk, projectId, endpoints, scriptName, workspace, instanceId);
			} catch (Exception e) {
				sharedTask.getTaskLogger().error("查询脚本执行状态失败", e);
				sharedTask.updateRuntime(RunStatus.FAIL, e.getMessage());
				return;
			}
			String status = results.getStatus();

			String message = results.getMessage();

			if (StrUtil.equals(HcsScriptRunStatus.LAUNCHING.code(), status)) {
				sharedTask.getTaskLogger().info("脚本正在提交 scriptName={} status={}", scriptName, status);
			} else if (StrUtil.equals(HcsScriptRunStatus.RUNNING.code(), status)) {
				sharedTask.getTaskLogger().info("脚本正在执行 scriptName={} status={}", scriptName, status);
			} else if (StrUtil.equals(HcsScriptRunStatus.FINISHED.code(), status)) {

				sharedTask.getTaskLogger().info("脚本执行成功 scriptName={} status={}", scriptName, status);
				sharedTask.updateRuntime(RunStatus.SUCCESS, null);
				break;

			} else if (StrUtil.equals(HcsScriptRunStatus.FAILED.code(), status)) {
				sharedTask.getTaskLogger().error("脚本执行失败 scriptName={} status={} message={}", scriptName, status, message);
				sharedTask.updateRuntime(RunStatus.FAIL, message);
				break;

			} else {
				sharedTask.getTaskLogger().warn("执行脚本结果未知，获取到未知状态 scriptName={} status={}", scriptName, status);
			}
		}
	}

	/**
	 * 输出内容到指定数据源
	 */
	@Override
	public void outputToTargetDataSource(SharedTask sharedTask) {
		sharedTask.getTaskLogger().info("创建离线管道任务");
		sharedTask.updateAllSubRuntime(SubTaskCurrentStep.OFFLINE_TASK, 0, RunStatus.RUN, null);

		// 创建目标源应该创建连接；并存储连接名和数据库名
		TargetSourceItems targetSourceItems = sharedTask.getTargetSource().getTargetSourceItems();
		//String targetDataConnName = "bsjx-dws-test";
		//String targetDatabaseName = "bsjx";


		PlatformConnection platformConnection = new PlatformConnection(sharedTask.getSysConnection());
		String ak = platformConnection.getHcsConnContent().getAk();
		String sk = platformConnection.getHcsConnContent().getSk();
		List<String> endpoints = List.of(platformConnection.getHcsConnContent().getCdmEndpoint());

		// 共享源
		ShareDataSourcesDomainEntity shareDataSources = sharedTask.getAppInfo().getShareDataSources();
		ShareDataSourcesItem shareDataSourcesItem = shareDataSources.getShareDataSourcesItem();
		String projectId = shareDataSourcesItem.getProjectId();
		String stdDataConnName = shareDataSourcesItem.getStdDataConnName();
		String stdDataDatabaseName = shareDataSourcesItem.getStdDataDatabaseName();
		String clusterId = shareDataSourcesItem.getClusterId();


		for (Map.Entry<String, List<String>> entry : sharedTask.getOutputTables().entrySet()) {
			var handle = entry.getKey();
			var sqls = entry.getValue();

			for (String sql : sqls) {
				var table = new CreateJobTableDTO();

				var t = SqlUtils.extractCreateSqlTable(sql);

				if (t == null) {
					var message = StrUtil.format("对象标识建表语句错误 sql={}", sql);
					sharedTask.getTaskLogger().error(message);
					sharedTask.updateRuntime(RunStatus.FAIL, message);
					continue;
				}

				table.setTableName(t.getTableName());
				table.setColumnList(t.getColumnList());

				sharedTask.getTaskLogger().info("开始创建作业 table={} columnList={}", table.getTableName(), CollUtil.join(table.getColumnList(), "$"));
				// 在目标源创建表
				var mysql = SqlUtils.hiveToMysql(sql);

				// XXX 目标源数据库链接替换为代理
				String databaseUrl = targetSourceItems.getDatabaseUrl();
				if (StrUtil.equals("99.1000", appConfig.getProvincePrefix())) {
					databaseUrl = "10.14.145.200";
				}

				String jdbcUrl = StrUtil.format("jdbc:postgresql://{}:{}/{}?currentSchema={}&sslmode=disable",
						databaseUrl, targetSourceItems.getPort(), targetSourceItems.getDatabaseName(), targetSourceItems.getDatabaseName());


				try {
					hcsRepository.createTable(jdbcUrl, targetSourceItems.getDatabaseUsername(), targetSourceItems.getDatabasePassword(), mysql);
				} catch (Exception e) {
					sharedTask.getTaskLogger().error("目标源创建表失败", e);
					sharedTask.updateRuntime(RunStatus.FAIL, "目标源创建表失败" + e.getMessage());
					return;
				}

				// 创建作业
				String jobName = table.getTableName().toLowerCase();

				CdmJobResponse cdmJobResponse = hcsRepository.showJobs(ak, sk, projectId, endpoints, clusterId, jobName);
				if (StrUtil.isNotEmpty(cdmJobResponse.getErrorCode()) && !StrUtil.equals(CdmErrCode.JOB_NOT_EXIST.code(), cdmJobResponse.getErrorCode())) {
					sharedTask.getTaskLogger().error("查询作业，失败 code={} message={}", cdmJobResponse.getErrorCode(), cdmJobResponse.getErrorMsg());
				}

				if (cdmJobResponse.getJobs() != null && !cdmJobResponse.getJobs().isEmpty()) {
					for (Job job : cdmJobResponse.getJobs()) {
						HcsBaseResponse resp = hcsRepository.deleteJob(ak, sk, projectId, endpoints, clusterId, job.getName());
						if (StrUtil.isNotEmpty(resp.getErrorCode())) {
							sharedTask.getTaskLogger().error("删除作业，失败 code={} message={}", resp.getErrorCode(), resp.getErrorMsg());
						}
					}
				}
				HcsBaseResponse resp = hcsRepository.createJob(ak, sk, projectId, endpoints, clusterId, table, stdDataConnName, stdDataDatabaseName,
						targetSourceItems.getLinkName(), targetSourceItems.getDatabaseName());
				if (StrUtil.isNotEmpty(resp.getErrorCode())) {
					sharedTask.getTaskLogger().error("创建作业，失败 code={} message={}", resp.getErrorCode(), resp.getErrorMsg());
					sharedTask.updateRuntime(RunStatus.FAIL, resp.getErrorMsg());
					return;
				}

				// 启动作业
				sharedTask.getTaskLogger().info("执行任务写入目标源");
				sharedTask.getTaskLogger().info("启动作业 jobName={} ", jobName);

				sharedTask.updateAllSubRuntime(SubTaskCurrentStep.WRITE_TARGET_SOURCE, 0, RunStatus.RUN, null);

				HcsBaseResponse startJobResp = hcsRepository.startJob(ak, sk, projectId, endpoints, clusterId, jobName);
				if (StrUtil.isNotEmpty(startJobResp.getErrorCode())) {
					sharedTask.getTaskLogger().error("执行任务写入目标源，失败 jobName={} code={} message={}", jobName, startJobResp.getErrorCode(), startJobResp.getErrorMsg());
					sharedTask.updateRuntime(RunStatus.FAIL, startJobResp.getErrorMsg());
					return;
				}


				checkRun(ak, sk, projectId, endpoints, clusterId, jobName, handle, sharedTask);
			}
		}
	}

	private void checkRun(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName, String handle, SharedTask sharedTask) {
		while (true) {
			CdmJobStatusResponse cdmJobStatusResponse = hcsRepository.showJobStatus(ak, sk, projectId, endpoints, clusterId, jobName);

			if (StrUtil.isNotEmpty(cdmJobStatusResponse.getErrorCode())) {
				sharedTask.getTaskLogger().error("执行任务写入目标源，失败 jobName={} code={} message={}", jobName, cdmJobStatusResponse.getErrorCode(), cdmJobStatusResponse.getErrorMsg());
				sharedTask.updateRuntime(RunStatus.FAIL, cdmJobStatusResponse.getErrorMsg());
				break;
			}

			if (cdmJobStatusResponse.getSubmissions() != null) {
				Submission submission = cdmJobStatusResponse.getSubmissions().get(0);

				if (StrUtil.equals(HcsCmdRunStatus.BOOTING.code(), submission.getStatus())) {
					sharedTask.getTaskLogger().info("作业启动中");
				} else if (StrUtil.equals(HcsCmdRunStatus.FAILURE_ON_SUBMIT.code(), submission.getStatus())) {
					sharedTask.getTaskLogger().error("作业提交失败");

					sharedTask.updateSubRuntime(handle, SubTaskCurrentStep.WRITE_TARGET_SOURCE, 0, RunStatus.FAIL, "作业提交失败");
					sharedTask.updateRuntime(RunStatus.FAIL, "作业提交失败");

					return;
				} else if (StrUtil.equals(HcsCmdRunStatus.RUNNING.code(), submission.getStatus())) {
					sharedTask.getTaskLogger().info("作业运行中");
				} else if (StrUtil.equals(HcsCmdRunStatus.SUCCEEDED.code(), submission.getStatus())) {

					Long writeRows = submission.getCounters().getOrgApacheSqoopSubmissionCounterSqoopCounters().getRowsWritten();

					sharedTask.getTaskLogger().info("作业运行成功，写入数据 {}", writeRows);

					sharedTask.updateSubRuntime(handle, SubTaskCurrentStep.WRITE_TARGET_SOURCE, Math.toIntExact(writeRows), RunStatus.SUCCESS, null);

					sharedTask.updateRuntime(RunStatus.SUCCESS, null);
					return;
				} else if (StrUtil.equals(HcsCmdRunStatus.FAILED.code(), submission.getStatus())) {
					sharedTask.getTaskLogger().error("作业运行失败");

					sharedTask.updateSubRuntime(handle, SubTaskCurrentStep.WRITE_TARGET_SOURCE, 0, RunStatus.FAIL, "作业运行失败");
					sharedTask.updateRuntime(RunStatus.FAIL, "作业运行失败");
					return;
				} else if (StrUtil.equals(HcsCmdRunStatus.UNKNOWN.code(), submission.getStatus())) {
					sharedTask.getTaskLogger().info("作业状态未知");
				}
			}
		}
	}


	@Override
	@Transactional
	public void updateSharedTaskStatus(SharedTaskDomainEntity task) {
		SharedTaskDomainEntity entity = sharedTaskRepository.findById(task.getId());
		entity.setTaskStatus(task.getTaskStatus());
		sharedTaskRepository.save(entity);

		if (task.getTaskStatus() == 0) {
			remoteJobService.updateJobStatus(task.getId(), 2);
		} else if (task.getTaskStatus() == 1) {
			remoteJobService.updateJobStatus(task.getId(), 1);
		}
	}


	@Override
	@Transactional
	public void updateLastInstance(SharedTaskDomainEntity sharedTask, SharedTaskInstanceDomainEntity instance, ExecutionType execType) {
		Long id = sharedTask.getId();

		SharedTaskDomainEntity sharedTaskDomainEntity = sharedTaskRepository.findById(id);

		if (instance != null) {
			Long lastInstanceId = instance.getId();
			Integer runStatus = instance.getRunStatus();
			String taskInstanceNo = instance.getTaskInstanceNo();
			LocalDateTime runTime = instance.getRunTime();
			Integer runDuration = instance.getRunDuration();
			Integer sharedDataCount = instance.getSharedDataCount();

			switch (execType) {
				case FORMAL -> {
					sharedTaskDomainEntity.setLastExecutionInstanceId(lastInstanceId);
					sharedTaskDomainEntity.setRunStatus(runStatus);
				}

				case TEST -> {
					sharedTaskDomainEntity.setLastTestInstanceId(lastInstanceId);
					sharedTaskDomainEntity.setTestStatus(runStatus);
				}
			}

			sharedTaskDomainEntity.setLastExecutionNo(taskInstanceNo);        // 最后一次任务执行编号
			sharedTaskDomainEntity.setLastRunTime(runTime);            // 最后一次运行时间
			sharedTaskDomainEntity.setLastRunDuration(runDuration);        // 最后一次运行时长(秒)
			sharedTaskDomainEntity.setLastSharedDataCount(sharedDataCount);    // 最后一次共享数据总量
		} else {
			sharedTaskDomainEntity.setRunStatus(RunStatus.RUN.code());
			sharedTaskDomainEntity.setLastExecutionNo(null);        // 最后一次任务执行编号
			sharedTaskDomainEntity.setLastRunTime(null);            // 最后一次运行时间
			sharedTaskDomainEntity.setLastRunDuration(0);          // 最后一次运行时长(秒)
			sharedTaskDomainEntity.setLastSharedDataCount(0);      // 最后一次共享数据总量
		}

		sharedTaskRepository.save(sharedTaskDomainEntity);
	}

	@Override
	public SharedTaskDetails getSharedTaskDetail(Long id) {
		SharedTaskBasicInfoValueDTO sharedTaskBasicInfoValueDTO = sharedTaskRepository.getSharedTaskBasicInfoValueDTO(id);
		List<SharedTaskShareDataValueDTO> sharedTaskShareDataValueDTOList = sharedTaskDetailsRepository.getSharedTaskShareDataValueDTO(id);
		String cron = sharedTaskBasicInfoValueDTO.getCronExpression();
		//翻译cron表达式
		sharedTaskBasicInfoValueDTO.setCronDetail(CronParseUtil.parseToHumanReadable(cron));
		return new SharedTaskDetails(sharedTaskBasicInfoValueDTO, sharedTaskShareDataValueDTOList);
	}

	@Override
	public void deleteSharedTask(Long id) {
		sharedTaskRepository.deleteById(id);
		remoteJobService.deleteJob(id);
	}

	// 分析湖仓映射关系
	@Override
	public void shareChannelConnected(SharedTask sharedTask) {
		try {
			sharedTask.updateAllSubRuntime(SubTaskCurrentStep.MAPPING, 0, RunStatus.RUN, null);
			sharedTask.getTaskLogger().info("开始分析湖仓映射关系");

			Map<Long, ShareChannel> map = new HashMap<>();

			for (HandleDomainEntity handle : sharedTask.getHandles()) {
				List<HandleItemDomainEntity> items = handle.getHandleItems();
				for (HandleItemDomainEntity item : items) {
					ShareChannel enableChannel = map.get(item.getDataChannelId());
					if (enableChannel == null) {
						enableChannel = shareChannelRepository.findByDataChannelIdAndChannelStatus(item.getDataChannelId());
						if (enableChannel == null) {
							sharedTask.getTaskLogger().error("共享通道不存在 data_channel_id={}", item.getDataChannelId());
							continue;
						}

						map.put(item.getDataChannelId(), enableChannel);
					}

					item.setShareChannel(enableChannel);
				}
			}

			if (map.isEmpty()) {
				sharedTask.getTaskLogger().error("共享通道不存在");
				sharedTask.updateRuntime(RunStatus.FAIL, "共享通道不存在");
				return;
			}

			List<ShareChannel> channels = new ArrayList<>();
			map.forEach((k, v) -> {
				channels.add(v);
			});

			// 还原通道内的表
			List<Path> paths = new ArrayList<>(channels.size());
			String runResult = shareChannelsDomainService.shareChannelConnected4TaskExecute(channels, paths);

			if (!paths.isEmpty()) {
				sharedTask.destroyLogger();

				// 追加通道探测日志
				Path fullLogPath = sharedTask.getFullLogPath();
				for (Path path : paths) {
					if (path != null) {
						String s = FileUtil.readString(path.toFile(), Charset.defaultCharset());
						FileUtil.appendString(s, fullLogPath.toFile(), Charset.defaultCharset());
					}
				}

				sharedTask.restartLogger();
			}


			if (StrUtil.equals("error", runResult)) {
				sharedTask.getTaskLogger().error("分析湖仓映射关系失败");
				sharedTask.updateRuntime(RunStatus.FAIL, "");
			}
		} catch (IORuntimeException e) {
			sharedTask.getTaskLogger().error("分析湖仓映射关系失败");

			sharedTask.updateRuntime(RunStatus.FAIL, "");
		}
	}
}
