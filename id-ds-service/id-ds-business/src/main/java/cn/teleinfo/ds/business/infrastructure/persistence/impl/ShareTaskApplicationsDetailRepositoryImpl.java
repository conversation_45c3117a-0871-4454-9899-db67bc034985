package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.repository.ShareTaskApplicationsDetailRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsDetailsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareTaskApplicationsDetailsJpaRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
public class ShareTaskApplicationsDetailRepositoryImpl implements ShareTaskApplicationsDetailRepository {

	private final ShareTaskApplicationsDetailsJpaRepository detailsJpaRepository;

	/**
	 * 根据申请任务ID查询申请任务详情
	 * @param taskId 申请任务ID
	 * @return 申请任务详情列表
	 */
	@Override
	public List<ShareTaskApplicationsDetailDTO> findShareTaskApplicationsDetailsByTaskId(Long taskId) {
		return detailsJpaRepository.findShareTaskApplicationsDetailsByTaskId(taskId);
	}

	@Override
	public List<ShareTaskApplicationsDetailsDomainEntity> findShareTaskApplicationsDetailsDomainEntities(Long shareTaskApplicationsId) {
		List<ShareTaskApplicationsDetailDTO> details = detailsJpaRepository.findShareTaskApplicationsDetailsByTaskId(shareTaskApplicationsId);
		if (details != null && !details.isEmpty()){
			return BeanUtil.copyToList(details,ShareTaskApplicationsDetailsDomainEntity.class);
		}
		return null;
	}

	@Override
	public ShareTaskApplicationsDetailsDomainEntity findShareTaskApplicationsDetailsById(Long id) {
		ShareTaskApplicationsDetailsEntity details = detailsJpaRepository.findShareTaskApplicationsDetailsById(id);
		ShareTaskApplicationsDetailsDomainEntity domainEntity = new ShareTaskApplicationsDetailsDomainEntity();
		BeanUtils.copyProperties(details,domainEntity);
		return domainEntity;
	}
}
