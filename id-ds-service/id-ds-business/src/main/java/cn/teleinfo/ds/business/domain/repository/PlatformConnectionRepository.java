package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.PlatformConnectionType;

public interface PlatformConnectionRepository {

	/**
	 * 查询连接设置
	 *
	 * @param platformType 平台类型
	 * @return 连接设置信息
	 */
	PlatformConnectionDomainEntity findByPlatformType(PlatformConnectionType platformType);

	/**
	 * 更新连接设置
	 */
	void updateConnectionSetting(Integer platformType,String platformConnection);
}
