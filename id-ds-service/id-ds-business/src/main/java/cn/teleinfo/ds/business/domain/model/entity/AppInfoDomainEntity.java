package cn.teleinfo.ds.business.domain.model.entity;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AppInfoDomainEntity {

	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除: 0 未删除 null 已删除
	 */
	private Integer isDeleted;
	/**
	 * 应用原始id
	 */
	private Long sourceId;

	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 标识编码
	 */
	private String handleCode;

	/**
	 * 部署地址
	 */
	private String deployAddress;

	/**
	 * 系统版本
	 */
	private String sysVersion;

	/**
	 * 所属企业前缀
	 */
	private String entPrefix;
	private String entName;

	/**
	 * 所属省级前缀
	 */
	private String provincePrefix;
	private String provinceName;

	/**
	 * 类型: 1-中台应用,2-非中台应用
	 */
	private Integer appType;

	/**
	 * 共享源
	 */
	private ShareDataSourcesDomainEntity shareDataSources;
}
