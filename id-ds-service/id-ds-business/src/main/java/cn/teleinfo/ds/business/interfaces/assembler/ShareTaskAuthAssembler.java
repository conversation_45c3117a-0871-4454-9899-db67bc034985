package cn.teleinfo.ds.business.interfaces.assembler;

import cn.teleinfo.ds.business.application.query.ShareTaskAuthQuery;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskAuthsRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
public class ShareTaskAuthAssembler {

	public ShareTaskAuthQuery toQuery(ListShareTaskAuthsRequest request) {
		ShareTaskAuthQuery query = new ShareTaskAuthQuery();
		BeanUtils.copyProperties(request, query);
		return query;
	}
}
