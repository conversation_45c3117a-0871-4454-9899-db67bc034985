package cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection;

import java.time.LocalDateTime;

public interface ShareTaskApplicationsDTOView {


	Long getId();
	String getTaskName();
	String getTaskNo();
	Integer getTaskType();
	Long getTargetSourceId();
	Integer getApplicationsStatus();
	LocalDateTime getUpdateTime();
	String getUpdateByName();
	String getAppHandleCode();
	String getEntPrefix();
	String getGraph();
	String getEntName();
	String getAppName();
	String getTargetSource();
	String getCronExpression();
	String getItems();
	String getAuditRemark();
	String getCommittedUser();
}
