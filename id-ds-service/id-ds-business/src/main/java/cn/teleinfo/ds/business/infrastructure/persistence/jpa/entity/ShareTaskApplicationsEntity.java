package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

/**
 * 共享任务申请表
 */
@Getter
@Setter
@Entity
@Table(name = "t_share_task_applications")
@SQLDelete(sql = "update t_share_task_applications set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class ShareTaskApplicationsEntity extends AuditableEntity {

	/**
	 * 任务名称
	 */
	@Column(name = "task_name")
	private String taskName;

	/**
	 * 任务编号
	 */
	@Column(name = "task_no")
	private String taskNo;

	/**
	 * 任务类型(1：手动任务 2：定时任务)
	 */
	@Column(name = "task_type")
	private Integer taskType;

	/**
	 * 任务状态( 1:申请中 2:已驳回 3:已授权)
	 */
	@Column(name = "applications_status")
	private Integer applicationsStatus;

	/**
	 * 提交人
	 */
	@Column(name = "committed_by")
	private Long committedBy;

	/**
	 * 共享源类型
	 */
	@Column(name = "source_type")
	private String sourceType;

	/**
	 * 目标源ID
	 */
	@Column(name = "target_source_id")
	private Long targetSourceId;


	/**
	 * 省级前缀
	 */
	@Column(name = "province_prefix")
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	@Column(name = "ent_prefix")
	private String entPrefix;

	/**
	 * 应用身份编码
	 */
	@Column(name = "app_handle_code")
	private String appHandleCode;

	/**
	 * 图谱
	 */
	@Column(name = "graph")
	private String graph;

	/**
	 * CRON表达式，如0 0 12 * * ?（仅定时任务使用）
	 */
	@Column(name = "cron_expression")
	private String cronExpression;

}
