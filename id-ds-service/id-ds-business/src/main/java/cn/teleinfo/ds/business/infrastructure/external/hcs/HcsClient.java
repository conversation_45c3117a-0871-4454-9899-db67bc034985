package cn.teleinfo.ds.business.infrastructure.external.hcs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmJobResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmJobStatusResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CdmLinkResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.CreateJobTableDTO;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.HcsBaseResponse;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ListScriptResultsResponseDTO;
import cn.teleinfo.ds.business.infrastructure.external.hcs.dto.ScriptInfoDTO;
import com.huaweicloud.sdk.cdm.v1.CdmClient;
import com.huaweicloud.sdk.cdm.v1.model.CdmCreateAndUpdateLinkReq;
import com.huaweicloud.sdk.cdm.v1.model.CdmCreateJobJsonReq;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.ConfigValues;
import com.huaweicloud.sdk.cdm.v1.model.Configs;
import com.huaweicloud.sdk.cdm.v1.model.CreateJobRequest;
import com.huaweicloud.sdk.cdm.v1.model.CreateLinkRequest;
import com.huaweicloud.sdk.cdm.v1.model.DeleteJobRequest;
import com.huaweicloud.sdk.cdm.v1.model.DeleteLinkRequest;
import com.huaweicloud.sdk.cdm.v1.model.Input;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.cdm.v1.model.LinksLinkconfigvalues;
import com.huaweicloud.sdk.cdm.v1.model.ListClustersRequest;
import com.huaweicloud.sdk.cdm.v1.model.ListClustersResponse;
import com.huaweicloud.sdk.cdm.v1.model.ShowJobStatusRequest;
import com.huaweicloud.sdk.cdm.v1.model.ShowJobStatusResponse;
import com.huaweicloud.sdk.cdm.v1.model.ShowJobsRequest;
import com.huaweicloud.sdk.cdm.v1.model.ShowJobsResponse;
import com.huaweicloud.sdk.cdm.v1.model.ShowLinkRequest;
import com.huaweicloud.sdk.cdm.v1.model.ShowLinkResponse;
import com.huaweicloud.sdk.cdm.v1.model.StartJobRequest;
import com.huaweicloud.sdk.cdm.v1.model.StartJobResponse;
import com.huaweicloud.sdk.cdm.v1.model.Submission;
import com.huaweicloud.sdk.cdm.v1.model.UpdateLinkRequest;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.exception.ClientRequestException;
import com.huaweicloud.sdk.core.http.HttpConfig;
import com.huaweicloud.sdk.dataartsstudio.v1.DataArtsStudioClient;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigCommodityOrder;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigDataSourceView;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ColumnsList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.DatabasesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListColumnsRequest;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListColumnsResponse;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListDataArtsStudioInstancesRequest;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListDataArtsStudioInstancesResponse;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListDataTablesRequest;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListDataTablesResponse;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListDatabasesRequest;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListDatabasesResponse;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListDataconnectionsRequest;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListDataconnectionsResponse;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListManagerWorkSpacesRequest;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ListManagerWorkSpacesResponse;
import com.huaweicloud.sdk.dataartsstudio.v1.model.TablesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.Workspacebody;
import com.huaweicloud.sdk.dgc.v1.DgcClient;
import com.huaweicloud.sdk.dgc.v1.model.CreateScriptRequest;
import com.huaweicloud.sdk.dgc.v1.model.CreateScriptResponse;
import com.huaweicloud.sdk.dgc.v1.model.DeleteScriptRequest;
import com.huaweicloud.sdk.dgc.v1.model.DeleteScriptResponse;
import com.huaweicloud.sdk.dgc.v1.model.ExecuteScriptReq;
import com.huaweicloud.sdk.dgc.v1.model.ExecuteScriptRequest;
import com.huaweicloud.sdk.dgc.v1.model.ExecuteScriptResponse;
import com.huaweicloud.sdk.dgc.v1.model.ListScriptResultsRequest;
import com.huaweicloud.sdk.dgc.v1.model.ListScriptResultsResponse;
import com.huaweicloud.sdk.dgc.v1.model.ListScriptsRequest;
import com.huaweicloud.sdk.dgc.v1.model.ListScriptsResponse;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAuthProjectsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAuthProjectsResponse;
import cn.teleinfo.ds.common.core.exception.CheckedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 与华为云对接
 */
@Slf4j
@Component
public class HcsClient {

	private IamClient buildIamClient(String ak, String sk, List<String> endpoints) {
		// 认证凭证
		var auth = new BasicCredentials().withAk(ak).withSk(sk);
		// 禁用SSL校验
		var httpConfig = HttpConfig.getDefaultHttpConfig();
		httpConfig.setIgnoreSSLVerification(true);
		// 构建IAM客户端
		return IamClient.newBuilder()
				.withHttpConfig(httpConfig)
				.withCredential(auth)
				.withEndpoints(endpoints)
				.build();
	}

	private DataArtsStudioClient buildDataArtsStudioClient(String ak, String sk, String projectId,
														   List<String> endpoints) {
		// 认证凭证
		var auth = new BasicCredentials().withProjectId(projectId).withAk(ak).withSk(sk);
		// 禁用SSL校验
		var httpConfig = HttpConfig.getDefaultHttpConfig();
		httpConfig.setIgnoreSSLVerification(true);
		// 构建DataArtsStudioClient客户端
		return DataArtsStudioClient.newBuilder()
				.withCredential(auth)
				.withHttpConfig(httpConfig)
				.withEndpoints(endpoints)
				.build();
	}

	private CdmClient buildCdmClient(String ak, String sk, String projectId, List<String> endpoints) {
		// 认证凭证
		var auth = new BasicCredentials().withProjectId(projectId).withAk(ak).withSk(sk);
		// 禁用SSL校验
		var httpConfig = HttpConfig.getDefaultHttpConfig();
		httpConfig.setIgnoreSSLVerification(true);
		// 构建CDM客户端
		return CdmClient.newBuilder()
				.withHttpConfig(httpConfig)
				.withCredential(auth)
				.withEndpoints(endpoints)
				.build();
	}

	private DgcClient buildDgcClient(String ak, String sk, String projectId, List<String> endpoints) {
		// 认证凭证
		var auth = new BasicCredentials().withProjectId(projectId).withAk(ak).withSk(sk);
		// 禁用SSL校验
		var httpConfig = HttpConfig.getDefaultHttpConfig();
		httpConfig.setIgnoreSSLVerification(true);
		// 构建Dgc客户端
		return DgcClient.newBuilder()
				.withHttpConfig(httpConfig)
				.withCredential(auth)
				.withEndpoints(endpoints)
				.build();
	}

	// 获取资源空间列表
	public List<AuthProjectResult> keystoneListAuthProjects(String ak, String sk, List<String> endpoints) {
		KeystoneListAuthProjectsRequest request = new KeystoneListAuthProjectsRequest();
		KeystoneListAuthProjectsResponse response = this.buildIamClient(ak, sk, endpoints)
				.keystoneListAuthProjects(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取资源空间列表列表错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取资源空间列表列表错误");
		}

		return response.getProjects();
	}

	// 获取数据治理中心实例列表
	public List<ApigCommodityOrder> listDataArtsStudioInstances(String ak, String sk, String projectId,
																List<String> endpoints, Integer offset, Integer limit) {
		ListDataArtsStudioInstancesRequest request = new ListDataArtsStudioInstancesRequest();
		request.withLimit(limit);
		request.withOffset(offset(offset));
		ListDataArtsStudioInstancesResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints)
				.listDataArtsStudioInstances(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取数据治理中心实例列表错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取数据治理中心实例列表错误");
		}

		return response.getCommodityOrders();
	}

	// 获取工作空间列表
	public List<Workspacebody> listManagerWorkSpacesRequest(String ak, String sk, String projectId, String instanceId,
															List<String> endpoints, Integer offset, Integer limit) {
		ListManagerWorkSpacesRequest request = new ListManagerWorkSpacesRequest();
		request.withInstanceId(instanceId);
		request.withLimit(limit);
		request.withOffset(offset(offset));
		ListManagerWorkSpacesResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints)
				.listManagerWorkSpaces(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取工作空间列表错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取工作空间列表错误");
		}

		return response.getData();
	}

	// 获取CDM集群名称列表
	public List<Clusters> listClusters(String ak, String sk, String projectId, List<String> endpoints) {
		ListClustersRequest request = new ListClustersRequest();
		ListClustersResponse response = this.buildCdmClient(ak, sk, projectId, endpoints).listClusters(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取CDM集群名称列表错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取CDM集群名称列表错误");
		}

		return response.getClusters();
	}

	// 数据集成-规范层连接名称
	public List<Links> listConnections(String ak, String sk, String projectId, List<String> endpoints,
									   String clusterId) {
		ShowLinkRequest request = new ShowLinkRequest();
		request.setClusterId(clusterId);
		request.setLinkName("all");
		ShowLinkResponse response = this.buildCdmClient(ak, sk, projectId, endpoints).showLink(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取数据集成-规范层连接名称错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取数据集成-规范层连接名称错误");
		}

		return response.getLinks();
	}

	// 数据开发-规范层连接名称
	public List<ApigDataSourceView> listDataConnections(String ak, String sk, String projectId,
														List<String> endpoints, String workspace, Integer offset, Integer limit) {
		ListDataconnectionsRequest request = new ListDataconnectionsRequest();
		request.withWorkspace(workspace);
		request.withLimit(String.valueOf(limit));
		request.withOffset(String.valueOf(offset(offset)));
		ListDataconnectionsResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints)
				.listDataconnections(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取数据开发-规范层连接名称称错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取数据开发-规范层连接名称称错误");
		}

		return response.getDataConnectionLists();
	}

	// 数据开发-规范层数据库名称
	public List<DatabasesList> listDataDatabases(String ak, String sk, String projectId,
												 List<String> endpoints, String workspace, String connectionId, Integer offset, Integer limit) {
		ListDatabasesRequest request = new ListDatabasesRequest();
		request.withWorkspace(workspace);
		request.withConnectionId(connectionId);
		request.withLimit(String.valueOf(limit));
		request.withOffset(String.valueOf(offset(offset)));
		ListDatabasesResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints)
				.listDatabases(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取数据开发-规范层数据库名称错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取数据开发-规范层数据库名称错误");
		}

		return response.getDatabases();
	}

	// 获取所有作业
	public List<Job> listJobs(String ak, String sk, String projectId, List<String> endpoints, String clusterId) {
		ShowJobsRequest request = new ShowJobsRequest();
		request.setClusterId(clusterId);
		request.setJobName("all");
//		request.setFilter("inddb");
		ShowJobsResponse response = this.buildCdmClient(ak, sk, projectId, endpoints).showJobs(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取所有作业错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取所有作业错误");
		}
		return response.getJobs();
	}

	// 创建脚本
	public void createScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName,
							 String scriptContent, String workspace, String databaseName, String connectionName) {

		CreateScriptRequest request = new CreateScriptRequest();
		request.withWorkspace(workspace);
		ScriptInfo body = new ScriptInfo();
		body.withContent(scriptContent);
		body.withDirectory("/Temp");
		body.withType(ScriptInfo.TypeEnum.fromValue("HiveSQL"));
		body.withName(scriptName);
		body.withConnectionName(connectionName);
		body.withDatabase(databaseName);
		request.withBody(body);
		CreateScriptResponse response = this.buildDgcClient(ak, sk, projectId, endpoints).createScript(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("创建脚本错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("创建脚本错误");
		}
	}

	// 查询脚本
	public ScriptInfo findScript(String ak, String sk, String projectId, List<String> endpoints, String workspace, String scriptName) {
		ListScriptsRequest request = new ListScriptsRequest();
		request.withWorkspace(workspace);
		request.withLimit(20);
		request.setScriptName(scriptName);
		request.withOffset(0);

		ListScriptsResponse response = this.buildDgcClient(ak, sk, projectId, endpoints).listScripts(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("查询脚本错误 httpStatusCode={}", response.getHttpStatusCode());
			return null;
		}

		if (response.getScripts() == null || response.getScripts().isEmpty()) {
			return null;
		}

		return response.getScripts().get(0);
	}

	// 查询所有脚本
	public ScriptInfoDTO findScriptList(String ak, String sk, String projectId, List<String> endpoints, String workspace, Integer limit, Integer offset) {
		ListScriptsRequest request = new ListScriptsRequest();
		request.withWorkspace(workspace);
		request.withLimit(limit);
		request.withOffset(offset);
		ListScriptsResponse response = this.buildDgcClient(ak, sk, projectId, endpoints).listScripts(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("查询脚本错误 httpStatusCode={}", response.getHttpStatusCode());
			return null;
		}

		if (response.getScripts() == null || response.getScripts().isEmpty()) {
			return null;
		}
		ScriptInfoDTO scriptInfoDTO = new ScriptInfoDTO();
		scriptInfoDTO.setScriptInfo(response.getScripts());
		scriptInfoDTO.setTotal(response.getTotal());
		return scriptInfoDTO;
	}


	// 删除脚本
	public void deleteScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String workspace) {
		DeleteScriptRequest request = new DeleteScriptRequest();
		request.setScriptName(scriptName);
		request.setWorkspace(workspace);
		DeleteScriptResponse response = this.buildDgcClient(ak, sk, projectId, endpoints).deleteScript(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("删除脚本错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("删除脚本错误");
		}
	}


	// 执行脚本
	public String executeScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName,
								String workspace) {
		ExecuteScriptRequest request = new ExecuteScriptRequest();
		request.withWorkspace(workspace);
		request.withScriptName(scriptName);
		ExecuteScriptReq body = new ExecuteScriptReq();
		//body.withParams("{}");
		request.withBody(body);
		ExecuteScriptResponse response = this.buildDgcClient(ak, sk, projectId, endpoints).executeScript(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("执行脚本错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("执行脚本错误");
		}
		return response.getInstanceId();
	}

	// 查询脚本实例执行结果
	public ListScriptResultsResponseDTO listScriptResults(String ak, String sk, String projectId, List<String> endpoints,
														  String scriptName, String workspace, String instanceId) {
		ListScriptResultsRequest request = new ListScriptResultsRequest();
		request.withWorkspace(workspace);
		request.withScriptName(scriptName);
		request.withInstanceId(instanceId);
		ListScriptResultsResponse response = this.buildDgcClient(ak, sk, projectId, endpoints)
				.listScriptResults(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("查询脚本实例执行结果错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("查询脚本实例执行结果错误");
		}
		ListScriptResultsResponseDTO responseDTO = new ListScriptResultsResponseDTO();
		responseDTO.setStatus(response.getStatus());
		responseDTO.setMessage(response.getMessage());
		return responseDTO;
	}

	//获取数据源中的表
	public List<TablesList> listTables(String ak, String sk, String projectId, List<String> endpoints, String connectionId,
									   String databaseName, String tableName, String workSpace) {
		ListDataTablesRequest request = new ListDataTablesRequest();
		request.withConnectionId(connectionId);
		request.withDatabaseName(databaseName);
		request.withTableName(tableName);
		request.withWorkspace(workSpace);
		request.withOffset("0");
		request.withLimit("1000");
		ListDataTablesResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints).listDataTables(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取数据源中的表错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取数据源中的表错误");
		}
		return response.getTables();
	}

	//获取表的字段
	public List<ColumnsList> listTableColumns(String ak, String sk, String projectId, List<String> endpoints, String connectionId, String workSpace,
											  String tableId) {
		ListColumnsRequest request = new ListColumnsRequest();
		request.withConnectionId(connectionId);
		request.withWorkspace(workSpace);
		request.withTableId(tableId);
		ListColumnsResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints).listColumns(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("<获取表字段错误> httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取表的字段错误");
		}
		return response.getColumns();


	}

	/**
	 * 创建作业
	 *
	 * @param ak           ak
	 * @param sk           sk
	 * @param projectId    projectId
	 * @param endpoints    endpoints
	 * @param clusterId    集群 Id
	 * @param table        迁移的表信息
	 * @param fromLinkName 源链接名称
	 * @param fromDatabase 源数据库
	 * @param toLinkName   目标链接名称
	 * @param toDatabase   目标数据库
	 */
	public HcsBaseResponse createJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId,
									 CreateJobTableDTO table, String fromLinkName, String fromDatabase,
									 String toLinkName, String toDatabase
	) {
		var tableName = table.getTableName().toLowerCase();
		var columnList = CollUtil.join(table.getColumnList(), "&").toLowerCase();

		// 一、作业任务参数配置。例如配置作业失败重试、抽取并发数，具体可参考作业任务参数说明。
		List<Input> driverConfigsInputs = List.of(
				// 输入参数列表，列表中的每个参数为"name,value"结构，请参考inputs数据结构参数说明。
				new Input()
						.withName("groupJobConfig.groupName")
						.withValue("DEFAULT")
		);
		// 源连接参数、目的连接参数和作业任务参数，它们的配置数据结构相同，其中"inputs"里的参数不一样，详细请参见configs数据结构说明。
		List<Configs> driverConfigs = new ArrayList<>();
		driverConfigs.add(
				new Configs()
						.withInputs(driverConfigsInputs)
						.withName("groupJobConfig")
		);
		ConfigValues driverConfigValues = new ConfigValues();
		driverConfigValues.withConfigs(driverConfigs);

		// 二、源连接参数配置。根据不同源端有不同的参数配置，具体可参考源端作业参数说明下相应的源端参数配置。
		List<Input> fromConfigsInputs = List.of(
				new Input().withName("fromJobConfig.hive").withValue("hive"),
				new Input().withName("fromJobConfig.database").withValue(fromDatabase),
				new Input().withName("fromJobConfig.table").withValue(tableName),
				new Input().withName("fromJobConfig.columnList").withValue(columnList)
		);

		List<Configs> fromConfigs = new ArrayList<>();
		fromConfigs.add(new Configs().withInputs(fromConfigsInputs).withName("fromJobConfig"));
		ConfigValues fromConfigValues = new ConfigValues();
		fromConfigValues.withConfigs(fromConfigs);

		// 三、目的连接参数配置。根据不同目的端有不同的参数配置，具体可参考目的端作业参数说明下相应的目的端参数配置。
		List<Input> toConfigsInputs = List.of(
				new Input().withName("toJobConfig.schemaName").withValue(toDatabase),
				// DO_NOTHING 不自动建表
				// CREATE_WHEN_NOT_EXIST  不存在建表
				// DROP_AND_CREATE 先删除再建表
				// new Input().withName("toJobConfig.tablePreparation").withValue("DO_NOTHING"), // 从 Hive 到关系数据库这个是不好使的。
				new Input().withName("toJobConfig.tableName").withValue(tableName),
				new Input().withName("toJobConfig.columnList").withValue(columnList),
				new Input().withName("toJobConfig.beforeImportType").withValue("shouldClearTable")
		);
		List<Configs> toConfigs = new ArrayList<>();
		toConfigs.add(
				new Configs()
						.withInputs(toConfigsInputs)
						.withName("toJobConfig")
		);
		ConfigValues toConfigValues = new ConfigValues();
		toConfigValues.withConfigs(toConfigs);
		// 四、创建作业
		Job job = new Job()
				// 作业类型：NORMAL_JOB：表/文件迁移、BATCH_JOB：整库迁移、SCENARIO_JOB：场景迁移。
				.withJobType(Job.JobTypeEnum.fromValue("NORMAL_JOB"))
				// 源端连接类型 generic-jdbc-connector：关系数据库连接 hive-connector：Hive连接 ...等
				.withFromConnectorName("hive-connector")
				// 源连接参数配置
				.withFromConfigValues(fromConfigValues)
				// 源连接名称，即为通过"创建连接"接口创建的连接对应的连接名。
				.withFromLinkName(fromLinkName) // bsjx-hive-test
				// 目的端连接类型 generic-jdbc-connector：关系数据库连接 hive-connector：Hive连接 ...等
				.withToConnectorName("generic-jdbc-connector")
				// 目的连接参数配置
				.withToConfigValues(toConfigValues)
				// 目的端连接名称，即为通过"创建连接"接口创建的连接对应的连接名。
				.withToLinkName(toLinkName) // bsjx-dws-test
				// 作业任务参数配置
				.withDriverConfigValues(driverConfigValues)
				// 作业名称，长度在1到240个字符之间。
				.withName(tableName);

		List<Job> jobs = List.of(job);

		CreateJobRequest request = new CreateJobRequest();
		request.withClusterId(clusterId);
		CdmCreateJobJsonReq body = new CdmCreateJobJsonReq();
		body.withJobs(jobs);
		request.withBody(body);

		HcsBaseResponse response = new HcsBaseResponse();
		try {
			this.buildCdmClient(ak, sk, projectId, endpoints).createJob(request);
		} catch (ClientRequestException e) {
			log.error("创建 CDM 作业错误 request={}",JSONUtil.toJsonPrettyStr(request), e);
			response.setErrorCode(e.getErrorCode());
			response.setErrorMsg(e.getErrorMsg());
		}


		return response;
	}

	public HcsBaseResponse startJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName) {
		StartJobRequest request = new StartJobRequest();
		request.setClusterId(clusterId);
		request.setJobName(jobName);
		HcsBaseResponse response = new HcsBaseResponse();
		try {
			this.buildCdmClient(ak, sk, projectId, endpoints).startJob(request);
		} catch (ClientRequestException e) {
			log.error("启动作业失败 request={}",JSONUtil.toJsonPrettyStr(request), e);
			response.setErrorCode(e.getErrorCode());
			response.setErrorMsg(e.getErrorMsg());
		}
		return response;
	}

	public CdmJobStatusResponse showJobStatus(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName) {
		ShowJobStatusRequest request = new ShowJobStatusRequest();
		request.setJobName(jobName);
		request.setClusterId(clusterId);

		CdmJobStatusResponse resp = new CdmJobStatusResponse();
		try {
			ShowJobStatusResponse response = this.buildCdmClient(ak, sk, projectId, endpoints).showJobStatus(request);
			resp.setSubmissions(response.getSubmissions());
		} catch (ClientRequestException e) {
			log.error("查询作业状态失败 request={}",JSONUtil.toJsonPrettyStr(request), e);
			resp.setErrorCode(e.getErrorCode());
			resp.setErrorMsg(e.getErrorMsg());
		}

		return resp;
	}

	public CdmJobResponse showJobs(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName) {
		ShowJobsRequest request = new ShowJobsRequest();
		request.setClusterId(clusterId);
		request.setJobName(jobName);

		CdmJobResponse resp = new CdmJobResponse();

		try {
			ShowJobsResponse response = this.buildCdmClient(ak, sk, projectId, endpoints).showJobs(request);
			resp.setJobs(response.getJobs());
		} catch (ClientRequestException e) {
			log.error("查询作业失败 request={}",JSONUtil.toJsonPrettyStr(request), e);
			resp.setErrorCode(e.getErrorCode());
			resp.setErrorMsg(e.getErrorMsg());
		}

		return resp;
	}

	public HcsBaseResponse deleteJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName) {
		DeleteJobRequest request = new DeleteJobRequest();
		request.setJobName(jobName);
		request.setClusterId(clusterId);
		HcsBaseResponse response = new HcsBaseResponse();
		try {
			this.buildCdmClient(ak, sk, projectId, endpoints).deleteJob(request);
		} catch (ClientRequestException e) {
			log.error("删除作业失败 request={}",JSONUtil.toJsonPrettyStr(request), e);
			response.setErrorCode(e.getErrorCode());
			response.setErrorMsg(e.getErrorMsg());
		}
		return response;
	}

	// 创建连接
	// cdm 作业使用
	public HcsBaseResponse createLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId,
									  String host, Integer port, String database, String username, String password, String linkName) {
		CreateLinkRequest request = new CreateLinkRequest();
		request.withClusterId(clusterId);
		//request.withValidate("true"); // 为“true”时，此API仅校验参数是否正确，不创建连接。

		LinksLinkconfigvalues linksLinkconfigvalues = new LinksLinkconfigvalues();
		linksLinkconfigvalues.withConfigs(List.of(
				new Configs().withName("linkConfig")
						.withInputs(List.of(
								new Input().withName("linkConfig.databaseType").withValue("DWS"),
								new Input().withName("linkConfig.host").withValue(host),
								new Input().withName("linkConfig.port").withValue(port + ""),
								new Input().withName("linkConfig.database").withValue(database),
								new Input().withName("linkConfig.username").withValue(username),
								new Input().withName("linkConfig.password").withValue(password)
						))
		));

		Links links = new Links();
		links.withLinkConfigValues(linksLinkconfigvalues);
		links.withName(linkName);
		links.withConnectorName("generic-jdbc-connector");

		CdmCreateAndUpdateLinkReq body = new CdmCreateAndUpdateLinkReq();
		body.withLinks(List.of(links));

		request.withBody(body);

		HcsBaseResponse response = new HcsBaseResponse();
		try {
			this.buildCdmClient(ak, sk, projectId, endpoints).createLink(request);
		} catch (ClientRequestException e) {
			log.error("创建 cdm 连接失败 request={}", JSONUtil.toJsonPrettyStr(request), e);
			response.setErrorCode(e.getErrorCode());
			response.setErrorMsg(e.getErrorMsg());
		}
		return response;
	}


	// 查询连接
	public CdmLinkResponse listConnections(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String linkName) {
		CdmLinkResponse response = new CdmLinkResponse();

		ShowLinkRequest request = new ShowLinkRequest();
		request.setClusterId(clusterId);
		request.setLinkName(linkName);
		try {
			ShowLinkResponse showLinkResponse = this.buildCdmClient(ak, sk, projectId, endpoints).showLink(request);
			response.setLinks(showLinkResponse.getLinks());
			response.setFromToUnMapping(showLinkResponse.getFromToUnMapping());
			response.setBatchFromToMapping(showLinkResponse.getBatchFromToMapping());
		} catch (ClientRequestException e) {
			log.error("查询 cdm 连接失败 request={}", JSONUtil.toJsonPrettyStr(request), e);
			response.setErrorCode(e.getErrorCode());
			response.setErrorMsg(e.getErrorMsg());
		}
		return response;
	}

	// 更新连接
	public HcsBaseResponse updateLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId,
									  String host, Integer port, String database, String username, String password, String linkName) {
		UpdateLinkRequest request = new UpdateLinkRequest();
		request.withClusterId(clusterId);
		request.withLinkName(linkName);

		LinksLinkconfigvalues linksLinkconfigvalues = new LinksLinkconfigvalues();
		linksLinkconfigvalues.withConfigs(List.of(
				new Configs().withName("linkConfig")
						.withInputs(List.of(
								new Input().withName("linkConfig.databaseType").withValue("DWS"),
								new Input().withName("linkConfig.host").withValue(host),
								new Input().withName("linkConfig.port").withValue(port + ""),
								new Input().withName("linkConfig.database").withValue(database),
								new Input().withName("linkConfig.username").withValue(username),
								new Input().withName("linkConfig.password").withValue(password)
						))
		));

		Links links = new Links();
		links.withLinkConfigValues(linksLinkconfigvalues);
		links.withName(linkName);
		links.withConnectorName("generic-jdbc-connector");

		CdmCreateAndUpdateLinkReq body = new CdmCreateAndUpdateLinkReq();
		body.withLinks(List.of(links));
		request.withBody(body);
		HcsBaseResponse response = new HcsBaseResponse();
		try {
			this.buildCdmClient(ak, sk, projectId, endpoints).updateLink(request);
		} catch (ClientRequestException e) {
			log.error("更新 cdm 连接失败 request={}", JSONUtil.toJsonPrettyStr(request), e);
			response.setErrorCode(e.getErrorCode());
			response.setErrorMsg(e.getErrorMsg());
		}

		return response;
	}

	public HcsBaseResponse delLink(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String linkName) {
		DeleteLinkRequest request = new DeleteLinkRequest();
		request.withClusterId(clusterId);
		request.withLinkName(linkName);
		HcsBaseResponse response = new HcsBaseResponse();
		try {
			this.buildCdmClient(ak, sk, projectId, endpoints).deleteLink(request);
		} catch (ClientRequestException e) {
			log.error("删除 cdm 连接失败 request={}", JSONUtil.toJsonPrettyStr(request), e);
			response.setErrorCode(e.getErrorCode());
			response.setErrorMsg(e.getErrorMsg());
		}

		return response;
	}

	private Integer offset(Integer offset) {
		return offset >= 1 ? offset - 1 : offset;
	}

	private boolean isOK(int status) {
		return status >= 200 && status < 300;
	}
}
