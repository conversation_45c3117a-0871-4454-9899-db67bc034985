package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.application.query.ShareTaskAuthQuery;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskAuthEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import cn.teleinfo.ds.common.core.util.PageResponse;

import java.util.List;

public interface ShareTaskAuthRepository {

	// 查询当前已经授权的记录
	ShareTaskAuthDomainEntity findShareTaskAuthByPass(Long shareTaskApplicationsId);


	List<ShareTaskAuthView> findShareTaskAuthsByApplicationId(Long applicationId);

	// 保存授权记录
	Long save(ShareTaskAuthDomainEntity shareTaskAuthDomainEntity);

	PageResponse<ShareTaskAuthView> listShareTaskAuths(ShareTaskAuthQuery query, Integer page, Integer size);

	ShareTaskAuthDomainEntity findById(Long id);


}
