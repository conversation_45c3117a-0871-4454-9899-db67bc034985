package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.teleinfo.ds.business.application.query.ListShareChannelApplicationsQuery;
import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.domain.model.entity.DataChannelDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannelDetectLog;
import cn.teleinfo.ds.business.domain.repository.ShareChannelRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.DataChannelEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelDetectLogEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsDetailView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.DataChannelJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareChannelDetectLogJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareChannelJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.mapper.ShareChannelMapper;
import cn.teleinfo.ds.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@AllArgsConstructor
@Component
public class ShareChannelRepositoryImpl implements ShareChannelRepository {

	private final ShareChannelJpaRepository shareChannelJpaRepository;

	private final ShareChannelMapper shareChannelMapper;

	private final DataChannelJpaRepository dataChannelJpaRepository;

	private final ShareChannelDetectLogJpaRepository shareChannelDetectLogJpaRepository;

	/**
	 * 查询共享通道列表
	 */
	@Override
	public PageResponse<ShareChannelsDTO> listShareChannels(ListShareChannelsQuery query, Integer current, Integer size) {
		int pageIndex = (current > 0) ? current - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		var results = shareChannelJpaRepository.listShareChannels(query.getChannelName(), query.getHandle(),
				query.getDetectionStatus(), query.getStartTime(), query.getEndTime(), query.getAppHandle(), query.getUserId(), pageable);

		return new PageResponse<>(results.toList(), results.getTotalElements(), (long) size, (long) current,
				(long) results.getTotalPages());
	}

	@Override
	public ShareChannelsDetailsDTO queryDetailsById(String id) {
		return shareChannelJpaRepository.queryDetailsById(id);
	}

	@Override
	public List<ShareChannelsDetailsDTO> listShareChannelsVersion(String shareChannelId) {
		return shareChannelJpaRepository.listShareChannelsVersion(shareChannelId);
	}

	@Override
	public ShareChannelsVersionSqlView queryVersionSql(String shareChannelId, String main_version,
													   String minor_version) {
		return shareChannelJpaRepository.queryVersionSql(shareChannelId, main_version, minor_version);
	}

	@Override
	public List<ShareChannelsVersionDTO> queryShareChannelsVersionDTO(String shareChannelId) {
		return shareChannelJpaRepository.queryShareChannelsVersionDTO(shareChannelId);
	}

	@Override
	public PageResponse<ShareChannelsApplicationsView> listShareChannelsApplications(ListShareChannelApplicationsQuery query,
																					 Integer current, Integer size) {
		int pageIndex = (current > 0) ? current - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		var results = shareChannelJpaRepository.listShareChannelsApplications(query.getChannelName(), query.getHandle(),
				query.getChannelStatus(), query.getStartTime(), query.getEndTime(), query.getAppHandle(), pageable);

		return new PageResponse<>(results.toList(), results.getTotalElements(), (long) size, (long) current,
				(long) results.getTotalPages());
	}

	@Override
	public ShareChannelsApplicationsDetailView queryShareChannelsApplicationDetails(String applicationId) {
		return shareChannelJpaRepository.queryShareChannelsApplicationDetails(applicationId);
	}

	@Override
	public List<ShareChannel> findShareChannelsByDataChannelId(Long dataChannelId) {
		List<ShareChannel> shareChannelList = new ArrayList<>();
		List<ShareChannelEntity> channels = shareChannelJpaRepository.findByDataChannelId(dataChannelId);
		if (channels.isEmpty()) {
			return shareChannelList;
		}
		for (ShareChannelEntity channel : channels) {
			ShareChannel shareChannel = shareChannelMapper.toShareChannel(channel);
			shareChannelList.add(shareChannel);
		}
		return shareChannelList;
	}

	@Override
	public void saveShareChannel(ShareChannel shareChannel) {
		ShareChannelEntity shareChannelEntity = shareChannelMapper.toShareChannelEntity(shareChannel);
		shareChannelJpaRepository.save(shareChannelEntity);

	}

	@Override
	public ShareChannel findByShareChannelIdAndChannelStatus(Long shareChannelId) {
		ShareChannelEntity shareChannel = shareChannelJpaRepository.findByShareChannelIdAndChannelStatus(shareChannelId, 1);
		return shareChannelMapper.toShareChannel(shareChannel);
	}

	@Override
	public ShareChannel findByDataChannelIdAndChannelStatus(Long dataChannelId) {
		ShareChannelEntity shareChannel = shareChannelJpaRepository.findByDataChannelIdAndChannelStatus(dataChannelId, 1);
		return shareChannelMapper.toShareChannel(shareChannel);
	}

	@Override
	public DataChannelDomainEntity findDataChannel(Long shareChannelId) {
		Optional<DataChannelEntity> dataChannel = dataChannelJpaRepository.findById(shareChannelId);
		return dataChannel.map(shareChannelMapper::toDataChannelDomainEntity).orElse(null);
	}

	@Override
	public ShareChannel findMaxVersion(Long shareChannelId) {
		ShareChannelEntity shareChannel = shareChannelJpaRepository.findMaxVersionByShareChannelId(shareChannelId);
		return shareChannelMapper.toShareChannel(shareChannel);
	}

	@Override
	public List<String> queryChannelVersions(String shareChannelId) {
		return shareChannelJpaRepository.queryChannelVersions(shareChannelId);
	}

	@Override
	public ShareChannel findByShareChannelIdAndVersion(Long shareChannelId, Integer mainVersion, Integer minorVersion) {
		ShareChannelEntity shareChannel = shareChannelJpaRepository
				.findByShareChannelIdAndMainVersionAndMinorVersion(shareChannelId, mainVersion, minorVersion);
		return shareChannelMapper.toShareChannel(shareChannel);
	}

	@Override
	public List<ShareChannel> findAllShareChannel() {
		List<ShareChannelEntity> detectChannel = shareChannelJpaRepository.findDetectChannel();
		return shareChannelMapper.toShareChannelList(detectChannel);
	}

	@Override
	public List<ShareChannel> findByShareChannelIdAndDetectionStatus(Long shareChannelId, Integer status) {
		List<ShareChannelEntity> shareChannelList = shareChannelJpaRepository
				.findAllByShareChannelIdAndDetectionStatus(shareChannelId, status);
		return shareChannelMapper.toShareChannelList(shareChannelList);
	}

	@Override
	public ShareChannelDetectLog findShareChannelLog(Long shareChannelId) {
		List<ShareChannelDetectLogEntity> shareChannelDetectLogEntities = shareChannelDetectLogJpaRepository.findByShareChannelIdOrderByUpdateTimeDesc(shareChannelId);
		if (shareChannelDetectLogEntities.isEmpty()) {
			return null;
		}
		ShareChannelDetectLogEntity shareChannelDetectLogEntity = shareChannelDetectLogEntities.get(0);
		return shareChannelMapper.toShareChannelDetectLog(shareChannelDetectLogEntity);


	}

	@Override
	public ShareChannelDetectLog findShareChannelLog(Long shareChannelId, Integer mainVersion, Integer minorVersion) {
		List<ShareChannelDetectLogEntity> shareChannelDetectLogEntities = shareChannelDetectLogJpaRepository.findByShareChannelIdAndMainVersionAndMinorVersionOrderByUpdateTimeDesc(shareChannelId, mainVersion, minorVersion);
		if (shareChannelDetectLogEntities.isEmpty()) {
			return null;
		}
		ShareChannelDetectLogEntity shareChannelDetectLogEntity = shareChannelDetectLogEntities.get(0);
		return shareChannelMapper.toShareChannelDetectLog(shareChannelDetectLogEntity);
	}

	@Override
	public void saveShareChannelLog(ShareChannelDetectLog shareChannelDetectLog) {
		ShareChannelDetectLogEntity shareChannelDetectLogEntity = new ShareChannelDetectLogEntity();
		shareChannelDetectLogEntity.setShareChannelId(shareChannelDetectLog.getShareChannelId());
		shareChannelDetectLogEntity.setLogPath(shareChannelDetectLog.getLogPath());
		shareChannelDetectLogEntity.setConnectTest(shareChannelDetectLog.getConnectTest());
		shareChannelDetectLogEntity.setExecTest(shareChannelDetectLog.getExecTest());
		shareChannelDetectLogEntity.setGrammarCheck(shareChannelDetectLog.getGrammarCheck());
		shareChannelDetectLogEntity.setMainVersion(shareChannelDetectLog.getMainVersion());
		shareChannelDetectLogEntity.setMinorVersion(shareChannelDetectLog.getMinorVersion());
		shareChannelDetectLogEntity.setCreateTime(LocalDateTime.now());
		shareChannelDetectLogEntity.setUpdateTime(LocalDateTime.now());
		shareChannelDetectLogEntity.setId(shareChannelDetectLog.getId());
		shareChannelDetectLogJpaRepository.save(shareChannelDetectLogEntity);

	}

	@Override
	public void updateDetectStatus(ShareChannelDetectLog shareChannelDetectLog) {
		Optional<ShareChannelDetectLogEntity> entity = shareChannelDetectLogJpaRepository.findById(shareChannelDetectLog.getId());
		entity.ifPresent(shareChannelDetectLogEntity -> {
			shareChannelDetectLogEntity.setExecTest(shareChannelDetectLog.getExecTest());
			shareChannelDetectLogEntity.setUpdateTime(LocalDateTime.now());
			shareChannelDetectLogJpaRepository.save(shareChannelDetectLogEntity);
		});
	}

	@Override
	public ShareChannel findById(Long id) {
		Optional<ShareChannelEntity> entity = shareChannelJpaRepository.findById(id);
		return entity.map(shareChannelMapper::toShareChannel).orElse(null);
	}

	@Override
	public List<ShareChannel> findAllByShareChannelId(Long shareChannelId) {
		List<ShareChannelEntity> detectChannel = shareChannelJpaRepository.findAllByShareChannelId(shareChannelId);
		return shareChannelMapper.toShareChannelList(detectChannel);
	}

	@Override
	public void saveAll(List<ShareChannel> changeList) {
		List<ShareChannelEntity> shareChannelEntityList = shareChannelMapper.toShareChannelEntityList(changeList);
		shareChannelJpaRepository.saveAll(shareChannelEntityList);
	}

}
