package cn.teleinfo.ds.business.domain.service;


import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.valueobject.Graph;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.GraphHandlesDTO;

import java.util.List;

public interface HandleGraphDomainService {
    Graph handleChildren(String handle);

    Graph handleGraph(String handle);

    /**
     * 对象标识模糊查询
     */
    List<GraphHandlesDTO> getGraphHandles(String handle);

    Handle handleItems(String handle);
}
