server:
  port: 6000

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: nacos
      password: KGOy5JmDdh*TU&Noamjg
      discovery:
        server-addr: ${NACOS_HOST:*************}:${NACOS_PORT:30848}
        namespace: ${NACOS_NAMESPACE:id-ds-dev}
        group: liheng
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
  config:
    import:
      - nacos:<EMAIL>@.yml
      - nacos:${spring.application.name}-@profiles.active@.yml
  jpa:
    show-sql: true

# 雪花算法
id:
  snowflake:
    # 终端ID
    worker-id: 1
    # 数据中心ID
    datacenter-id: 1
api:
  ip: http://id-yc-province-manage-web.test.idx.space

business:
    log-path: ./logs