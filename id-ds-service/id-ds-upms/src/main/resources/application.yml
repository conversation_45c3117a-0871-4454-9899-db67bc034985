server:
  port: 4000

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: nacos
      password: KGOy5JmDdh*TU&Noamjg
      discovery:
        server-addr: ${NACOS_HOST:*************}:${NACOS_PORT:30848}
        namespace: ${NACOS_NAMESPACE:id-ds-dev}
        group: liheng
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
  config:
    import:
      - nacos:<EMAIL>@.yml
      - nacos:${spring.application.name}-@profiles.active@.yml
api:
  ip: http://127.0.0.1:6100

