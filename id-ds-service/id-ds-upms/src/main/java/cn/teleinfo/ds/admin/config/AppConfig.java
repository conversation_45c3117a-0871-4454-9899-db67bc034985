package cn.teleinfo.ds.admin.config;

import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AppConfig {


	@Bean
	public OkHttpClient okHttpClient() {
		return new OkHttpClient.Builder()
//                .retryOnConnectionFailure(false)
//                .connectTimeout(10, TimeUnit.SECONDS)
//                .writeTimeout(10, TimeUnit.SECONDS)
//                .readTimeout(10, TimeUnit.SECONDS)
				.build();
	}
}
