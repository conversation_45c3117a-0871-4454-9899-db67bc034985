package cn.teleinfo.ds.admin.mapper;

import cn.teleinfo.ds.upms.api.vo.AppInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.teleinfo.ds.upms.api.entity.SysPost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 岗位信息表
 *
 * <AUTHOR>
 * @date 2022-03-26 12:50:43
 */
@Mapper
public interface SysPostMapper extends BaseMapper<SysPost> {

	/**
	 * 通过用户ID，查询岗位信息
	 * @param userId 用户id
	 * @return 岗位信息
	 */
	List<SysPost> listPostsByUserId(Long userId);

	List<AppInfoVO> getAppListByHandleCode(@Param("query") List<String> codeList);

}
