package cn.teleinfo.ds.admin.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.teleinfo.ds.admin.mapper.SysUserMapper;
import cn.teleinfo.ds.common.core.constant.UserConstants;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.common.security.service.PigUser;
import cn.teleinfo.ds.common.security.util.SecurityUtils;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.teleinfo.ds.upms.api.dto.SysLogDTO;
import cn.teleinfo.ds.upms.api.entity.SysLog;
import cn.teleinfo.ds.admin.mapper.SysLogMapper;
import cn.teleinfo.ds.admin.service.SysLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-11-20
 */
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements SysLogService {

	@Override
	public Page getLogByPage(Page page, SysLogDTO sysLog) {
		RoleService roleService = SpringUtil.getBean(RoleService.class);
		//当前登录人
		PigUser user = SecurityUtils.getUser();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(user.getId());
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				sysLog.setCreateBy(user.getUsername());
			}
		}

		if (StrUtil.isNotBlank(sysLog.getAppHandle())) {
			SysUserMapper sysUserMapper = SpringUtil.getBean(SysUserMapper.class);
			//当前应用下的用户
			List<String> userNameList = sysUserMapper.getUserNameByAppHandle(sysLog.getAppHandle());
			userNameList.add("");
			sysLog.setUserNameList(userNameList);
		}
		return baseMapper.selectPage(page, buildQuery(sysLog));
	}

	/**
	 * 插入日志
	 * @param sysLog 日志对象
	 * @return true/false
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean saveLog(SysLog sysLog) {
		baseMapper.insert(sysLog);
		return Boolean.TRUE;
	}

	/**
	 * 查询日志列表
	 * @param sysLog 查询条件
	 * @return List<SysLog>
	 */
	@Override
	public List<SysLog> getList(SysLogDTO sysLog) {
		RoleService roleService = SpringUtil.getBean(RoleService.class);
		//当前登录人
		PigUser user = SecurityUtils.getUser();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(user.getId());
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				sysLog.setCreateBy(user.getUsername());
			}
		}

		if (StrUtil.isNotBlank(sysLog.getAppHandle())) {
			SysUserMapper sysUserMapper = SpringUtil.getBean(SysUserMapper.class);
			//当前应用下的用户
			List<String> userNameList = sysUserMapper.getUserNameByAppHandle(sysLog.getAppHandle());
			userNameList.add("");
			sysLog.setUserNameList(userNameList);
		}
		return baseMapper.selectList(buildQuery(sysLog));
	}

	/**
	 * 构建查询条件
	 * @param sysLog 前端条件
	 * @return LambdaQueryWrapper
	 */
	private LambdaQueryWrapper buildQuery(SysLogDTO sysLog) {
		LambdaQueryWrapper<SysLog> wrapper = Wrappers.lambdaQuery();
		if (StrUtil.isNotBlank(sysLog.getLogType())) {
			wrapper.eq(SysLog::getLogType, sysLog.getLogType());
		}

		if (ArrayUtil.isNotEmpty(sysLog.getCreateTime())) {
			wrapper.ge(SysLog::getCreateTime, sysLog.getCreateTime()[0])
				.le(SysLog::getCreateTime, sysLog.getCreateTime()[1]);
		}

		if (StrUtil.isNotBlank(sysLog.getCreateBy())) {
			wrapper.eq(SysLog::getCreateBy, sysLog.getCreateBy());
		}

		if (sysLog.getUserNameList() != null && !sysLog.getUserNameList().isEmpty()) {
			wrapper.in(SysLog::getCreateBy, sysLog.getUserNameList());
		}

		wrapper.orderByDesc(SysLog::getCreateTime);

		return wrapper;
	}

}
