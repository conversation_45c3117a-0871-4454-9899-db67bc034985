<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.teleinfo</groupId>
        <artifactId>id-ds-service</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>id-ds-quartz</artifactId>
    <packaging>jar</packaging>

    <description>基于quartz后台定时任务模块</description>

    <dependencies>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--日志处理-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-log</artifactId>
        </dependency>
        <!--feign 处理-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-feign</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <!--数据库-->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!--swagger-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-swagger</artifactId>
        </dependency>
        <!--spring security 、oauth、jwt依赖-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-security</artifactId>
        </dependency>
        <!-- quartz 模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-jsqlparser-4.9</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>transmittable-thread-local</artifactId>
			<version>2.14.5</version>
		</dependency>
		<dependency>
			<groupId>cn.teleinfo</groupId>
			<artifactId>id-ds-upms-api</artifactId>
		</dependency>

		<dependency>
			<groupId>cn.teleinfo</groupId>
			<artifactId>id-ds-business-api</artifactId>
		</dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>boot</id>
        </profile>
        <profile>
            <id>cloud</id>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
	<build>
		<plugins>
			<plugin>
				<groupId>com.google.cloud.tools</groupId>
				<artifactId>jib-maven-plugin</artifactId>
				<configuration>
					<from>
						<image>harbor.idx.space/ops/openjdk:17.0-jdk</image>
						<!-- 指定所需系统架构 -->
						<platforms>
							<platform>
								<architecture>amd64</architecture>
								<os>linux</os>
							</platform>
							<platform>
								<architecture>arm64</architecture>
								<os>linux</os>
							</platform>
						</platforms>
					</from>
					<to>
						<image>harbor.idx.space/zadig/id-ds-quartz:latest</image>
					</to>
					<!--容器相关的属性-->
					<container>
						<!--jvm内存参数-->
						<environment>
							<JAVA_OPTS>-Xms1g -Xmx2g -Xss1024k</JAVA_OPTS>
						</environment>
						<entrypoint>
							<arg>/bin/bash</arg>
							<arg>-c</arg>
							<!--suppress UnresolvedMavenProperty -->
							<arg>java ${JAVA_OPTS} -Duser.timezone=PRC -cp /app/resources/:/app/classes/:/app/libs/* cn.teleinfo.ds.quartz.DsQuartzApplication
							</arg>
						</entrypoint>
						<ports>
							<port>2000</port>
						</ports>
						<creationTime>USE_CURRENT_TIMESTAMP</creationTime>
					</container>
					<allowInsecureRegistries>true</allowInsecureRegistries>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
