package cn.teleinfo.ds.quartz.framework.apilog.core.annotation;


import cn.teleinfo.ds.quartz.framework.apilog.core.enums.OperateTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * API 访问日志
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiAccessLog {

    /**
     * 操作类型
     */
    OperateTypeEnum operateType() default OperateTypeEnum.EXPORT;

} 