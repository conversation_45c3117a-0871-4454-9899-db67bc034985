package cn.teleinfo.ds.quartz.service;

import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.quartz.vo.JobLogPageReqVO;
import cn.teleinfo.ds.quartz.entity.JobLogDO;
import cn.teleinfo.ds.quartz.framework.common.pojo.PageResult;
import cn.teleinfo.ds.quartz.framework.quartz.core.service.JobLogFrameworkService;

/**
 * Job 日志 Service 接口
 *
 * <AUTHOR>
 */
public interface JobLogService extends JobLogFrameworkService {

    /**
     * 获得定时任务日志分页
     *
     * @param pageReqVO 分页查询
     * @return 定时任务日志分页
     */
	PageResponse<JobLogDO> getJobLogPage(JobLogPageReqVO pageReqVO);

    /**
     * 获得定时任务日志
     *
     * @param id 编号
     * @return 定时任务日志
     */
    JobLogDO getJobLog(Long id);

	/**
	 * 清理 exceedDay 天前的任务日志
	 *
	 * @param exceedDay   超过多少天就进行清理
	 * @param deleteLimit 清理的间隔条数
	 */
	Integer cleanJobLog(Integer exceedDay, Integer deleteLimit);

} 