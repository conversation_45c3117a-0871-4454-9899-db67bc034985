package cn.teleinfo.ds.quartz.entity;


import cn.teleinfo.ds.quartz.enums.JobLogStatusEnum;
import cn.teleinfo.ds.quartz.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 定时任务日志 DO
 *
 * <AUTHOR>
 */
@TableName("t_job_log")
@KeySequence("infra_job_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class JobLogDO extends BaseDO {

    /**
     * 日志编号
     */
	@TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 任务编号
     */
    private Long jobId;
    /**
     * 处理器的名字
     */
    private String handlerName;
    /**
     * 处理器的参数
     */
    private String handlerParam;
    /**
     * 第几次执行
     *
     * 用于：
     * 1. 重试时，记录重试次数
     * 2. 报警时，发送几次。
     */
    private Integer executeIndex;

    /**
     * 开始执行时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束执行时间
     */
    private LocalDateTime endTime;
    /**
     * 执行时长，单位：毫秒
     */
    private Integer duration;
    /**
     * 任务状态
     *
     * 枚举 {@link JobLogStatusEnum}
     */
    private Integer status;
    /**
     * 结果数据
     */
    private String result;

} 