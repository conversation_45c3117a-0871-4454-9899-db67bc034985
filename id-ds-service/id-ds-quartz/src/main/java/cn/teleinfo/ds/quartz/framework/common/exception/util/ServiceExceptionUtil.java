package cn.teleinfo.ds.quartz.framework.common.exception.util;


import cn.teleinfo.ds.common.core.exception.CheckedException;
import cn.teleinfo.ds.quartz.framework.common.exception.ErrorCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 异常工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ServiceExceptionUtil {

    public static CheckedException exception(ErrorCode errorCode) {
        return new CheckedException(errorCode.getMsg());
    }

    public static CheckedException exception0(Integer code, String message) {
        return new CheckedException(message);
    }

} 