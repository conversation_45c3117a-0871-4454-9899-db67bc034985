package cn.teleinfo.ds.quartz.mapper;


import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.quartz.vo.JobPageReqVO;
import cn.teleinfo.ds.quartz.entity.JobDO;
import cn.teleinfo.ds.quartz.framework.common.pojo.PageResult;
import cn.teleinfo.ds.quartz.framework.mybatis.core.mapper.BaseMapperX;
import cn.teleinfo.ds.quartz.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

/**
 * 定时任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JobMapper extends BaseMapperX<JobDO> {

	default JobDO selectByHandlerName(String handlerName) {
		return selectOne(JobDO::getHandlerName, handlerName);
	}

	default PageResponse<JobDO> selectPage(JobPageReqVO reqVO) {
		return selectPage(reqVO, new LambdaQueryWrapperX<JobDO>()
				.likeIfPresent(JobDO::getName, reqVO.getName())
				.eqIfPresent(JobDO::getStatus, reqVO.getStatus())
				.likeIfPresent(JobDO::getHandlerName, reqVO.getHandlerName())
				.orderByDesc(JobDO::getId));
	}

	default JobDO selectByIdentity(String identity) {
		return selectOne(JobDO::getIdentity, identity);
	}

} 