package cn.teleinfo.ds.quartz.framework.common.util.object;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.quartz.framework.common.pojo.PageResult;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Bean 工具类
 *
 * 1. 实现 Bean 之间的属性复制
 * 2. 实现 Bean 之间的方法复制
 */
public class BeanUtils {

    public static <T, U> U toBean(T source, Class<U> targetClass) {
        if (source == null) {
            return null;
        }
        return BeanUtil.toBean(source, targetClass);
    }

    public static <T, U> List<U> toBean(Collection<T> sourceList, Class<U> targetClass) {
        if (sourceList == null) {
            return null;
        }
        return sourceList.stream().map(s -> toBean(s, targetClass)).collect(Collectors.toList());
    }

    public static <T, U> PageResult<U> toBean(PageResult<T> source, Class<U> targetClass) {
        if (source == null) {
            return null;
        }
        return new PageResult<>(toBean(source.getList(), targetClass), source.getTotal());
    }

    /**
     * 获取对象的空属性
     *
     * @param source 源对象
     * @return 空属性的名称列表
     */
    public static String[] getNullPropertyNames(Object source) {
        BeanWrapper beanWrapper = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = beanWrapper.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = beanWrapper.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        return emptyNames.toArray(new String[0]);
    }

} 