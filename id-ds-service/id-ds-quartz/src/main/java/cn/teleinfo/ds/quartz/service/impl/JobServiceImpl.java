package cn.teleinfo.ds.quartz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.quartz.vo.JobPageReqVO;
import cn.teleinfo.ds.quartz.vo.JobSaveReqVO;
import cn.teleinfo.ds.quartz.entity.JobDO;
import cn.teleinfo.ds.quartz.mapper.JobMapper;
import cn.teleinfo.ds.quartz.enums.JobStatusEnum;
import cn.teleinfo.ds.quartz.framework.common.exception.util.ServiceExceptionUtil;
import cn.teleinfo.ds.quartz.framework.common.pojo.PageResult;
import cn.teleinfo.ds.quartz.framework.common.util.object.BeanUtils;
import cn.teleinfo.ds.quartz.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.teleinfo.ds.quartz.framework.quartz.core.scheduler.SchedulerManager;
import cn.teleinfo.ds.quartz.framework.quartz.core.util.CronUtils;
import cn.teleinfo.ds.quartz.service.JobService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static cn.teleinfo.ds.quartz.enums.ErrorCodeConstants.*;


/**
 * 定时任务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class JobServiceImpl implements JobService {

	@Resource
	private JobMapper jobMapper;

	@Resource
	private SchedulerManager schedulerManager;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long createJob(JobSaveReqVO createReqVO) throws SchedulerException {
		validateCronExpression(createReqVO.getCronExpression());

		if (StrUtil.isEmpty(createReqVO.getIdentity())) {
			createReqVO.setIdentity(createReqVO.getHandlerName());
		}

		// 校验唯一性
		if (jobMapper.selectByIdentity(createReqVO.getIdentity()) != null) {
			throw ServiceExceptionUtil.exception(JOB_HANDLER_EXISTS);
		}

		// 插入
		JobDO job = BeanUtils.toBean(createReqVO, JobDO.class);
		job.setStatus(JobStatusEnum.INIT.getStatus());
		jobMapper.insert(job);

		// 添加 Job 到 Quartz 中
		schedulerManager.addJob(job.getId(), job.getIdentity(), job.getHandlerName(), job.getHandlerParam(),
				job.getCronExpression(), createReqVO.getRetryCount(), createReqVO.getRetryInterval());
		// 更新
		JobDO updateObj = new JobDO().setId(job.getId()).setStatus(JobStatusEnum.NORMAL.getStatus());
		jobMapper.updateById(updateObj);

		// 返回
		return job.getId();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateJob(JobSaveReqVO updateReqVO) throws SchedulerException {
		validateCronExpression(updateReqVO.getCronExpression());
		// 校验存在
		JobDO job = validateJobExists(updateReqVO.getId());
		// 只有开启状态，才可以修改.原因是，如果出暂停状态，修改 Quartz Job 时，会导致任务又开始执行
		if (!job.getStatus().equals(JobStatusEnum.NORMAL.getStatus())) {
			throw ServiceExceptionUtil.exception(JOB_UPDATE_ONLY_NORMAL_STATUS);
		}
		// 更新
		JobDO updateObj = BeanUtils.toBean(updateReqVO, JobDO.class);
		jobMapper.updateById(updateObj);

		// 更新 Job 到 Quartz 中
		schedulerManager.updateJob(job.getIdentity(), job.getHandlerName(), updateReqVO.getHandlerParam(),
				updateReqVO.getCronExpression(), updateReqVO.getRetryCount(), updateReqVO.getRetryInterval());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateJobStatus(Long id, Integer status) throws SchedulerException {
		// 校验 status
		if (!Objects.equals(status, JobStatusEnum.NORMAL.getStatus())
				&& !Objects.equals(status, JobStatusEnum.STOP.getStatus())) {
			throw ServiceExceptionUtil.exception(JOB_CHANGE_STATUS_INVALID);
		}
		// 校验存在
		JobDO job = validateJobExists(id);
		// 校验是否已经为当前状态
		if (job.getStatus().equals(status)) {
			throw ServiceExceptionUtil.exception(JOB_CHANGE_STATUS_EQUALS);
		}
		// 更新 Job 状态
		JobDO updateObj = new JobDO().setId(id).setStatus(status);
		jobMapper.updateById(updateObj);

		// 更新状态 Job 到 Quartz 中
		if (Objects.equals(status, JobStatusEnum.NORMAL.getStatus())) { // 开启
			schedulerManager.resumeJob(job.getIdentity());
		} else { // 暂停
			schedulerManager.pauseJob(job.getIdentity());
		}
	}

	@Override
	public void triggerJob(Long id) throws SchedulerException {
		// 校验存在
		JobDO job = validateJobExists(id);
		// 触发 Quartz 中的 Job
		schedulerManager.triggerJob(job.getId(), job.getIdentity(), job.getHandlerName(), job.getHandlerParam());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteJob(Long id) throws SchedulerException {
		// 校验存在
		JobDO job = validateJobExists(id);
		// 更新
		jobMapper.deleteById(id);

		// 删除 Job 到 Quartz 中
		schedulerManager.deleteJob(job.getIdentity());
	}

	private JobDO validateJobExists(Long id) {
		JobDO job = jobMapper.selectById(id);
		if (job == null) {
			throw ServiceExceptionUtil.exception(JOB_NOT_EXISTS);
		}
		return job;
	}

	private void validateCronExpression(String cronExpression) {
		if (!CronUtils.isValid(cronExpression)) {
			throw ServiceExceptionUtil.exception(JOB_CRON_EXPRESSION_VALID);
		}
	}

	@Override
	public JobDO getJob(Long id) {
		return jobMapper.selectById(id);
	}

	@Override
	public List<JobDO> getJobList(Collection<Long> ids) {
		return jobMapper.selectBatchIds(ids);
	}

	@Override
	public PageResponse<JobDO> getJobPage(JobPageReqVO pageReqVO) {
		return jobMapper.selectPage(pageReqVO);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void syncJob() throws SchedulerException {
		// 1. 查询 Job 配置
		List<JobDO> jobList = jobMapper.selectList();

		// 2. 遍历处理
		for (JobDO job : jobList) {
			// 2.1 先删除，再创建
			schedulerManager.deleteJob(job.getIdentity());
			schedulerManager.addJob(job.getId(),job.getIdentity(), job.getHandlerName(), job.getHandlerParam(), job.getCronExpression(),
					job.getRetryCount(), job.getRetryInterval());
			// 2.2 如果 status 为暂停，则需要暂停
			if (Objects.equals(job.getStatus(), JobStatusEnum.STOP.getStatus())) {
				schedulerManager.pauseJob(job.getIdentity());
			}
			log.info("[syncJob][id({}) handlerName({}) 同步完成]", job.getId(), job.getHandlerName());
		}
	}

	@Override
	public List<JobDO> getJobsByStatus(Integer status) {
		return jobMapper.selectList(new LambdaQueryWrapperX<JobDO>()
				.eq(JobDO::getStatus, status));
	}

} 