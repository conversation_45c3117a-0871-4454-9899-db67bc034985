package cn.teleinfo.ds.quartz.framework.quartz.core.util;

import org.quartz.CronExpression;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Cron 表达式的工具类
 *
 * <AUTHOR>
 */
public class CronUtils {

    /**
     * 校验 CRON 表达式是否有效
     *
     * @param cronExpression CRON 表达式
     * @return 是否有效
     */
    public static boolean isValid(String cronExpression) {
        return CronExpression.isValidExpression(cronExpression);
    }

    /**
     * 基于 CRON 表达式，获得下 n 次的执行时间
     *
     * @param cronExpression CRON 表达式
     * @param n 数量
     * @return 执行时间
     */
    public static List<LocalDateTime> getNextTimes(String cronExpression, int n) {
        try {
            CronExpression cron = new CronExpression(cronExpression);
            List<LocalDateTime> times = new ArrayList<>();
            Date now = new Date();
            for (int i = 0; i < n; i++) {
                Date next = cron.getNextValidTimeAfter(now);
                if (next == null) { // 没有下一次执行时间
                    break;
                }
                times.add(LocalDateTime.ofInstant(next.toInstant(), ZoneId.systemDefault()));
                now = next;
            }
            return times;
        } catch (ParseException e) {
            throw new IllegalArgumentException(e);
        }
    }

} 