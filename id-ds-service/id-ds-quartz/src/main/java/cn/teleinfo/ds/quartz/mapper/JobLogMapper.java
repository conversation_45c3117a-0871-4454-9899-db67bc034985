package cn.teleinfo.ds.quartz.mapper;


import cn.teleinfo.ds.common.core.util.PageResponse;
import cn.teleinfo.ds.quartz.vo.JobLogPageReqVO;
import cn.teleinfo.ds.quartz.entity.JobLogDO;
import cn.teleinfo.ds.quartz.framework.common.pojo.PageResult;
import cn.teleinfo.ds.quartz.framework.mybatis.core.mapper.BaseMapperX;
import cn.teleinfo.ds.quartz.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * 任务日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JobLogMapper extends BaseMapperX<JobLogDO> {

    default PageResponse<JobLogDO> selectPage(JobLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<JobLogDO>()
                .eqIfPresent(JobLogDO::getJobId, reqVO.getJobId())
                .likeIfPresent(JobLogDO::getHandlerName, reqVO.getHandlerName())
                .betweenIfPresent(JobLogDO::getBeginTime, reqVO.getBeginTime(), reqVO.getEndTime())
                .eqIfPresent(JobLogDO::getStatus, reqVO.getStatus())
                .orderByDesc(JobLogDO::getId));
    }

    /**
     * 物理删除指定时间之前的日志
     *
     * @param createTime 最大时间
     * @param limit      删除条数，防止一次删除太多
     * @return 删除条数
     */
    @Delete("DELETE FROM t_job_log WHERE create_time < #{createTime} LIMIT #{limit}")
    Integer deleteByCreateTimeLt(@Param("createTime") LocalDateTime createTime, @Param("limit") Integer limit);

} 