package cn.teleinfo.ds.quartz.job;


import cn.teleinfo.ds.business.api.feign.BusinessService;
import cn.teleinfo.ds.quartz.framework.quartz.core.handler.JobHandler;
import cn.teleinfo.ds.common.core.util.R;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 同步数据Job
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SyncHandleJob implements JobHandler {

	@Resource
	private BusinessService businessService;


	@Override
	public String execute(String param) {
		R r = businessService.integratedHandles();
		if (r.getCode() == 200) {
			return "同步数据成功";
		} else {
			return "同步数据失败";
		}
	}

}
