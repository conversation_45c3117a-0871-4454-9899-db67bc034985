package cn.teleinfo.ds.quartz.framework.common.util.date;

import cn.hutool.core.date.LocalDateTimeUtil;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 时间工具类
 */
public class DateUtils {

    public static final String FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND = "yyyy-MM-dd HH:mm:ss";

    /**
     * 时间格式化
     *
     * @param time 需要格式化的时间
     * @param pattern 格式
     * @return 格式化后的字符串
     */
    public static String format(LocalDateTime time, String pattern) {
        if (time == null) {
            return null;
        }
        return time.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 计算两个时间之间的差值
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 两个时间之间的差值
     */
    public static Duration between(LocalDateTime startTime, LocalDateTime endTime) {
        return LocalDateTimeUtil.between(startTime, endTime);
    }

} 