package cn.teleinfo.ds.quartz.job;


import cn.teleinfo.ds.business.api.feign.BusinessService;
import cn.teleinfo.ds.common.core.util.R;
import cn.teleinfo.ds.quartz.framework.quartz.core.handler.JobHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 自动探测共享通道Job
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DetectShareChannelJob implements JobHandler {

	@Resource
	private BusinessService businessService;


	@Override
	public String execute(String param) {
		businessService.autoDetectShareChannel();
		return "开始探测任务";

	}

}
