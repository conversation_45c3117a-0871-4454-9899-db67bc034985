package cn.teleinfo.ds.quartz;

import cn.teleinfo.ds.common.feign.annotation.EnablePigFeignClients;
import cn.teleinfo.ds.common.security.annotation.EnablePigResourceServer;
import cn.teleinfo.ds.common.swagger.annotation.EnableDoc;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @date 2023-07-05
 */
@EnableDoc("job")
@EnablePigFeignClients(basePackages = {"cn.teleinfo.ds.*"})
@EnablePigResourceServer
@EnableDiscoveryClient
@SpringBootApplication
public class DsQuartzApplication {

	public static void main(String[] args) {
		SpringApplication.run(DsQuartzApplication.class, args);
	}

}
