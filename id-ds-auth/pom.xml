<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.teleinfo</groupId>
        <artifactId>id-data-share</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>id-ds-auth</artifactId>
    <packaging>jar</packaging>

    <description>id-data-share 认证授权中心，基于 spring security oAuth2</description>

    <dependencies>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--断路器依赖-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-feign</artifactId>
        </dependency>
        <!--upms api、model 模块-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-upms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <!--freemarker 授权码模式渲染-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!-- log -->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-log</artifactId>
        </dependency>
        <!-- 调用验证码核心模块 -->
        <dependency>
            <groupId>com.pig4cloud.plugin</groupId>
            <artifactId>captcha-core</artifactId>
            <version>${captcha.version}</version>
        </dependency>
        <!-- 加解密依赖 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>boot</id>
        </profile>
        <profile>
            <id>cloud</id>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

	<build>
		<plugins>
			<plugin>
				<groupId>com.google.cloud.tools</groupId>
				<artifactId>jib-maven-plugin</artifactId>
				<configuration>
					<from>
						<image>harbor.idx.space/ops/openjdk:17.0-jdk</image>
						<!-- 指定所需系统架构 -->
						<platforms>
							<platform>
								<architecture>amd64</architecture>
								<os>linux</os>
							</platform>
							<platform>
								<architecture>arm64</architecture>
								<os>linux</os>
							</platform>
						</platforms>
					</from>
					<to>
						<image>harbor.idx.space/zadig/id-ds-auth:latest</image>
					</to>
					<!--容器相关的属性-->
					<container>
						<!--jvm内存参数-->
						<environment>
							<JAVA_OPTS>-Xms1g -Xmx2g -Xss1024k</JAVA_OPTS>
						</environment>
						<entrypoint>
							<arg>/bin/bash</arg>
							<arg>-c</arg>
							<!--suppress UnresolvedMavenProperty -->
							<arg>java ${JAVA_OPTS} -Duser.timezone=PRC -cp /app/resources/:/app/classes/:/app/libs/* cn.teleinfo.ds.auth.DsAuthApplication
							</arg>
						</entrypoint>
						<ports>
							<port>3000</port>
						</ports>
						<creationTime>USE_CURRENT_TIMESTAMP</creationTime>
					</container>
					<allowInsecureRegistries>true</allowInsecureRegistries>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
